# Ignore Python cache and compiled files
__pycache__/
*.pyc
*.pyo
*.pyd

# Ignore git and VCS
.git/
.gitignore

# Ignore environment files
.env
.env.*

# Ignore logs
logs/
*.log

# Ignore editor/project files
.vscode/
.idea/
.DS_Store

# Ignore test files and output
*.out
*.test
*.tmp

# Ignore local cache
.cache/
.cache

# Ignore build artifacts
dist/
build/
.eggs/
*.egg-info/

# Ignore Jupyter checkpoints
.ipynb_checkpoints/

# Ignore Dockerfile and docker-compose override files (optional)
Dockerfile*
docker-compose* 