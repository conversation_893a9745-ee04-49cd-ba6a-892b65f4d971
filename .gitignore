# Byte-compiled / optimized / DLL files
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Installer logs
debug.log
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# mypy
.mypy_cache/
.dmypy.json

# Pyre type checker
.pyre/

# VS Code
.vscode/

# PyCharm
.idea/

# Logs
logs/

# Environment files
.env
.env.*

# Mac
.DS_Store

# Other
*.sqlite3
*.db

# Ignore local cache and data
*.cache/
cache/

# Ignore compiled Cython files
*.c
*.cpp
*.pyd
*.pyo
*.pdb

# Ignore test output
*.out
*.log

# Ignore coverage
.coverage

# Ignore wheel files
*.whl