# API Reference

This document lists all API endpoints, their input and output formats, and notes which APIs would need to be added or modified to support the new multi-user, job, and conversation-aware database schema.

---

## Table of Contents
- [User APIs](#user-apis)
- [Job Description APIs](#job-description-apis)
- [Notes APIs](#notes-apis)
- [SerpAPI Results APIs](#serpapi-results-apis)
- [RapidAPI Results APIs](#rapidapi-results-apis)
- [Conversation & Message APIs](#conversation--message-apis)
- [LinkedIn Profile Extraction APIs](#linkedin-profile-extraction-apis)
- [Other Utility APIs](#other-utility-apis)
- [APIs to Add/Modify for New Schema](#apis-to-addmodify-for-new-schema)

---

## User APIs

### Register User
- **POST** `/users/register`
- **Input:**
  ```json
  {
    "email": "<EMAIL>",
    "company_name": "Acme Corp",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "password": "..."
  }
  ```
- **Output:**
  ```json
  { "success": true, "user_id": "..." }
  ```

### Login
- **POST** `/users/login`
- **Input:**
  ```json
  { "email": "<EMAIL>", "password": "..." }
  ```
- **Output:**
  ```json
  { "success": true, "token": "...", "user": { ... } }
  ```

### Get User Profile
- **GET** `/users/me`
- **Headers:** `Authorization: Bearer <token>`
- **Output:**
  ```json
  { "user": { ... } }
  ```

---

## Job Description APIs

### Create Job Description
- **POST** `/jobs`
- **Input:**
  ```json
  {
    "job_title": "Software Engineer",
    "location": "Remote",
    "jd_text": "..."
  }
  ```
- **Output:**
  ```json
  { "success": true, "job_id": "..." }
  ```

### List Job Descriptions
- **GET** `/jobs`
- **Output:**
  ```json
  { "jobs": [ { ... }, ... ] }
  ```

### Get Job Description
- **GET** `/jobs/{job_id}`
- **Output:**
  ```json
  { "job": { ... } }
  ```

### Update Job Description
- **PUT** `/jobs/{job_id}`
- **Input:**
  ```json
  { "job_title": "...", "location": "...", "jd_text": "..." }
  ```
- **Output:**
  ```json
  { "success": true }
  ```

### Delete Job Description
- **DELETE** `/jobs/{job_id}`
- **Output:**
  ```json
  { "success": true }
  ```

---

## Notes APIs

### Add Note
- **POST** `/jobs/{job_id}/notes`
- **Input:**
  ```json
  { "note_text": "...", "note_type": "general" }
  ```
- **Output:**
  ```json
  { "success": true, "note_id": "..." }
  ```

### List Notes
- **GET** `/jobs/{job_id}/notes`
- **Output:**
  ```json
  { "notes": [ { ... }, ... ] }
  ```

---

## SerpAPI Results APIs

### Add SerpAPI Result
- **POST** `/jobs/{job_id}/serpapi-results`
- **Input:**
  ```json
  { "linkedin_url": "...", "search_query": "..." }
  ```
- **Output:**
  ```json
  { "success": true, "serpapi_result_id": "..." }
  ```

### List SerpAPI Results
- **GET** `/jobs/{job_id}/serpapi-results`
- **Output:**
  ```json
  { "results": [ { ... }, ... ] }
  ```

---

## RapidAPI Results APIs

### Add RapidAPI Result
- **POST** `/serpapi-results/{serpapi_result_id}/rapidapi-results`
- **Input:**
  ```json
  { "content_json": { ... }, "extracted_name": "...", ... }
  ```
- **Output:**
  ```json
  { "success": true, "rapidapi_result_id": "..." }
  ```

### List RapidAPI Results for a Job
- **GET** `/jobs/{job_id}/rapidapi-results`
- **Output:**
  ```json
  { "results": [ { ... }, ... ] }
  ```

---

## Conversation & Message APIs

### Start Conversation
- **POST** `/conversations`
- **Input:**
  ```json
  { "job_id": "...", "title": "..." }
  ```
- **Output:**
  ```json
  { "success": true, "conversation_id": "..." }
  ```

### List Conversations
- **GET** `/conversations`
- **Output:**
  ```json
  { "conversations": [ { ... }, ... ] }
  ```

### Get Conversation
- **GET** `/conversations/{conversation_id}`
- **Output:**
  ```json
  { "conversation": { ... } }
  ```

### Add Message
- **POST** `/conversations/{conversation_id}/messages`
- **Input:**
  ```json
  { "sender": "user", "content": "..." }
  ```
- **Output:**
  ```json
  { "success": true, "message_id": "..." }
  ```

### List Messages
- **GET** `/conversations/{conversation_id}/messages`
- **Output:**
  ```json
  { "messages": [ { ... }, ... ] }
  ```

---

## LinkedIn Profile Extraction APIs

### Single Profile Extraction
- **POST** `/rapidapi-linkedin/profile`
- **Input:**
  ```json
  { "linkedin_url": "..." }
  ```
- **Output:**
  ```json
  { "success": true, "data": { ... }, "message": "..." }
  ```

### Batch Profile Extraction
- **POST** `/rapidapi-linkedin/batch`
- **Input:**
  ```json
  { "linkedin_urls": [ "...", ... ], "max_workers": 3 }
  ```
- **Output:**
  ```json
  { "success": true, "profiles": [ { ... }, ... ], "total_processed": 2, ... }
  ```

### CSV Upload
- **POST** `/rapidapi-linkedin/upload-csv`
- **Form Data:**
  - `file`: CSV file
  - `output_dir`: Output directory (optional)
- **Output:**
  ```json
  { "success": true, "json_path": "...", "csv_path": "...", "total_profiles": 10, "successful_profiles": 8 }
  ```

---

## Other Utility APIs

### Health Check
- **GET** `/rapidapi-linkedin/health`
- **Output:**
  ```json
  { "status": "healthy", ... }
  ```

### Cache Stats
- **GET** `/rapidapi-linkedin/cache/stats`
- **Output:**
  ```json
  { "success": true, "total_cached_profiles": 10, ... }
  ```

### Clear Cache
- **DELETE** `/rapidapi-linkedin/cache/clear`
- **Output:**
  ```json
  { "success": true, "message": "..." }
  ```

---

# APIs to Add/Modify for New Schema

With the new database schema (multi-user, jobs, conversations, etc.), you should:

## **Add:**
- `/users/register`, `/users/login`, `/users/me` (if not present)
- `/jobs` endpoints (CRUD)
- `/jobs/{job_id}/notes` endpoints
- `/jobs/{job_id}/serpapi-results` endpoints
- `/jobs/{job_id}/rapidapi-results` endpoints
- `/conversations` endpoints (CRUD)
- `/conversations/{conversation_id}/messages` endpoints

## **Modify:**
- All endpoints to require authentication (JWT or session)
- All endpoints to scope data by `user_id` (multi-tenancy)
- All endpoints to use UUIDs for resource IDs
- `/rapidapi-linkedin/upload-csv` and related endpoints to associate results with the correct user/job
- Add pagination, filtering, and sorting to list endpoints

## **Deprecate/Remove:**
- Any endpoints that do not support multi-user or job context
- Any endpoints that use file-based storage only (move to DB-backed)

---

**This API reference is designed to be a living document. Update as you add new endpoints or change response formats!** 