# ChatAPI Debugging Guide

## 🚨 Current Issue Analysis

### Error Response
```json
{
    "workflow_state": {
        "job_id": null,
        "user_id": "784af227-217b-4518-b340-08c87ee53a24",
        "current_step": null,
        "status": "error",
        "last_updated": null
    },
    "result": null,
    "error": "Error processing job description: Failed to parse job description",
    "next_step": null,
    "guidance": "Workflow complete or awaiting clarification."
}
```

### Request Payload
```json
{
    "query": "parse jd: https://www.linkedin.com/jobs/view/4265286378/"
}
```

## 🔍 Root Cause Analysis

### 1. LinkedIn URL Scraping Issues

**Problem**: LinkedIn job URLs are notoriously difficult to scrape due to:
- Anti-bot protection
- Dynamic content loading
- Authentication requirements
- Rate limiting

**Evidence**: The error occurs specifically with LinkedIn URLs, and the scraping service likely returns empty or insufficient content.

### 2. Flow Breakdown

```mermaid
flowchart TD
    A[Request: parse jd: LinkedIn URL] --> B[Extract Intent: parse_jd_url]
    B --> C[Extract URL from query]
    C --> D[ScrapingService.scrape_job_description]
    D --> E{Content Scraped?}
    E -->|No| F[Create Fallback Data]
    E -->|Yes| G[Send to OpenAI]
    F --> H[Save Fallback to DB]
    G --> I{OpenAI Parse Success?}
    I -->|No| J[❌ ERROR: Failed to parse]
    I -->|Yes| K[Validate Fields]
    K --> L{Sufficient Data?}
    L -->|No| M[❌ ERROR: Insufficient data]
    L -->|Yes| N[✅ Success]
    
    style J fill:#ffebee
    style M fill:#ffebee
    style F fill:#fff3e0
```

## 🛠️ Immediate Fixes

### Fix 1: Enhanced LinkedIn Scraping

Add to `app/services/scraping_service.py`:

```python
async def scrape_linkedin_job(self, url: str) -> Dict[str, Any]:
    """Enhanced LinkedIn job scraping with multiple fallback methods"""
    
    # Method 1: Try with different user agents
    user_agents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    ]
    
    for user_agent in user_agents:
        try:
            result = await self._scrape_with_user_agent(url, user_agent)
            if result and result.get("description"):
                return result
        except Exception as e:
            logging.warning(f"User agent {user_agent} failed: {e}")
            continue
    
    # Method 2: Try with requests + BeautifulSoup
    try:
        return await self._scrape_with_requests_linkedin(url)
    except Exception as e:
        logging.warning(f"Requests method failed: {e}")
    
    # Method 3: Extract job ID and try alternative URL format
    try:
        job_id = self._extract_linkedin_job_id(url)
        if job_id:
            alt_url = f"https://www.linkedin.com/jobs-guest/jobs/api/jobPosting/{job_id}"
            return await self._scrape_linkedin_api(alt_url)
    except Exception as e:
        logging.warning(f"Alternative URL method failed: {e}")
    
    return None

def _extract_linkedin_job_id(self, url: str) -> str:
    """Extract job ID from LinkedIn URL"""
    import re
    match = re.search(r'/jobs/view/(\d+)', url)
    return match.group(1) if match else None

async def _scrape_linkedin_api(self, api_url: str) -> Dict[str, Any]:
    """Try to scrape from LinkedIn's guest API"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
        'Accept-Language': 'en-US,en;q=0.9'
    }
    
    response = requests.get(api_url, headers=headers, timeout=10)
    if response.status_code == 200:
        data = response.json()
        description = data.get('description', {}).get('text', '')
        if description:
            return {
                "source": "LinkedIn API",
                "description": description,
                "title": data.get('title', ''),
                "company": data.get('companyDetails', {}).get('company', {}).get('name', ''),
                "location": data.get('formattedLocation', '')
            }
    
    return None
```

### Fix 2: Improved Error Handling in ChatAPI

Update `app/routes/chat_api.py`:

```python
elif intent == "parse_jd_url":
    url = data.get("url")
    if not url:
        # Try to extract URL from the query string
        match = re.search(r'https?://[^\s]+', query)
        if match:
            url = match.group(0)
    if not url:
        raise HTTPException(status_code=400, detail="Missing job description URL.")
    
    scraping_service = ScrapingService()
    
    try:
        # 1. Try to scrape the job description from the URL
        scraped = await scraping_service.scrape_job_description(url)
        
        # Enhanced logging for debugging
        logging.info(f"Scraping attempt for URL: {url}")
        logging.info(f"Scraped result keys: {list(scraped.keys()) if scraped else 'None'}")
        
        # 2. Check if we got meaningful content
        jd_text = None
        if scraped:
            jd_text = (
                scraped.get("description") or
                scraped.get("full_text") or
                scraped.get("job_descriptions", [None])[0] or
                scraped.get("data", {}).get("description") or
                scraped.get("data", {}).get("full_text") or
                ""
            )
        
        # 3. If no content, try alternative methods
        if not jd_text or len(jd_text.strip()) < 100:
            logging.warning(f"Insufficient content from primary scraping. Trying alternatives...")
            
            # Try LinkedIn-specific scraping
            if "linkedin.com" in url:
                linkedin_result = await scraping_service.scrape_linkedin_job(url)
                if linkedin_result:
                    jd_text = linkedin_result.get("description", "")
            
            # If still no content, provide helpful error
            if not jd_text or len(jd_text.strip()) < 50:
                error_msg = "Unable to extract job description from the provided URL. This could be due to:"
                suggestions = [
                    "The URL requires login or authentication",
                    "The page uses dynamic content that's hard to scrape",
                    "The URL is not publicly accessible",
                    "Rate limiting or anti-bot protection"
                ]
                
                return JSONResponse({
                    "workflow_state": {
                        "job_id": None,
                        "user_id": str(user_id),
                        "current_step": "scraping_failed",
                        "status": "error",
                        "last_updated": datetime.utcnow().isoformat()
                    },
                    "error": error_msg,
                    "suggestions": suggestions,
                    "next_step": "try_manual_paste",
                    "guidance": "Please copy and paste the job description text directly using: 'parse jd: [job description text]'"
                })
        
        # 4. If we have content, proceed with parsing
        if jd_text:
            logging.info(f"Successfully extracted {len(jd_text)} characters from URL")
            parser = JobDescriptionService()
            
            try:
                normalized = await parser.parse_and_save_job_description(
                    db, jd_text, user_id, parse_method="url"
                )
                result = normalized
                
                # Get the new job_id from the DB
                from app.db.db_models import JobDescription
                new_jd = db.query(JobDescription).filter_by(user_id=user_id).order_by(
                    JobDescription.created_at.desc()
                ).first()
                job_id = str(new_jd.id)
                
            except Exception as parse_error:
                logging.error(f"OpenAI parsing failed: {parse_error}")
                
                # Create a basic job description entry for workflow continuity
                basic_jd = {
                    "Role": "Unknown Position",
                    "Company": "Unknown Company", 
                    "Location": "Unknown Location",
                    "Requirements": {
                        "Skills": [],
                        "Experience": "Not specified",
                        "Minimum Qualification": "Not specified",
                        "Preferred Qualification": "Not specified"
                    },
                    "Responsibilities": [],
                    "Benefits": [],
                    "SkillsPreference": [{"name": "", "status": "R"}],
                    "Suggested_Job_Titles": [],
                    "raw_content": jd_text[:1000]  # Store first 1000 chars for reference
                }
                
                # Save basic entry to database
                from app.db.db_models import JobDescription
                new_jd = JobDescription(
                    user_id=user_id,
                    job_title="Parsing Failed - Manual Review Required",
                    location="Unknown",
                    jd_text=basic_jd,
                    parse_method="url-failed"
                )
                db.add(new_jd)
                db.commit()
                db.refresh(new_jd)
                
                result = basic_jd
                job_id = str(new_jd.id)
                
                return JSONResponse({
                    "workflow_state": {
                        "job_id": job_id,
                        "user_id": str(user_id),
                        "current_step": "parsing_failed_but_saved",
                        "status": "warning",
                        "last_updated": datetime.utcnow().isoformat()
                    },
                    "result": result,
                    "error": f"Content extracted but parsing failed: {str(parse_error)}",
                    "next_step": "manual_review",
                    "guidance": "Job saved with basic info. Please review and edit the job details manually, then proceed with candidate search."
                })
        
    except Exception as scraping_error:
        logging.error(f"Complete scraping failure: {scraping_error}")
        
        return JSONResponse({
            "workflow_state": {
                "job_id": None,
                "user_id": str(user_id),
                "current_step": "scraping_failed",
                "status": "error",
                "last_updated": datetime.utcnow().isoformat()
            },
            "error": f"Failed to scrape job description: {str(scraping_error)}",
            "suggestions": [
                "Try copying the job description text directly",
                "Check if the URL is publicly accessible",
                "Use a different job posting URL"
            ],
            "next_step": "try_manual_paste",
            "guidance": "Please copy and paste the job description text directly"
        })
```

## 🧪 Testing & Debugging

### Test the Fix

1. **Test with the problematic URL**:
```bash
curl -X POST "http://localhost:8000/chatAPI" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"query": "parse jd: https://www.linkedin.com/jobs/view/4265286378/"}'
```

2. **Test with manual text**:
```bash
curl -X POST "http://localhost:8000/chatAPI" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{"query": "parse jd: Software Engineer position at TechCorp. Requirements: Python, FastAPI, 3+ years experience..."}'
```

### Debug Logging

Add this to your logging configuration to get more detailed debugging:

```python
# app/logger.py
import logging

# Set more verbose logging for debugging
logging.getLogger('app.services.scraping_service').setLevel(logging.DEBUG)
logging.getLogger('app.services.jd_service').setLevel(logging.DEBUG)
logging.getLogger('app.routes.chat_api').setLevel(logging.DEBUG)
```

## 📊 Monitoring & Metrics

Track these metrics to monitor the fix effectiveness:

1. **Scraping Success Rate**: % of URLs successfully scraped
2. **Parsing Success Rate**: % of scraped content successfully parsed
3. **Error Types**: Categorize errors (scraping vs parsing vs validation)
4. **Response Times**: Monitor performance impact

## 🔄 Alternative Approaches

If the above fixes don't work, consider:

1. **Manual Job Description Entry**: Provide a form for users to paste job descriptions directly
2. **Third-party Job APIs**: Integrate with job board APIs instead of scraping
3. **Browser Extension**: Create a browser extension to extract job descriptions
4. **Proxy Services**: Use rotating proxies for scraping

This comprehensive approach should resolve the "Failed to parse job description" error and provide better user experience with clear error messages and alternative paths.
