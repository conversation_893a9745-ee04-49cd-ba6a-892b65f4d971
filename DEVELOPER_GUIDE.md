# DashChat Developer Guide

## Overview

DashChat is a FastAPI-based application with background job processing (Celery), PostgreSQL database, and a modern Python/Conda environment.  
This guide covers setup, configuration, environment variables, dependency management, and operational best practices.

---

## 1. Quickstart

### **A. Conda Environment Setup**
```bash
conda env create -f environment.yml
conda activate dashchat_friend
pip install -r requirements.txt
```

### **B. Running the Application**
```bash
uvicorn app.main:app --reload
```

### **C. Running with Docker**
```bash
docker build -t dashchat .
docker run -p 8000:8000 dashchat
```
- The app will be available at [http://localhost:8000](http://localhost:8000).

---

## 2. Environment Variables & Configuration

DashChat uses environment variables for DB and service configuration.  
**Key variables (see `app/db/database.py`):**
- `DB_HOST`
- `DB_PORT`
- `DB_NAME`
- `DB_USER`
- `DB_PASSWORD`

**How to set:**  
- Use a `.env` file or export variables in your shell.
- Example `.env`:
  ```
  DB_HOST=your-db-host
  DB_PORT=5432
  DB_NAME=DashChatDb
  DB_USER=DashChat
  DB_PASSWORD=yourpassword
  ```

---

## 3. Dependency Management

### **A. Conda (Recommended)**
- All core dependencies are in `environment.yml`.
- Includes Python 3.11, numpy, pandas, scikit-learn, SQLAlchemy, FastAPI, Celery, and more.

### **B. Pip**
- Additional/overlapping dependencies in `requirements.txt`.
- Key libraries: `uvicorn`, `fastapi[all]`, `sqlalchemy`, `psycopg2-binary`, `celery[redis]`, `openai`, `torch`, `pdfplumber`, etc.

### **C. Redis**
- Required for Celery background jobs.
- Install and start Redis:
  ```bash
  sudo apt-get install redis-server
  sudo systemctl start redis-server
  sudo systemctl enable redis-server
  ```

---

## 4. Database

- Uses PostgreSQL (see `app/db/database.py`).
- SQLAlchemy is configured with connection pooling (`pool_size=10`, `max_overflow=20`).
- To initialize tables:
  ```bash
  python app/db/database.py
  ```

---

## 5. Background Jobs

- Celery is used for async/background processing.
- Start a worker:
  ```bash
  celery -A app.celery_worker.celery_app worker --loglevel=info
  ```
- Make sure Redis is running.

---

## 6. Logging & Monitoring

- Uvicorn and Celery logs are written to `/var/log/uvicorn/` and `/var/log/celery/` (in production).
- Use `journalctl` or check log files for debugging.

---

## 7. Production Deployment

- Use Docker or systemd services for reliability.
- Example systemd service files: `/etc/systemd/system/dashchat.service`, `/etc/systemd/system/celery-dashchat.service`.
- After editing service files:
  ```bash
  sudo systemctl daemon-reload
  sudo systemctl restart dashchat
  sudo systemctl restart celery
  ```

---

## 8. Coding & Contribution Guidelines

- Use context managers for DB sessions (`with SessionLocal() as db:`).
- All new dependencies should be added to both `environment.yml` and `requirements.txt`.
- Document new environment variables in this file.
- Use clear, descriptive commit messages.

---

## 9. Troubleshooting

- **DB Connection Pool Errors:**  
  Ensure sessions are always closed, and pool settings are appropriate for your workload.
- **Missing Dependencies:**  
  Re-run `conda env update -f environment.yml` and `pip install -r requirements.txt`.
- **Celery/Redis Issues:**  
  Make sure Redis is running and accessible.

---

## 10. Useful Commands

- **Start app:** `uvicorn app.main:app --reload`
- **Start Celery worker:** `celery -A app.celery_worker.celery_app worker --loglevel=info`
- **Build Docker image:** `docker build -t dashchat .`
- **Run Docker container:** `docker run -p 8000:8000 dashchat`



**Keep this documentation up to date as the project evolves!** 