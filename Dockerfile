# Use ARM64-compatible Debian base
FROM arm64v8/debian:bullseye-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        wget curl git bzip2 ca-certificates make gcc g++ \
        libc6-dev build-essential && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* /var/cache/apt/archives/*

# Install Miniforge (ARM64 Conda installer)
ENV CONDA_DIR=/opt/conda
ENV PATH=$CONDA_DIR/bin:$PATH

RUN wget https://github.com/conda-forge/miniforge/releases/latest/download/Miniforge3-Linux-aarch64.sh -O /tmp/miniforge.sh && \
    bash /tmp/miniforge.sh -b -p $CONDA_DIR && \
    rm /tmp/miniforge.sh && \
    conda clean -afy

# Copy files
COPY environment.yml .
COPY requirements.txt .
COPY app ./app
COPY app/depedencies.sh ./install_dependencies.sh

# Step 1: Create conda environment WITHOUT pip packages that fail build
RUN conda env create -f environment.yml

# Set shell to use conda env for all subsequent RUN instructions
SHELL ["conda", "run", "-n", "baba_dash", "/bin/bash", "-c"]

# Step 2: Install pip packages separately to prevent CondaEnvException
RUN pip install --upgrade pip setuptools wheel && \
    pip install -r requirements.txt && \
    pip install skillner==1.0.3

# Step 3: Install additional dependencies (spaCy models, ortools, etc.)
RUN bash ./install_dependencies.sh

# Set PATH for final container runtime
ENV PATH=/opt/conda/envs/baba_dash/bin:$PATH

# Expose FastAPI default port
EXPOSE 8000

# Start FastAPI using correct Python interpreter
CMD ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
