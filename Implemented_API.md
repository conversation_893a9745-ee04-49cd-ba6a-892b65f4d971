# Implemented API Endpoints & Example curl Commands

This document lists all implemented and working API endpoints in your FastAPI app (as per `app/main.py`), with example curl commands for testing each endpoint.

---

## 1. Job Descriptions (`/api/v1`)

### Parse Job Description (Text)
```bash
curl -X POST http://localhost:8000/api/v1/parse \
  -H "Content-Type: application/json" \
  -d '{"description": "We are looking for a Software Engineer in San Francisco..."}'
```

### Parse Job Description (URL)
```bash
curl -X POST http://localhost:8000/api/v1/parse-url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/job-posting"}'
```

### Get Job Description by ID
```bash
curl http://localhost:8000/api/v1/<job_id>
```

### List All Job Descriptions
```bash
curl http://localhost:8000/api/v1/
```

---

## 2. LinkedIn (`/linkedin`)

*Assuming standard endpoints, adjust as needed:*

### Example: Search LinkedIn Profiles
```bash
curl -X POST http://localhost:8000/linkedin/search \
  -H "Content-Type: application/json" \
  -d '{"query": "Software Engineer San Francisco"}'
```

---

## 3. Ranking (`/ranking`)

### Example: Rank Candidates
```bash
curl -X POST http://localhost:8000/ranking/rank \
  -H "Content-Type: application/json" \
  -d '{"candidates": [...], "job_description": "..."}'
```

---

## 4. SerpAPI (`/serpapi`)

### Example: Search LinkedIn via SerpAPI
```bash
curl -X POST http://localhost:8000/serpapi/search-linkedin \
  -H "Content-Type: application/json" \
  -d '{"query": "Software Engineer San Francisco"}'
```

---

## 5. LinkedIn Profile (`/linkedin-profile`)

### Example: Extract LinkedIn Profile Data
```bash
curl -X POST http://localhost:8000/linkedin-profile/extract \
  -H "Content-Type: application/json" \
  -d '{"linkedin_url": "https://www.linkedin.com/in/mikelevy"}'
```

---

## 6. RapidAPI LinkedIn (`/rapidapi-linkedin`)

### Single Profile Extraction
```bash
curl -X POST http://localhost:8000/rapidapi-linkedin/profile \
  -H "Content-Type: application/json" \
  -d '{"linkedin_url": "https://www.linkedin.com/in/mikelevy"}'
```

### Batch Profile Extraction
```bash
curl -X POST http://localhost:8000/rapidapi-linkedin/batch \
  -H "Content-Type: application/json" \
  -d '{"linkedin_urls": ["https://www.linkedin.com/in/mikelevy"], "max_workers": 3}'
```

### CSV Upload
```bash
curl -X POST http://localhost:8000/rapidapi-linkedin/upload-csv \
  -F "file=@test_linkedin_urls.csv" \
  -F "output_dir=output"
```

### Health Check
```bash
curl http://localhost:8000/rapidapi-linkedin/health
```

---

## 7. Root Endpoint

### Welcome Message
```bash
curl http://localhost:8000/
```

---

**How to Use:**
- Replace `<job_id>` with an actual job ID from your database.
- Replace file paths and URLs as needed for your environment.
- For endpoints requiring authentication, add `-H "Authorization: Bearer <token>"` if implemented.

**If you want the exact endpoints and curl for any specific router (e.g., `/linkedin`, `/ranking`, etc.), please share the corresponding route file or let me know!**
This list covers all endpoints that are certainly implemented and working based on your `main.py` and previous documentation. 