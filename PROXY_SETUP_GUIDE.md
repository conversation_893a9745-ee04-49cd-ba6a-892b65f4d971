# Proxy Setup Guide for ChatApp

## 🚨 Current Issue

Your scraping is being blocked by <PERSON><PERSON><PERSON><PERSON> because:
1. **IP Detection**: Your personal IP (**************) is being exposed
2. **Proxy Not Working**: The proxy configuration isn't being applied correctly
3. **Bot Detection**: <PERSON>flare is detecting automated requests

## 🔧 Solution Steps

### Step 1: Set Environment Variables

Create a `.env` file in your project root with proxy credentials:

```bash
# .env file
SCRAPER_PROXY_USER=your_proxy_username
SCRAPER_PROXY_PASS=your_proxy_password
SCRAPER_PROXY_HOST=brd.superproxy.io
SCRAPER_PROXY_PORT=9222
```

### Step 2: Verify Proxy Credentials

Check if your proxy service is working:

```bash
# Test with curl (if available)
curl -x http://username:<EMAIL>:9222 http://httpbin.org/ip

# Expected output should show a different IP than your personal IP
```

### Step 3: Alternative Proxy Services

If you don't have a proxy service, consider these options:

#### Free Proxy Lists (Less Reliable)
```bash
SCRAPER_PROXY_HOST=free-proxy-server.com
SCRAPER_PROXY_PORT=8080
# Note: Free proxies are often unreliable and blocked
```

#### Paid Proxy Services (Recommended)
- **Bright Data** (formerly Luminati): `brd.superproxy.io`
- **ProxyMesh**: `proxy.proxymesh.com`
- **Smartproxy**: `gate.smartproxy.com`
- **Oxylabs**: `pr.oxylabs.io`

### Step 4: Test Proxy Configuration

Run this simple test to verify your proxy:

```python
# test_proxy.py
import os
import requests

def test_proxy():
    proxy_user = os.getenv("SCRAPER_PROXY_USER")
    proxy_pass = os.getenv("SCRAPER_PROXY_PASS")
    proxy_host = os.getenv("SCRAPER_PROXY_HOST", "brd.superproxy.io")
    proxy_port = os.getenv("SCRAPER_PROXY_PORT", "9222")
    
    if not proxy_user or not proxy_pass:
        print("❌ Proxy credentials not set")
        return
    
    proxy_url = f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
    
    try:
        # Test direct connection
        direct = requests.get('http://httpbin.org/ip', timeout=10)
        direct_ip = direct.json().get('origin')
        print(f"Direct IP: {direct_ip}")
        
        # Test proxy connection
        proxies = {'http': proxy_url, 'https': proxy_url}
        proxy_resp = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
        proxy_ip = proxy_resp.json().get('origin')
        print(f"Proxy IP: {proxy_ip}")
        
        if direct_ip != proxy_ip:
            print("✅ Proxy is working!")
        else:
            print("❌ Proxy is not working - same IP")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_proxy()
```

### Step 5: Alternative Solutions

If proxy setup is difficult, try these alternatives:

#### Option A: Use Different Scraping Approach
```python
# In scraping_service.py, add this method
async def _scrape_with_requests_only(self, url: str):
    """Scrape using requests with rotating headers"""
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            # Extract content based on site structure
            return self._extract_content_from_soup(soup)
    except Exception as e:
        logging.error(f"Requests scraping failed: {e}")
        return None
```

#### Option B: Use LinkedIn API (If Available)
```python
# Use official LinkedIn API instead of scraping
# Requires LinkedIn API access and authentication
```

#### Option C: Manual Job Description Entry
```python
# Modify chat_api.py to handle manual text input better
# When scraping fails, prompt user to paste job description directly
```

## 🔍 Debugging Steps

### 1. Check Current IP
```bash
curl http://httpbin.org/ip
# Should show your current IP: **************
```

### 2. Check Environment Variables
```python
import os
print("Proxy User:", os.getenv("SCRAPER_PROXY_USER"))
print("Proxy Host:", os.getenv("SCRAPER_PROXY_HOST"))
print("Proxy Port:", os.getenv("SCRAPER_PROXY_PORT"))
```

### 3. Test WebDriver with Proxy
The updated scraping service now includes:
- ✅ Proxy connection testing before use
- ✅ IP verification for WebDriver
- ✅ Better error handling for blocked requests
- ✅ Validation of scraping results

## 🚀 Quick Fix

If you want to test immediately without proxy setup:

1. **Disable proxy temporarily** in `scraping_service.py`:
```python
# Comment out proxy setup in _setup_driver method
# Use basic Chrome options without proxy
```

2. **Use alternative URLs** for testing:
```python
# Test with non-LinkedIn URLs first
# Use job boards that don't have Cloudflare protection
```

3. **Implement manual fallback**:
```python
# When scraping fails, return a prompt for manual entry
# Guide user to copy-paste job description text
```

## 📞 Next Steps

1. **Set up proxy credentials** in environment variables
2. **Test proxy connection** using the test script
3. **Run the application** and check logs for IP verification
4. **Monitor scraping success rate** and adjust as needed

The enhanced scraping service will now:
- ✅ Verify proxy is working before scraping
- ✅ Show which IP is being used
- ✅ Detect when requests are being blocked
- ✅ Provide better error messages for debugging
