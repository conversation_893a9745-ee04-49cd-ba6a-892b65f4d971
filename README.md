# DashChat

## Quickstart / Setup

1. **Create the Conda environment from YAML:**
   ```bash
   conda env create -f environment.yml
   ```

2. **Activate the environment:**
   ```bash
   conda activate dashchat_friend
   ```

3. **Install any additional pip requirements:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application:**
   ```bash
   uvicorn app.main:app --reload
   ```

## Docker

You can also run DashChat using Docker. This is the recommended way for deployment or to avoid local dependency issues.

1. **Build the Docker image:**
   ```bash
   docker build -t dashchat .
   ```

2. **Run the Docker container:**
   ```bash
   docker run -p 8000:8000 dashchat
   ```

The app will be available at [http://localhost:8000](http://localhost:8000).

- The Dockerfile uses `environment.yml` to set up the Conda environment inside the container.
- All dependencies and spaCy models are installed automatically during the build.

## Production Deployment

### Background Job Processing

DashChat uses Celery for background job processing. The Celery worker runs as a systemd service for 24/7 operation.

### Logging and Monitoring

#### Application Logs
- **Uvicorn App Logs:**
  - Standard output: `/var/log/uvicorn/app.log`
  - Error logs: `/var/log/uvicorn/app.err`

#### Background Job Logs
- **Celery Worker Logs:**
  - Standard output: `/var/log/celery/worker.log`
  - Error logs: `/var/log/celery/worker.err`

#### Checking Service Status
```bash
# Check Celery worker status
sudo systemctl status celery

# Check Uvicorn app status
sudo systemctl status uvicorn

# View Celery logs
sudo journalctl -u celery -f

# View Uvicorn logs
sudo journalctl -u uvicorn -f
```

### Systemd Services

Both the FastAPI application and Celery worker are configured as systemd services for production deployment:

- **Celery Worker Service:** `/etc/systemd/system/celery.service`
- **Uvicorn App Service:** `/etc/systemd/system/dashchat.service`

These services automatically start on boot and restart on failure.

#### Viewing and Editing Service Files

To view or edit the service configurations:

```bash
# View/edit Celery worker service
sudo nano /etc/systemd/system/celery-dashchat.service

# View/edit DashChat app service  
sudo nano /etc/systemd/system/dashchat.service
```

After making changes to service files, reload systemd and restart the service:

```bash
sudo systemctl daemon-reload
sudo systemctl restart celery
sudo systemctl restart dashchat
```
