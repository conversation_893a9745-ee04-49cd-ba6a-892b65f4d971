# DashChat

## Quickstart / Setup

1. **Create the Conda environment from YAML:**
   ```bash
   conda env create -f environment.yml
   ```

2. **Activate the environment:**
   ```bash
   conda activate dashchat_friend
   ```

3. **Install any additional pip requirements:**
   ```bash
   pip install -r requirements.txt
   ```

4. **Run the application:**
   ```bash
   uvicorn app.main:app --reload
   ```

## Docker

You can also run DashChat using Docker. This is the recommended way for deployment or to avoid local dependency issues.

1. **Build the Docker image:**
   ```bash
   docker build -t dashchat .
   ```

2. **Run the Docker container:**
   ```bash
   docker run -p 8000:8000 dashchat
   ```

The app will be available at [http://localhost:8000](http://localhost:8000).

- The Dockerfile uses `environment.yml` to set up the Conda environment inside the container.
- All dependencies and spaCy models are installed automatically during the build.
