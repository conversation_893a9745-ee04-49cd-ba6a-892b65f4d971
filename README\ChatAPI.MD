### Improve /ChatAPI

**Current issues:**

- **Simple intent detection**: Based only on rigid keywords
- **No context distinction**: Can't differentiate between a company URL and a job description (JD) URL
- **Lack of edge case handling**: Multiple URLs, mixed content are not well supported
- **Fixed Regex patterns**: Not flexible with natural language

### TODO List – Improve Intent Detection for ChatAPI

**TODO 1: Create `SmartIntentDetector` class**

- Replace the simple `detect_parse_jd_intent()` function
- Integrate LLM to better understand natural language

\**TODO 2: Build URL Analysis System*C\*

- Classify URLs as: job posting, company page, or other
- Handle multiple URLs in a single query
- Prioritize the first URL if multiple job URLs exist

**TODO 3: Context-Aware Intent Detection**

- Use the current workflow step to improve accuracy
- Handle confidence scores for intent
- Add fallback mechanism when confidence is low

**TODO 4: Handle Mixed Content**

- Long query containing both JD text and company URL → parse text, ignore company URL
- Query with job URL + little text → prioritize URL scraping
- Query with multiple URLs → use only the first job URL

**TODO 5: Improve Text Extraction**

- Extract JD text from the query, remove URLs and commands
- Extract note text more flexibly (not just regex-based)
- Extract search parameters (job title, location) from natural language

**TODO 6: Smart URL Detection**

- Detect job URLs even when keywords like "JD", "job", or "URL" are not present
- Example: “https://linkedin.com/jobs/123 looks interesting” → parse_jd_url

**TODO 7: Enhanced Response Structure**

- Add confidence score in the response
- Provide suggestions when intent is unclear
- Log intent analysis for debugging

**TODO 8: Confidence-Based Flow**

- Ask for clarification when confidence < 0.6
- Provide multiple intent suggestions
- Implement graceful degradation

**TODO 9: Handle the following test cases:**

1. `"Here’s the JD: [long text] website: https://company.com"` → `parse_jd` (ignore company URL)
2. `"https://linkedin.com/jobs/123 looks interesting"` → `parse_jd_url`
3. `"Find software engineer in HCM"` → `search_candidates` (extract job_title + location)
4. `"Add note: this candidate seems suitable for the position"` → `add_note` (extract note text)
5. `"https://jobs.com/1 https://careers.com/2 which is better?"` → `parse_jd_url` (take only the first URL)

**TODO 10: Update main ChatAPI function**

- Replace simple intent detection with `SmartIntentDetector`
- Use `extracted_data` from intent analysis
- Add confidence checking logic
- Improve error messages with reasoning
