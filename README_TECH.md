# ChatApp Technical Documentation

## 🚀 Project Overview

ChatApp is a FastAPI-based application designed for job description parsing, candidate search, and ranking. It integrates with LinkedIn, Google Search (SerpAPI), and uses AI for intelligent candidate matching and ranking.

### Core Features

- **Job Description Parsing**: Extract structured data from job postings
- **Candidate Search**: Find candidates via LinkedIn and Google search
- **AI-Powered Ranking**: Rank candidates based on job requirements
- **User Authentication**: JWT-based authentication system
- **Multi-user Support**: Each user can manage their own jobs and candidates

## 🏗️ Project Architecture

```
app/
├── main.py              # FastAPI application entry point
├── auth/                # Authentication & authorization
├── db/                  # Database models and configuration
├── models/              # Pydantic schemas and data models
├── routes/              # API route handlers
├── services/            # Business logic layer
├── rank/                # Candidate ranking algorithms
└── logger.py            # Logging configuration
```

### Architecture Patterns

- **Layered Architecture**: Routes → Services → Database
- **Dependency Injection**: FastAPI's dependency system
- **Repository Pattern**: Database access through services
- **Schema Validation**: Pydantic models for request/response validation

## 🗄️ Database Schema

### Core Tables

#### Users Table

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    company_name VARCHAR(255),
    full_name VARCHAR(200),
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### Job Descriptions Table

```sql
CREATE TABLE job_descriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    job_title VARCHAR(255),
    location VARCHAR(255),
    jd_text JSON NOT NULL,
    parse_method VARCHAR(50),
    information_extracted_json JSON,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### Ranking Results Table

```sql
CREATE TABLE ranking_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID REFERENCES job_descriptions(id) ON DELETE CASCADE,
    linkedin_url TEXT,
    name VARCHAR(255),
    scores FLOAT,
    ranked_index INTEGER,
    status VARCHAR(50) DEFAULT 'pending',
    -- Scoring attributes
    tenure_discrete VARCHAR(50),
    company_discrete INTEGER,
    degree_major_discrete FLOAT,
    job_title_discrete FLOAT,
    skills_discrete FLOAT,
    university_discrete FLOAT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 Development Setup

### Prerequisites

- Python 3.11+
- PostgreSQL 12+
- Conda (recommended)

### Environment Setup

```bash
# 1. Create conda environment
conda env create -f environment.yml
conda activate dashchat_friend

# 2. Install dependencies
pip install -r requirements.txt

# 3. Set environment variables
export DB_HOST=your_db_host
export DB_NAME=your_db_name
export DB_USER=your_db_user
export DB_PASSWORD=your_db_password
export OPENAI_API_KEY=your_openai_key
export SECRET_KEY=your_jwt_secret

# 4. Run the application
uvicorn app.main:app --reload
```

### Docker Setup

```bash
docker build -t chatapp .
docker run -p 8000:8000 chatapp
```

## 📊 Database Operations

### Creating a New Table

1. **Define the SQLAlchemy Model** in `app/db/db_models.py`:

```python
class NewTable(Base):
    __tablename__ = 'new_table'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="new_tables")
```

2. **Add Relationship** to existing models if needed:

```python
# In User model
new_tables = relationship("NewTable", back_populates="user")
```

3. **Create Migration** (run after model changes):

```python
from app.db.database import create_db_and_tables
create_db_and_tables()
```

### Database Service Pattern

Create a service class for database operations:

```python
# app/services/new_service.py
from sqlalchemy.orm import Session
from app.db.db_models import NewTable
from typing import List, Optional

class NewService:
    def create_item(self, db: Session, name: str, user_id: str) -> NewTable:
        db_item = NewTable(name=name, user_id=user_id)
        db.add(db_item)
        db.commit()
        db.refresh(db_item)
        return db_item

    def get_item(self, db: Session, item_id: str) -> Optional[NewTable]:
        return db.query(NewTable).filter(NewTable.id == item_id).first()

    def get_items_by_user(self, db: Session, user_id: str) -> List[NewTable]:
        return db.query(NewTable).filter(NewTable.user_id == user_id).all()

    def update_item(self, db: Session, item_id: str, name: str) -> Optional[NewTable]:
        db_item = self.get_item(db, item_id)
        if db_item:
            db_item.name = name
            db.commit()
            db.refresh(db_item)
        return db_item

    def delete_item(self, db: Session, item_id: str) -> bool:
        db_item = self.get_item(db, item_id)
        if db_item:
            db.delete(db_item)
            db.commit()
            return True
        return False
```

## 🛣️ API Development

### Creating a New API Endpoint

1. **Define Pydantic Schemas** in `app/models/`:

```python
# app/models/new_schemas.py
from pydantic import BaseModel
from typing import Optional
from datetime import datetime

class NewItemCreate(BaseModel):
    name: str

class NewItemResponse(BaseModel):
    id: str
    name: str
    user_id: str
    created_at: datetime

    class Config:
        from_attributes = True
```

2. **Create Route Handler** in `app/routes/`:

```python
# app/routes/new_route.py
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.auth.security import get_current_user
from app.db.db_models import User
from app.services.new_service import NewService
from app.models.new_schemas import NewItemCreate, NewItemResponse
from typing import List

router = APIRouter(prefix="/api/v1/items", tags=["Items"])
service = NewService()

@router.post("/", response_model=NewItemResponse)
async def create_item(
    item: NewItemCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Create a new item."""
    try:
        result = service.create_item(db, item.name, current_user.id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[NewItemResponse])
async def get_items(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get all items for current user."""
    return service.get_items_by_user(db, current_user.id)

@router.get("/{item_id}", response_model=NewItemResponse)
async def get_item(
    item_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get specific item."""
    item = service.get_item(db, item_id)
    if not item or item.user_id != current_user.id:
        raise HTTPException(status_code=404, detail="Item not found")
    return item
```

3. **Register Router** in `app/main.py`:

```python
from app.routes import new_route

app.include_router(new_route.router)
```

### API Response Standards

All APIs should follow this response format:

```python
from fastapi.responses import JSONResponse

class StandardResponse(JSONResponse):
    def __init__(self, data=None, message="Success", status_code=200):
        content = {
            "data": data,
            "message": message,
            "statusCode": status_code
        }
        super().__init__(content=content, status_code=status_code)
```

## 🔐 Authentication System

### How Authentication Works

1. **User Registration**: Hash password, store user
2. **Login**: Verify credentials, return JWT token
3. **Protected Routes**: Validate JWT token via `get_current_user` dependency

### Adding Authentication to Routes

```python
from app.auth.security import get_current_user
from app.db.db_models import User

@router.get("/protected")
async def protected_route(current_user: User = Depends(get_current_user)):
    return {"user_id": current_user.id, "email": current_user.email}
```

### JWT Token Structure

```json
{
  "sub": "user_id_here",
  "exp": 1234567890
}
```

## 🎯 Ranking System

### Overview

The ranking system evaluates candidates based on multiple attributes:

- **Skills Match**: Technical and soft skills alignment
- **Job Title Relevance**: Current/previous role similarity
- **Company Experience**: Industry and company size match
- **Education**: Degree and university prestige
- **Tenure**: Work experience duration
- **University Ranking**: Educational institution quality

### Ranking Attributes

Each attribute is scored discretely and combined for final ranking:

```python
# app/rank/attributes/skills.py
class SkillsAttribute:
    def calculate_score(self, candidate_skills, job_requirements):
        # Fuzzy matching algorithm
        # Returns score between 0-1
        pass

# app/rank/attributes/job_title.py
class JobTitleAttribute:
    def calculate_score(self, candidate_title, job_title):
        # Semantic similarity using embeddings
        # Returns score between 0-1
        pass
```

### Adding New Ranking Attributes

1. **Create Attribute Class** in `app/rank/attributes/`:

```python
# app/rank/attributes/new_attribute.py
class NewAttribute:
    def __init__(self):
        self.weight = 0.1  # Attribute weight in final score

    def calculate_score(self, candidate_data, job_data):
        """
        Calculate score for this attribute
        Returns: float between 0-1
        """
        # Your scoring logic here
        return score

    def get_explanation(self, candidate_data, job_data):
        """
        Return human-readable explanation of the score
        """
        return "Explanation of why this score was given"
```

2. **Register in Ranking Service**:

```python
# app/services/ranking_service.py
from app.rank.attributes.new_attribute import NewAttribute

class RankingService:
    def __init__(self):
        self.attributes = {
            'skills': SkillsAttribute(),
            'job_title': JobTitleAttribute(),
            'new_attribute': NewAttribute(),  # Add here
        }
```

### Ranking Workflow

1. **Extract Job Requirements**: Parse job description for skills, title, etc.
2. **Process Candidate Data**: Extract relevant information from LinkedIn profiles
3. **Calculate Attribute Scores**: Score each attribute individually
4. **Combine Scores**: Weighted average of all attribute scores
5. **Rank Candidates**: Sort by final score

## 🔍 Services Layer

### Service Pattern

Services contain business logic and database operations. They should be:

- **Stateless**: No instance variables that change
- **Focused**: Single responsibility per service
- **Testable**: Easy to unit test
- **Reusable**: Can be used by multiple routes

### Key Services

#### JobDescriptionService

```python
# app/services/jd_service.py
class JobDescriptionService:
    async def parse_and_save_job_description(self, db, job_description_text, user_id, parse_method):
        # 1. Parse job description using OpenAI
        # 2. Extract structured information
        # 3. Save to database
        # 4. Return parsed result
        pass
```

#### LinkedInService

```python
# app/services/linkedin_service.py
class LinkedInService:
    async def search_candidates(self, job_requirements, location):
        # 1. Generate search queries
        # 2. Search via SerpAPI
        # 3. Extract LinkedIn URLs
        # 4. Return candidate list
        pass
```

#### RankingService

```python
# app/services/ranking_service.py
class RankingService:
    async def rank_candidates(self, job_id, candidates):
        # 1. Load job requirements
        # 2. Process candidate profiles
        # 3. Calculate ranking scores
        # 4. Save ranking results
        # 5. Return ranked list
        pass
```

## 📝 Models and Schemas

### Pydantic Models

Used for request/response validation and serialization:

```python
# app/models/schemas.py
from pydantic import BaseModel, Field, validator
from typing import List, Optional
from datetime import datetime

class JobDescriptionCreate(BaseModel):
    description: str = Field(..., min_length=10, description="Job description text")

    @validator('description')
    def validate_description(cls, v):
        if not v.strip():
            raise ValueError('Description cannot be empty')
        return v.strip()

class JobDescriptionResponse(BaseModel):
    id: str
    job_title: Optional[str]
    location: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True  # For SQLAlchemy model conversion
```

### Database Models

SQLAlchemy models for database tables:

```python
# app/db/db_models.py
from sqlalchemy import Column, String, Text, ForeignKey, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

class JobDescription(Base):
    __tablename__ = 'job_descriptions'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id', ondelete="CASCADE"))
    job_title = Column(String(255))
    jd_text = Column(JSON)
    created_at = Column(TIMESTAMP, server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="job_descriptions")
    ranking_results = relationship("RankingResult", cascade="all, delete-orphan")
```

## 🧪 Testing

### Test Structure

```
tests/
├── test_auth.py         # Authentication tests
├── test_jd_parser.py    # Job description parsing tests
├── test_linkedin.py     # LinkedIn integration tests
├── test_ranking.py      # Ranking algorithm tests
└── conftest.py          # Test configuration and fixtures
```

### Writing Tests

```python
# tests/test_jd_service.py
import pytest
from app.services.jd_service import JobDescriptionService
from app.db.database import get_db

@pytest.fixture
def jd_service():
    return JobDescriptionService()

@pytest.mark.asyncio
async def test_parse_job_description(jd_service, db_session):
    # Arrange
    job_text = "Software Engineer position requiring Python and FastAPI"
    user_id = "test-user-id"

    # Act
    result = await jd_service.parse_and_save_job_description(
        db=db_session,
        job_description_text=job_text,
        user_id=user_id,
        parse_method="raw-text"
    )

    # Assert
    assert result['job_title'] is not None
    assert 'Python' in result['skills']
```

### Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/test_jd_service.py

# Run with coverage
pytest --cov=app tests/
```

## 🔧 Configuration & Environment

### Environment Variables

```bash
# Database Configuration
DB_HOST=dashchat.cgiynjn2jmwt.us-west-2.rds.amazonaws.com
DB_PORT=5432
DB_NAME=DashChatDb
DB_USER=DashChat
DB_PASSWORD=your_password

# API Keys
OPENAI_API_KEY=your_openai_api_key
SERPAPI_KEY=your_serpapi_key
RAPIDAPI_KEY=your_rapidapi_key

# Security
SECRET_KEY=your_jwt_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=10080  # 7 days

# Application
DEBUG=False
LOG_LEVEL=INFO
```

### Configuration Management

```python
# app/config.py
import os
from pydantic import BaseSettings

class Settings(BaseSettings):
    db_host: str = os.getenv('DB_HOST', 'localhost')
    db_port: int = int(os.getenv('DB_PORT', 5432))
    db_name: str = os.getenv('DB_NAME', 'chatapp')
    db_user: str = os.getenv('DB_USER', 'postgres')
    db_password: str = os.getenv('DB_PASSWORD', '')

    openai_api_key: str = os.getenv('OPENAI_API_KEY', '')
    secret_key: str = os.getenv('SECRET_KEY', 'dev-secret')

    class Config:
        env_file = ".env"

settings = Settings()
```

## 📊 Logging

### Logging Configuration

```python
# app/logger.py
import logging
import os
from datetime import datetime

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging
log_filename = f"logs/{datetime.now().strftime('%m_%d_%Y_%H_%M_%S')}.log"

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_filename),
        logging.StreamHandler()  # Also log to console
    ]
)

logger = logging.getLogger(__name__)
```

### Using Logging in Services

```python
from app.logger import logging

class MyService:
    async def my_method(self):
        logging.info("Starting operation")
        try:
            # Your code here
            logging.info("Operation completed successfully")
        except Exception as e:
            logging.error(f"Operation failed: {str(e)}")
            raise
```

## 🚀 Deployment

### Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Considerations

- Use environment-specific configuration files
- Set up proper logging aggregation
- Configure database connection pooling
- Implement health checks
- Set up monitoring and alerting
- Use HTTPS in production
- Configure CORS properly for your frontend domain

## 🔍 Debugging & Troubleshooting

### Common Issues

#### Database Connection Issues

```python
# Check database connectivity
from app.db.database import engine
try:
    with engine.connect() as conn:
        result = conn.execute("SELECT 1")
        print("Database connection successful")
except Exception as e:
    print(f"Database connection failed: {e}")
```

#### Authentication Issues

```python
# Test JWT token generation
from app.auth.security import create_access_token
token = create_access_token(data={"sub": "test-user-id"})
print(f"Generated token: {token}")
```

#### API Testing

```bash
# Test authentication endpoint
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password"}'

# Test protected endpoint
curl -X GET "http://localhost:8000/api/v1/jd/" \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### Performance Monitoring

```python
# Add timing middleware
import time
from fastapi import Request

@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response
```

## 📚 Best Practices

### Code Organization

- Keep routes thin - move business logic to services
- Use dependency injection for database sessions and services
- Validate input data with Pydantic models
- Handle errors gracefully with proper HTTP status codes
- Use type hints throughout the codebase

### Database Best Practices

- Always use transactions for multi-step operations
- Use database indexes for frequently queried columns
- Implement soft deletes for important data
- Use UUIDs for primary keys to avoid enumeration attacks
- Implement proper foreign key constraints

### Security Best Practices

- Never store plain text passwords
- Use HTTPS in production
- Implement rate limiting
- Validate and sanitize all input data
- Use parameterized queries to prevent SQL injection
- Implement proper CORS configuration

### API Design Best Practices

- Use RESTful conventions
- Version your APIs (e.g., `/api/v1/`)
- Provide clear error messages
- Use appropriate HTTP status codes
- Implement pagination for list endpoints
- Document your APIs with OpenAPI/Swagger

## 🎯 Quick Start Checklist

For new developers joining the project:

1. **Environment Setup**

   - [ ] Install Python 3.11+
   - [ ] Install PostgreSQL
   - [ ] Clone the repository
   - [ ] Create conda environment
   - [ ] Install dependencies
   - [ ] Set up environment variables

2. **Database Setup**

   - [ ] Create database
   - [ ] Run migrations
   - [ ] Verify database connection

3. **Development**

   - [ ] Start the application
   - [ ] Test API endpoints
   - [ ] Run tests
   - [ ] Make your first code change

4. **Understanding the Codebase**
   - [ ] Read this documentation
   - [ ] Explore the project structure
   - [ ] Understand the authentication flow
   - [ ] Learn the ranking system
   - [ ] Review existing tests

## 📞 Support & Resources

- **API Documentation**: Available at `/docs` when running the application
- **Database Schema**: See `app/db/db_models.py`
- **API Reference**: See `API_REFERENCE.md`
- **Logs**: Check the `logs/` directory for application logs

---

_This documentation is maintained by the development team. Please keep it updated as the project evolves._
