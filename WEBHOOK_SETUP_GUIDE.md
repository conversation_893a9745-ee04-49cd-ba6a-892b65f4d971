# Payment Webhook Setup Guide

## 🎯 **Overview**

Webhook API được tạo để nhận thông báo từ hệ thống payment bên ngoài sau khi thanh toán thành công.

## 🔧 **API Endpoint**

```
POST /webhook/payment
```

### **Headers Required:**
```
X-API-Key: your-secret-webhook-key
Content-Type: application/json
```

### **Request Body:**
```json
{
  "status": "active",           // Required: active | inactive | upgrading
  "plan": "pro",               // Required: trial | starter | pro  
  "user_id": "uuid-string",    // Required: User ID trong hệ thống
  "user_email": "<EMAIL>",  // Optional: Email để verify
  "customer_id": "cust_123",   // Optional: Customer ID từ payment system
  "bill_id": "bill_456"        // Optional: Bill/Invoice ID
}
```

### **Response:**
```json
{
  "success": true,
  "message": "User updated successfully",
  "user_id": "uuid-string",
  "updated_fields": {
    "status": {"old": "inactive", "new": "active"},
    "plan": {"old": "trial", "new": "pro"}
  }
}
```

## 🔐 **Security Setup**

### **1. Set Environment Variable**

Thêm vào file `.env`:
```bash
WEBHOOK_API_KEY=your-super-secret-webhook-key-here
```

**Lưu ý:** Sử dụng key mạnh, ví dụ:
```bash
WEBHOOK_API_KEY=wh_live_sk_1234567890abcdef_secure_payment_webhook
```

### **2. Share Key với Payment System**

Cung cấp key này cho hệ thống payment để họ gửi trong header `X-API-Key`.

## 🧪 **Testing**

### **Test với curl:**

```bash
# Test thành công
curl -X POST "http://localhost:8000/webhook/payment" \
  -H "X-API-Key: your-super-secret-webhook-key-here" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active",
    "plan": "pro",
    "user_id": "existing-user-uuid",
    "user_email": "<EMAIL>",
    "customer_id": "cust_123",
    "bill_id": "bill_456"
  }'

# Test với key sai (sẽ trả về 401)
curl -X POST "http://localhost:8000/webhook/payment" \
  -H "X-API-Key: wrong-key" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active",
    "plan": "pro", 
    "user_id": "existing-user-uuid"
  }'

# Test không có key (sẽ trả về 401)
curl -X POST "http://localhost:8000/webhook/payment" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "active",
    "plan": "pro",
    "user_id": "existing-user-uuid"
  }'
```

## 📊 **Error Responses**

### **401 Unauthorized - Missing API Key:**
```json
{
  "detail": "Missing X-API-Key header"
}
```

### **401 Unauthorized - Invalid API Key:**
```json
{
  "detail": "Invalid API key"
}
```

### **400 Bad Request - User Not Found:**
```json
{
  "success": false,
  "message": "User not found",
  "user_id": "invalid-uuid",
  "updated_fields": {}
}
```

### **400 Bad Request - Email Mismatch:**
```json
{
  "success": false,
  "message": "User email mismatch",
  "user_id": "uuid-string",
  "updated_fields": {}
}
```

### **422 Validation Error - Invalid Data:**
```json
{
  "detail": [
    {
      "loc": ["body", "status"],
      "msg": "value is not a valid enumeration member; permitted: 'active', 'inactive', 'upgrading'",
      "type": "type_error.enum"
    }
  ]
}
```

## 🔍 **Monitoring & Logging**

Webhook sẽ log các thông tin sau:

```
INFO - Payment webhook received for user: uuid-string
INFO - New status: active, New plan: pro
INFO - Payment webhook processed successfully
INFO - Status: inactive -> active
INFO - Plan: trial -> pro
INFO - Customer ID: cust_123
INFO - Bill ID: bill_456
```

## 🚀 **Production Deployment**

### **1. Environment Variables**
```bash
# Production webhook key
WEBHOOK_API_KEY=wh_live_sk_production_key_here

# Database credentials
DB_HOST=your-production-db-host
DB_NAME=your-production-db-name
DB_USER=your-production-db-user
DB_PASSWORD=your-production-db-password
```

### **2. HTTPS Required**
Trong production, đảm bảo webhook endpoint được gọi qua HTTPS:
```
https://your-domain.com/webhook/payment
```

### **3. Rate Limiting**
Có thể thêm rate limiting để tránh spam:
```python
# Trong webhook.py
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@router.post("/payment")
@limiter.limit("10/minute")  # Giới hạn 10 requests/minute
def payment_webhook(...):
    ...
```

## 📞 **Integration với Payment Systems**

### **Stripe Example:**
```python
# Stripe webhook handler
@app.post("/stripe/webhook")
def stripe_webhook(request: Request):
    payload = request.body
    sig_header = request.headers.get('stripe-signature')
    
    # Verify Stripe signature
    event = stripe.Webhook.construct_event(payload, sig_header, stripe_webhook_secret)
    
    if event['type'] == 'checkout.session.completed':
        session = event['data']['object']
        
        # Call our webhook
        webhook_data = {
            "status": "active",
            "plan": session['metadata']['plan'],
            "user_id": session['metadata']['user_id'],
            "customer_id": session['customer'],
            "bill_id": session['id']
        }
        
        # Send to our webhook
        requests.post(
            "http://localhost:8000/webhook/payment",
            headers={"X-API-Key": "your-webhook-key"},
            json=webhook_data
        )
```

### **PayPal Example:**
```python
# PayPal webhook handler
@app.post("/paypal/webhook")
def paypal_webhook(request: Request):
    # Verify PayPal signature
    # ... PayPal verification logic ...
    
    if payment_status == "COMPLETED":
        webhook_data = {
            "status": "active",
            "plan": "pro",
            "user_id": custom_data['user_id'],
            "customer_id": payer_id,
            "bill_id": payment_id
        }
        
        # Send to our webhook
        requests.post(
            "http://localhost:8000/webhook/payment",
            headers={"X-API-Key": "your-webhook-key"},
            json=webhook_data
        )
```

## ✅ **Health Check**

Kiểm tra webhook service:
```bash
curl http://localhost:8000/webhook/health
```

Response:
```json
{
  "status": "healthy",
  "service": "webhook",
  "endpoints": [
    "POST /webhook/payment - Payment webhook"
  ]
}
```
