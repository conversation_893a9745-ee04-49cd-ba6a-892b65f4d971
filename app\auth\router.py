from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>Form
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.auth.schemas import UserCreate, UserPublic, Token, LoginRequest, UserUpdate
from app.services.user_service import UserService
from app.auth.security import verify_password, create_access_token, get_current_user
from fastapi.responses import JSONResponse
from typing import Any, Dict
import json
import uuid
from datetime import datetime
from app.services.master_service import sync_master_user

class EnhancedJSONResponse(JSONResponse):
    def render(self, content: Any) -> bytes:
        def default(obj):
            if isinstance(obj, uuid.UUID):
                return str(obj)
            if isinstance(obj, datetime):
                return obj.isoformat()
            return str(obj)
        return json.dumps(content, default=default).encode("utf-8")

class BaseResponse(EnhancedJSONResponse):
    def __init__(self, data: Any, message: str = "Success", status_code: int = 200):
        content = {"data": data, "message": message, "statusCode": status_code}
        super().__init__(content=content, status_code=status_code)

router = APIRouter(prefix="/auth", tags=["Authentication"])

user_service = UserService()

@router.post("/register", response_model=None, status_code=status.HTTP_201_CREATED)
def register_user(user: UserCreate, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    db_user = user_service.get_user_by_email(db, email=user.username)
    if db_user:
        return BaseResponse(
            data=None,
            message="Email already registered",
            status_code=400
        )
    db_user = user_service.create_user(db=db, user=user)
    user_data = {
        "id": db_user.id,
        "email": db_user.email,
        "company_name": db_user.company_name,
        "full_name": db_user.full_name,
        "credit": db_user.credit,
        "created_at": db_user.created_at,
        "status": db_user.status,
        "plan": db_user.plan,
    }
    background_tasks.add_task(
        sync_master_user,
        user_id=str(db_user.id), 
        email=db_user.email,
        company_name=db_user.company_name, 
        action="register"
    )
    return BaseResponse(data=user_data, message="User registered successfully", status_code=201)

@router.post("/login", response_model=None)
def login_for_access_token(
    login_req: LoginRequest, background_tasks: BackgroundTasks, db: Session = Depends(get_db)
):
    user = user_service.get_user_by_email(db, email=login_req.username)
    if not user or not verify_password(login_req.password, user.password_hash):
        return BaseResponse(
            data=None,
            message="Incorrect email or password",
            status_code=401
        )
    access_token = create_access_token(data={"sub": str(user.id)})
    login_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "email": user.email,
        "company_name": user.company_name,
        "full_name": user.full_name,
        "credit": getattr(user, "credit"),  # Add credit to response
        "status": user.status,
        "plan": user.plan
    }
    background_tasks.add_task(
        sync_master_user,
        user_id=str(user.id), 
        email=user.email,
        company_name=user.company_name, 
        action="login"
    )
    return BaseResponse(data=login_data, message="Login successful", status_code=200)

@router.get("/me", response_model=None)
def get_me(current_user = Depends(get_current_user)):
    user_data = {
        "id": current_user.id,
        "email": current_user.email,
        "company_name": current_user.company_name,
        "full_name": current_user.full_name,
        "credit": current_user.credit,
        "created_at": current_user.created_at,
        "status": current_user.status,
        "plan": current_user.plan,
    }
    return BaseResponse(data=user_data, message="User info fetched", status_code=200)



@router.put("/me", response_model=None)
def update_user_profile(
    user_update: UserUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """Update user profile (multiple fields)"""
    updated_user = user_service.update_user(db, str(current_user.id), user_update)
    if not updated_user:
        return BaseResponse(data=None, message="User not found", status_code=404)

    user_data = {
        "id": updated_user.id,
        "email": updated_user.email,
        "company_name": updated_user.company_name,
        "full_name": updated_user.full_name,
        "credit": updated_user.credit,
        "created_at": updated_user.created_at,
        "status": updated_user.status,
        "plan": updated_user.plan,
    }
    return BaseResponse(data=user_data, message="User profile updated successfully", status_code=200)
