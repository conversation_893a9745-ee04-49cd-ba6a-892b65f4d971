from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Literal
import uuid
from datetime import datetime

# --- User Schemas ---

class UserCreate(BaseModel):
    username: EmailStr
    password: str = Field(..., min_length=8)
    company_name: Optional[str] = None
    full_name: Optional[str] = None
    credit: Optional[int] = 3
    status: Optional[Literal['active', 'inactive', 'upgrading']] = 'active'
    plan: Optional[Literal['trial', 'starter', 'pro']] = 'trial'

class UserPublic(BaseModel):
    id: uuid.UUID
    email: EmailStr
    company_name: Optional[str] = None
    full_name: Optional[str] = None
    credit: int
    created_at: datetime
    status: str
    plan: str

    class Config:
        from_attributes = True

# --- Token Schemas ---

class Token(BaseModel):
    access_token: str
    token_type: str

class LoginRequest(BaseModel):
    username: EmailStr
    password: str

# --- User Update Schemas ---

class UserUpdate(BaseModel):
    status: Optional[Literal['active', 'inactive', 'upgrading']] = None
    plan: Optional[Literal['trial', 'starter', 'pro']] = None
    company_name: Optional[str] = None
    full_name: Optional[str] = None

# --- Payment Webhook Schemas ---

class PaymentWebhook(BaseModel):
    """Schema for payment webhook from external payment system"""
    status: Literal['active', 'inactive', 'upgrading']
    plan: Literal['trial', 'starter', 'pro']
    user_id: str = Field(..., description="User ID in our system")
    user_email: Optional[EmailStr] = Field(None, description="User email for verification")
    customer_id: Optional[str] = Field(None, description="Customer ID from payment system")
    bill_id: Optional[str] = Field(None, description="Bill/Invoice ID from payment system")

class PaymentWebhookResponse(BaseModel):
    """Response schema for payment webhook"""
    success: bool
    message: str
    user_id: str
    updated_fields: dict
