from pydantic import BaseModel, EmailStr, Field
from typing import Optional
import uuid
from datetime import datetime

# --- User Schemas ---

class UserCreate(BaseModel):
    username: EmailStr
    password: str = Field(..., min_length=8)
    company_name: Optional[str] = None
    full_name: Optional[str] = None

class UserPublic(BaseModel):
    id: uuid.UUID
    email: EmailStr
    company_name: Optional[str] = None
    full_name: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True

# --- Token Schemas ---

class Token(BaseModel):
    access_token: str
    token_type: str

class LoginRequest(BaseModel):
    username: EmailStr
    password: str
