from pydantic import BaseModel, EmailStr, Field
from typing import Optional, Literal
import uuid
from datetime import datetime

# --- User Schemas ---

class UserCreate(BaseModel):
    username: EmailStr
    password: str = Field(..., min_length=8)
    company_name: Optional[str] = None
    full_name: Optional[str] = None
    credit: Optional[int] = 3
    status: Optional[Literal['active', 'inactive', 'upgrading']] = 'active'
    plan: Optional[Literal['trial', 'starter', 'pro']] = 'trial'

class UserPublic(BaseModel):
    id: uuid.UUID
    email: EmailStr
    company_name: Optional[str] = None
    full_name: Optional[str] = None
    credit: int
    created_at: datetime
    status: str
    plan: str

    class Config:
        from_attributes = True

# --- Token Schemas ---

class Token(BaseModel):
    access_token: str
    token_type: str

class LoginRequest(BaseModel):
    username: EmailStr
    password: str

# --- User Update Schemas ---

class UserStatusUpdate(BaseModel):
    status: Literal['active', 'inactive', 'upgrading']

class UserPlanUpdate(BaseModel):
    plan: Literal['trial', 'starter', 'pro']

class UserUpdate(BaseModel):
    status: Optional[Literal['active', 'inactive', 'upgrading']] = None
    plan: Optional[Literal['trial', 'starter', 'pro']] = None
    company_name: Optional[str] = None
    full_name: Optional[str] = None
