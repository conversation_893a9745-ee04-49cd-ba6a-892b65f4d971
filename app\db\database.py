import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.db.db_models import Base

# Fetch database configuration from environment variables
DB_HOST = os.getenv('DB_HOST', 'dashchat.cgiynjn2jmwt.us-west-2.rds.amazonaws.com')
DB_PORT = os.getenv('DB_PORT', 5432)
DB_NAME = os.getenv('DB_NAME', 'DashChatDb')
DB_USER = os.getenv('DB_USER', 'DashChat')
DB_PASSWORD = os.getenv('DB_PASSWORD', 'TalentLabsAI<2026')

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """
    Dependency to get a database session.
    Ensures the session is always closed after the request.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def create_db_and_tables():
    """
    Creates all tables in the database that are defined in the Base metadata.
    This is useful for initializing the database.
    """
    Base.metadata.create_all(bind=engine)

if __name__ == "__main__":
    print("Creating database and tables...")
    create_db_and_tables()
    print("Database and tables created successfully.")
