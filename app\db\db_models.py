import uuid
from sqlalchemy import create_engine, Column, String, Text, ForeignKey, TIMESTAMP, JSON, Integer, UniqueConstraint, Float, Boolean, DateTime
from sqlalchemy.orm import relationship, declarative_base
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()

class User(Base):
    __tablename__ = 'users'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(Text, nullable=False)
    company_name = Column(String(255), nullable=True)
    full_name = Column(String(200), nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now())
    credit = Column(Integer, default=3, nullable=False)
    
    job_descriptions = relationship("JobDescription", back_populates="user", cascade="all, delete-orphan")
    workflow_states = relationship("JobWorkflowState", back_populates="user")

class JobDescription(Base):
    __tablename__ = 'job_descriptions'
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    job_title = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    jd_text = Column(JSON, nullable=False)
    parse_method = Column(String(50), nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now())
    information_extracted_json = Column(JSON, nullable=True)
    
    user = relationship("User", back_populates="job_descriptions")
    ranking_results = relationship("RankingResult", cascade="all, delete-orphan")
    workflow_state = relationship("JobWorkflowState", uselist=False, back_populates="job")

class RankingResult(Base):
    __tablename__ = 'ranking_results'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, nullable=False)
    job_id = Column(UUID(as_uuid=True), ForeignKey('job_descriptions.id', ondelete="CASCADE"), nullable=False)
    linkedin_url = Column(Text, nullable=True)
    name = Column(String(255), nullable=True)
    file_name = Column(String(255), nullable=True)
    scores = Column(Float, nullable=True)
    ranked_index = Column(Integer, nullable=True)
    status = Column(String(50), default="pending", nullable=True)
    tenure_discrete = Column(String(50), nullable=True)
    company_discrete = Column(Integer, nullable=True)
    degree_major_discrete = Column(Float, nullable=True)
    job_title_discrete = Column(Float, nullable=True)
    skills_discrete = Column(Float, nullable=True)
    university_discrete = Column(Float, nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now(), nullable=True)
    new_ranked_index = Column(Integer, nullable=True)
    candidate_note = Column(Text, default="", nullable=False)

class Note(Base):
    __tablename__ = 'notes'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('job_descriptions.id', ondelete="CASCADE"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id', ondelete="CASCADE"), nullable=False)
    note_text = Column(Text, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now())

    user = relationship("User")
    job = relationship("JobDescription")
    # Ensure only one note per (job_id, user_id)
    __table_args__ = (
        UniqueConstraint('job_id', 'user_id', name='uq_jobid_userid'),
    )

class SerpAPIResult(Base):
    __tablename__ = 'serpapi_results'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('job_descriptions.id', ondelete="CASCADE"), nullable=False)
    linkedin_url = Column(Text, nullable=False)
    search_rank = Column(Integer, nullable=True)
    search_query = Column(Text, nullable=True)
    source_platform = Column(String(50), default='linkedin')
    created_at = Column(TIMESTAMP, server_default=func.now())

    __table_args__ = (
        UniqueConstraint('job_id', 'linkedin_url', name='uq_jobid_linkedinurl'),
    )

class RapidAPIResult(Base):
    __tablename__ = 'rapidapi_results'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey('job_descriptions.id', ondelete="CASCADE"), nullable=False)
    serp_result_id = Column(UUID(as_uuid=True), ForeignKey('serpapi_results.id', ondelete="CASCADE"), nullable=False)
    content_json = Column(JSON, nullable=False)
    extracted_name = Column(String(255), nullable=True)
    extracted_position = Column(Text, nullable=True)
    created_at = Column(TIMESTAMP, server_default=func.now())

class JobWorkflowState(Base):
    __tablename__ = "job_workflow_state"
    job_id = Column(UUID(as_uuid=True), ForeignKey("job_descriptions.id", ondelete="CASCADE"), primary_key=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    job_description_parsed = Column(Boolean, default=False)
    notes_added = Column(Boolean, default=False)
    candidates_searched = Column(Boolean, default=False)
    batch_processed = Column(Boolean, default=False)
    ranking_done = Column(Boolean, default=False)
    current_step = Column(String(50), default="job_description_parsed")
    status = Column(String(20), default="in_progress")
    step_data = Column(JSON, nullable=True)
    last_updated = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships (optional, for easier joins)
    job = relationship("JobDescription", back_populates="workflow_state")
    user = relationship("User", back_populates="workflow_states")
