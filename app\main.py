from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routes import jd, linkedin, ranking, serpapi, rapidapi_linkedin, chat_api
from app.auth import router as auth_router
from app.routes import notes as notes_router
from app.logger import logging

# import os
# import nltk
# nltk.data.path.insert(0, "/home/<USER>/nltk_data")
# # nltk.data.path.insert(0, "/home/<USER>/miniconda3/envs/dashchat_friend/nltk_data")

# # Log NLTK data path and check for punkt resource
# logging.info(f"NLTK data path: {nltk.data.path}")
# resource_path = None
# for p in nltk.data.path:
#     candidate = os.path.join(p, "tokenizers", "punkt", "english.pickle")
#     logging.info(f"Checking for punkt resource at: {candidate}")
#     if os.path.exists(candidate):
#         logging.info(f"Found punkt resource at: {candidate}")
#         resource_path = candidate
#         break
# if not resource_path:
#     logging.error("Could not find punkt resource in any NLTK data path!")


app = FastAPI(
    title="Job Description Parser API",
    description="API for parsing and analyzing job descriptions",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    # allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth_router.router)
app.include_router(jd.router, tags=["Job Descriptions"])
app.include_router(linkedin.router, tags=["LinkedIn"])
app.include_router(ranking.router, tags=["Ranking"])
app.include_router(serpapi.router, tags=["SerpAPI"])
app.include_router(rapidapi_linkedin.router, tags=["RapidAPI LinkedIn"])
app.include_router(notes_router.router)
app.include_router(chat_api.router)

@app.on_event("startup")
async def startup_event():
    logging.info("Starting up the application")

@app.on_event("shutdown")
async def shutdown_event():
    logging.info("Shutting down the application")

@app.get("/")
async def root():
    logging.info("Root endpoint accessed")
    return {"message": "Welcome to the Job Description Parser API"} 