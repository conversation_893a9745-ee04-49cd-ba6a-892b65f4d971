from app.logger import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, ConfigDict, HttpUrl, Field



class Requirements(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    experience: str = Field(default="N/A")
    minimum_qualification: str = Field(default="N/A")
    preferred_qualification: str = Field(default="N/A")
    skills: List[str] = Field(default_factory=list)

    model_config = {
        "json_schema_extra": {
            "example": {
                "experience": "5+ years",
                "minimum_qualification": "Bachelor's degree",
                "preferred_qualification": "Master's degree",
                "skills": ["Python", "AWS", "Docker"]
            }
        }
    }

class SkillPreference(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    name: str
    status: str = "R"  # R for Required

    model_config = {
        "json_schema_extra": {
            "example": {
                "name": "Python",
                "status": "R"
            }
        }
    }

class JobDescriptionData(BaseModel):
    model_config = ConfigDict(extra='allow')
    
    benefits: List[str] = Field(default_factory=list)
    company: str = Field(default="N/A")
    location: str = Field(default="N/A")
    requirements: Requirements = Field(default_factory=Requirements)
    responsibilities: List[str] = Field(default_factory=list)
    role: str = Field(default="N/A")
    skills_preference: List[SkillPreference] = Field(default_factory=list)
    suggested_job_titles: List[str] = Field(default_factory=list)

    model_config = {
        "json_schema_extra": {
            "example": {
                "benefits": ["Health insurance", "401k"],
                "company": "Example Corp",
                "location": "San Francisco, CA",
                "requirements": {
                    "experience": "5+ years",
                    "minimum_qualification": "Bachelor's degree",
                    "preferred_qualification": "Master's degree",
                    "skills": ["Python", "AWS", "Docker"]
                },
                "responsibilities": ["Design systems", "Write code"],
                "role": "Senior Software Engineer",
                "skills_preference": [
                    {"name": "Python", "status": "R"},
                    {"name": "AWS", "status": "R"}
                ],
                "suggested_job_titles": ["Software Engineer", "Backend Developer"]
            }
        }
    }

class JobDescriptionResponse(BaseModel):
    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat()
        }
    )
    
    id: str
    created_at: datetime
    data: JobDescriptionData

    def dict(self, *args, **kwargs) -> Dict[str, Any]:
        """Override dict method to return the data field directly."""
        return self.data.dict(*args, **kwargs)

    model_config = {
        "json_schema_extra": {
            "example": {
                "id": "123e4567-e89b-12d3-a456-426614174000",
                "created_at": "2024-06-17T04:15:17.609Z",
                "data": {
                    "benefits": ["Health insurance", "401k"],
                    "company": "Example Corp",
                    "location": "San Francisco, CA",
                    "requirements": {
                        "experience": "5+ years",
                        "minimum_qualification": "Bachelor's degree",
                        "preferred_qualification": "Master's degree",
                        "skills": ["Python", "AWS", "Docker"]
                    },
                    "responsibilities": ["Design systems", "Write code"],
                    "role": "Senior Software Engineer",
                    "skills_preference": [
                        {"name": "Python", "status": "R"},
                        {"name": "AWS", "status": "R"}
                    ],
                    "suggested_job_titles": ["Software Engineer", "Backend Developer"]
                }
            }
        }
    }

class JobDescriptionURL(BaseModel):
    url: str 

    model_config = {
        "json_schema_extra": {
            "example": {
                "url": "https://jobs.apple.com/en-us/details/200609371/senior-software-engineer?team=SFTWR"
            }
        }
    } 