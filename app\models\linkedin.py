from app.logger import logging
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, HttpUrl



class LinkedInJob(BaseModel):
    """Model for LinkedIn job posting data."""
    title: str
    company: str
    location: str
    description: str
    url: HttpUrl
    job_requirements: List[str] = []
    experience_level: Optional[str] = None
    employment_type: Optional[str] = None
    salary_range: Optional[str] = None
    benefits: List[str] = []
    application_deadline: Optional[str] = None
    contact_information: Optional[str] = None
    additional_notes: Optional[str] = None

class LinkedInJobResponse(LinkedInJob):
    """Model for LinkedIn job posting response."""
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True 