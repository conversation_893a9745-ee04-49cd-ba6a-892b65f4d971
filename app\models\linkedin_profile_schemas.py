from pydantic import BaseModel, HttpUrl, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class ExperienceItem(BaseModel):
    """Model for work experience items."""
    company: Optional[str] = None
    position: Optional[str] = None
    duration: Optional[str] = None

class LinkedInProfileData(BaseModel):
    """Model for LinkedIn profile data."""
    url: str
    name: Optional[str] = None
    headline: Optional[str] = None
    location: Optional[str] = None
    company: Optional[str] = None
    education: Optional[str] = None
    skills: List[str] = Field(default_factory=list)
    experience: List[ExperienceItem] = Field(default_factory=list)
    extraction_time: Optional[float] = None

class ProfileDetailsRequest(BaseModel):
    """Request model for getting profile details."""
    linkedin_url: HttpUrl = Field(..., description="LinkedIn profile URL")
    
class ProfileDetailsResponse(BaseModel):
    """Response model for profile details."""
    success: bool
    data: Optional[LinkedInProfileData] = None
    error: Optional[str] = None
    message: Optional[str] = None

class MultipleProfilesRequest(BaseModel):
    """Request model for getting multiple profile details."""
    linkedin_urls: List[HttpUrl] = Field(..., description="List of LinkedIn profile URLs", min_items=1, max_items=10)
    max_concurrent: Optional[int] = Field(default=3, ge=1, le=5, description="Maximum concurrent requests")

class MultipleProfilesResponse(BaseModel):
    """Response model for multiple profile details."""
    success: bool
    profiles: List[ProfileDetailsResponse] = Field(default_factory=list)
    total_processed: int
    successful_count: int
    failed_count: int

class LocationVerificationRequest(BaseModel):
    """Request model for location verification."""
    linkedin_url: HttpUrl = Field(..., description="LinkedIn profile URL")
    expected_location: str = Field(..., description="Expected location to verify", min_length=1)

class LocationVerificationResponse(BaseModel):
    """Response model for location verification."""
    success: bool
    url: str
    verification_success: bool
    location_match: Optional[bool] = None
    expected_location: str
    actual_location: Optional[str] = None
    profile_data: Optional[LinkedInProfileData] = None
    error: Optional[str] = None

class CacheStatsResponse(BaseModel):
    """Response model for cache statistics."""
    success: bool
    total_cached_profiles: int
    cache_directory: str
    total_cache_size_bytes: int
    total_cache_size_mb: float
    error: Optional[str] = None

class ClearCacheResponse(BaseModel):
    """Response model for clearing cache."""
    success: bool
    message: str
    cleared_count: int
    error: Optional[str] = None

class ProfileExtractionResult(BaseModel):
    """Model for profile extraction results with metadata."""
    profile_data: LinkedInProfileData
    extraction_duration: float
    cache_hit: bool
    timestamp: datetime = Field(default_factory=datetime.now)

class BatchProcessingResult(BaseModel):
    """Model for batch processing results."""
    total_urls: int
    successful_extractions: int
    failed_extractions: int
    total_duration: float
    average_duration_per_profile: float
    results: List[ProfileExtractionResult]
    errors: List[Dict[str, Any]] = Field(default_factory=list) 