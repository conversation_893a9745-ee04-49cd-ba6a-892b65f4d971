from pydantic import BaseModel
from uuid import UUID
from datetime import datetime
from typing import List, Optional

class NoteCreate(BaseModel):
    job_id: UUID
    note_text: str
    job_title: Optional[str] = None
    location: Optional[str] = None

class NoteResponse(BaseModel):
    id: UUID
    job_id: UUID
    user_id: UUID
    note_text: str
    created_at: datetime
    missing_fields: Optional[List[str]] = None

    class Config:
        from_attributes = True 