from app.logger import logging
from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel



class Candidate(BaseModel):
    """Model for candidate data."""
    id: str
    name: str
    skills: List[str]
    experience: str
    education: str
    score: Optional[float] = None
    match_percentage: Optional[float] = None
    notes: Optional[str] = None

class RankingRequest(BaseModel):
    """Model for ranking request data."""
    job_id: str
    candidates: List[Candidate]

class RankingResponse(BaseModel):
    """Model for ranking response data."""
    job_id: str
    rankings: List[Candidate]
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True 