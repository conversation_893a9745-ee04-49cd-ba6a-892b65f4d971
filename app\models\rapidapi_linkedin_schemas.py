from pydantic import BaseModel, Field, HttpUrl
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID

class ContactInfo(BaseModel):
    """Model for contact information."""
    linkedin: str = Field(default="", description="LinkedIn profile URL")
    location: str = Field(default="", description="Location information")

class University(BaseModel):
    """Model for university/education information."""
    name: str = Field(default="", description="University name")
    location: str = Field(default="", description="University location")
    major: str = Field(default="", description="Field of study")
    degree: str = Field(default="", description="Degree type")
    tenure: str = Field(default="", description="Duration of study")

class Experience(BaseModel):
    """Model for work experience."""
    company: str = Field(default="", description="Company name")
    location: str = Field(default="", description="Work location")
    tenure: str = Field(default="", description="Duration of employment")
    position: str = Field(default="", description="Job title")

class LinkedInProfileData(BaseModel):
    """Model for LinkedIn profile data."""
    name: str = Field(default="", description="Full name")
    contact: ContactInfo = Field(default_factory=ContactInfo)
    universities: List[University] = Field(default_factory=list)
    experiences: List[Experience] = Field(default_factory=list)
    skills: List[str] = Field(default_factory=list)
    file_name: str = Field(default="", description="Profile identifier")
    error: Optional[str] = Field(default=None, description="Error message if any")

class SingleProfileRequest(BaseModel):
    """Request model for fetching a single LinkedIn profile."""
    linkedin_url: HttpUrl = Field(..., description="LinkedIn profile URL")

class SingleProfileResponse(BaseModel):
    """Response model for single profile fetch."""
    success: bool
    data: Optional[LinkedInProfileData] = None
    error: Optional[str] = None
    message: Optional[str] = None

class BatchProfilesRequest(BaseModel):
    """Request model for batch processing LinkedIn profiles."""
    linkedin_urls: List[HttpUrl] = Field(..., description="List of LinkedIn profile URLs", min_items=1, max_items=100)
    max_workers: Optional[int] = Field(default=5, ge=1, le=10, description="Maximum concurrent workers")

class BatchProfilesResponse(BaseModel):
    """Response model for batch profile processing."""
    success: bool
    profiles: List[LinkedInProfileData] = Field(default_factory=list)
    total_processed: int
    successful_count: int
    failed_count: int
    error: Optional[str] = None

class CSVProcessingRequest(BaseModel):
    """Request model for CSV file processing."""
    csv_file_path: str = Field(..., description="Path to CSV file containing LinkedIn URLs")
    output_dir: Optional[str] = Field(default="output", description="Output directory for results")

class CSVProcessingResponse(BaseModel):
    """Response model for CSV processing."""
    success: bool
    json_path: Optional[str] = None
    csv_path: Optional[str] = None
    total_profiles: int
    successful_profiles: int
    error: Optional[str] = None

class CacheStatsResponse(BaseModel):
    """Response model for cache statistics."""
    success: bool
    total_cached_profiles: int
    cache_directory: str
    total_cache_size_bytes: int
    total_cache_size_mb: float
    error: Optional[str] = None

class ClearCacheResponse(BaseModel):
    """Response model for clearing cache."""
    success: bool
    message: str
    cleared_count: int
    error: Optional[str] = None

class ProfileExtractionResult(BaseModel):
    """Model for profile extraction results with metadata."""
    profile_data: LinkedInProfileData
    extraction_duration: float
    cache_hit: bool
    timestamp: datetime = Field(default_factory=datetime.now)

class BatchProcessingResult(BaseModel):
    """Model for batch processing results."""
    total_urls: int
    successful_extractions: int
    failed_extractions: int
    total_duration: float
    average_duration_per_profile: float
    results: List[ProfileExtractionResult]
    errors: List[Dict[str, Any]] = Field(default_factory=list)

class RapidAPIConfig(BaseModel):
    """Model for RapidAPI configuration."""
    api_key: str = Field(..., description="RapidAPI key")
    api_host: str = Field(default="linkedin-api8.p.rapidapi.com", description="API host")
    api_url: str = Field(default="https://linkedin-api8.p.rapidapi.com/get-profile-data-by-url", description="API endpoint")
    max_retries: int = Field(default=3, ge=1, le=10, description="Maximum retry attempts")
    timeout: int = Field(default=15, ge=5, le=60, description="Request timeout in seconds") 

class BatchCandidatesRequest(BaseModel):
    job_id: UUID

class BatchCandidatesResponse(BaseModel):
    success: bool
    user_id: Optional[UUID] = None
    job_id: Optional[UUID] = None
    message: Optional[str] = None
    candidates_processed: Optional[int] = None
    error: Optional[str] = None