from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import datetime

class JobDescription(BaseModel):
    title: str
    description: str
    location: Optional[str] = None
    company: Optional[str] = None
    requirements: Optional[List[str]] = None

class JobDescriptionResponse(JobDescription):
    id: str
    created_at: datetime
    skills: Optional[List[str]] = []
    experience_level: Optional[str] = None
    employment_type: Optional[str] = None

class LinkedInProfile(BaseModel):
    url: str
    name: str
    title: str
    location: Optional[str] = None
    experience: Optional[List[dict]] = None
    education: Optional[List[dict]] = None
    skills: Optional[List[str]] = None

class CandidateRanking(BaseModel):
    profile: LinkedInProfile
    score: float
    match_reasons: List[str]
    missing_skills: Optional[List[str]] = None

class SearchResponse(BaseModel):
    query: str
    results: List[LinkedInProfile]
    total_results: int
    page: int
    total_pages: int

class ErrorResponse(BaseModel):
    error: str
    details: Optional[str] = None 