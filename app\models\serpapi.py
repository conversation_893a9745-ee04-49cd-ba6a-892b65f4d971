from pydantic import BaseModel, Field, ConfigDict
from typing import List, Optional
from datetime import datetime
from uuid import UUID

class SearchQueryRequest(BaseModel):
    job_id: UUID = Field(..., description="The job description ID to associate these results with")
    query: str = Field(..., description="The search query to find LinkedIn profiles")
    max_pages: int = Field(default=1, ge=1, le=10, description="Number of pages to search (1-10)")
    location: Optional[str] = Field(default=None, description="Location filter for the search")
    company: Optional[str] = Field(default=None, description="Company filter for the search")
    title: Optional[str] = Field(default=None, description="Job title filter for the search")
    skills: Optional[List[str]] = Field(default=None, description="List of skills to filter by")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "job_id": "00000000-0000-0000-0000-000000000000",
                "query": "software engineer python",
                "max_pages": 2,
                "location": "San Francisco",
                "company": "Google",
                "title": "Senior Software Engineer",
                "skills": ["Python", "Machine Learning", "AWS"]
            }
        }
    )

class SearchResponse(BaseModel):
    query: str = Field(..., description="The actual query that was executed")
    total_results: int = Field(..., description="Total number of LinkedIn profiles found")
    linkedin_urls: List[str] = Field(..., description="List of LinkedIn profile URLs")
    search_params: dict = Field(..., description="Original search parameters")
    timestamp: float = Field(..., description="Unix timestamp of when the search was performed")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "query": "software engineer python site:linkedin.com/in OR site:linkedin.com/pub",
                "total_results": 15,
                "linkedin_urls": [
                    "https://www.linkedin.com/in/john-doe",
                    "https://www.linkedin.com/in/jane-smith"
                ],
                "search_params": {
                    "query": "software engineer python",
                    "max_pages": 1,
                    "location": "San Francisco"
                },
                "timestamp": 1640995200.0
            }
        }
    )

class SearchHistoryItem(BaseModel):
    cache_key: str = Field(..., description="Cache key for the search")
    data: dict = Field(..., description="Cached search data")
    file_size: int = Field(..., description="Size of the cached file in bytes")

    model_config = ConfigDict(extra='allow')

class CacheClearResponse(BaseModel):
    message: str = Field(..., description="Status message")
    cleared_count: int = Field(..., description="Number of cache files cleared")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "message": "Cleared 5 cached files",
                "cleared_count": 5
            }
        }
    )

class BooleanSearchRequest(BaseModel):
    job_title: str = Field(..., description="Job title to search for")
    skills: List[str] = Field(default=[], description="Required skills")
    location: Optional[str] = Field(default=None, description="Preferred location")
    experience_level: Optional[str] = Field(default=None, description="Experience level (e.g., 'senior', 'junior')")
    company: Optional[str] = Field(default=None, description="Target company")
    max_pages: int = Field(default=1, ge=1, le=10, description="Number of pages to search")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "job_title": "Software Engineer",
                "skills": ["Python", "JavaScript", "React"],
                "location": "San Francisco, CA",
                "experience_level": "senior",
                "company": "Google",
                "max_pages": 2
            }
        }
    )

class LocationVerificationRequest(BaseModel):
    """Request model for candidate location verification."""
    candidate_name: str = Field(..., description="Full name of the candidate", min_length=1)
    expected_location: str = Field(..., description="Expected location to verify", min_length=1)
    company: Optional[str] = Field(None, description="Company name (optional)")

class LocationMatch(BaseModel):
    """Model for location match results."""
    title: str
    snippet: str
    link: str
    confidence: float = Field(..., ge=0.0, le=1.0)

class LocationVerificationResponse(BaseModel):
    """Response model for location verification."""
    success: bool
    candidate_name: str
    expected_location: str
    company: Optional[str] = None
    location_confidence: float = Field(0.0, ge=0.0, le=1.0)
    verification_result: str = Field(..., description="confirmed, likely, uncertain, or error")
    linkedin_matches: List[LocationMatch] = Field(default_factory=list)
    web_matches: List[LocationMatch] = Field(default_factory=list)
    company_matches: List[LocationMatch] = Field(default_factory=list)
    evidence_sources: List[str] = Field(default_factory=list)
    error: Optional[str] = None 