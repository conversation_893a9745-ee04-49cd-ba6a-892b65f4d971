name: DashChatApp_clean
channels:
  - defaults
  - conda-forge
dependencies:
  - appnope=0.1.4
  - asttokens=3.0.0
  - blas=2.126
  - blas-devel=3.9.0
  - brotli=1.0.9
  - brotli-bin=1.0.9
  - brotli-python=1.0.9
  - brotlicffi=*******
  - bzip2=1.0.8
  - ca-certificates=2025.6.15
  - cffi=1.17.1
  - clarabel=0.11.1
  - comm=0.2.2
  - cycler=0.11.0
  - debugpy=1.8.11
  - decorator=5.1.1
  - exceptiongroup=1.3.0
  - executing=0.8.3
  - fonttools=4.55.3
  - freetype=2.13.3
  - h2=4.2.0
  - hpack=4.1.0
  - hyperframe=6.1.0
  - importlib-metadata=8.5.0
  - importlib_metadata=8.5.0
  - ipykernel=6.29.5
  - ipython_pygments_lexers=1.1.1
  - jedi=0.19.2
  - jpeg=9e
  - jupyter_client=8.6.3
  - jupyter_core=5.7.2
  - kiwisolver=1.4.8
  - lcms2=2.16
  - lerc=4.0.0
  - libblas=3.9.0
  - libbrotlicommon=1.0.9
  - libbrotlidec=1.0.9
  - libbrotlienc=1.0.9
  - libcblas=3.9.0
  - libcxx=20.1.7
  - libdeflate=1.22
  - libexpat=2.7.0
  - libffi=3.4.6
  - libgfortran=15.1.0
  - libgfortran5=15.1.0
  - liblapack=3.9.0
  - liblapacke=3.9.0
  - liblzma=5.8.1
  - liblzma-devel=5.8.1
  - libopenblas=0.3.28
  - libosqp=0.6.3
  - libpng=1.6.39
  - libqdldl=0.1.7
  - libsodium=1.0.18
  - libsqlite=3.46.0
  - libtiff=4.7.0
  - libwebp-base=1.3.2
  - libzlib=1.2.13
  - llvm-openmp=20.1.7
  - lz4-c=1.9.4
  - matplotlib-inline=0.1.6
  - ncurses=6.5
  - nest-asyncio=1.6.0
  - numpy=2.2.4
  - openblas=0.3.28
  - openjpeg=2.5.2
  - openssl=3.5.0
  - packaging=24.2
  - parso=0.8.4
  - pexpect=4.9.0
  - pickleshare=0.7.5
  - pip=25.1.1
  - platformdirs=4.3.7
  - prompt_toolkit=3.0.43
  - ptyprocess=0.7.0
  - pure_eval=0.2.2
  - pycparser=2.22
  - pygments=2.19.1
  - pyparsing=3.2.0
  - pysocks=1.7.1
  - python=3.11.9
  - python-dateutil=2.9.0post0
  - python_abi=3.11
  - pyzmq=26.2.0
  - readline=8.2
  - requests=2.32.4
  - setuptools=73.0.1
  - six=1.17.0
  - sqlite=3.45.3
  - stack_data=0.2.0
  - tk=8.6.14
  - tornado=6.5.1
  - traitlets=5.14.3
  - typing-extensions=4.12.2
  - typing_extensions=4.12.2
  - unicodedata2=15.1.0
  - urllib3=2.5.0
  - wcwidth=0.2.13
  - wheel=0.45.1
  - xz=5.8.1
  - xz-gpl-tools=5.8.1
  - xz-tools=5.8.1
  - zeromq=4.3.5
  - zipp=3.21.0
  - zlib=1.2.13
  - zstandard=0.23.0
  - zstd=1.5.6
  - pip:
      - aiohttp==3.9.5
      - aiosignal==1.3.1
      - altair==5.5.0
      - amqp==5.3.1
      - annotated-types==0.6.0
      - anthropic==0.40.0
      - antlr4-python3-runtime==4.9.3
      - anyascii==0.3.2
      - anyio==4.9.0
      - asgiref==3.8.1
      - async-generator==1.10
      - async-timeout==5.0.1
      - attrs==23.2.0
      - autoflake==2.3.1
      - backcall==0.2.0
      - backoff==2.2.1
      - bcrypt==4.3.0
      - beautifulsoup4==4.12.3
      - billiard==*******
      - bleach==6.1.0
      - blinker==1.9.0
      - blis==0.7.11
      - cachetools==5.5.0
      - captcha==0.7.1
      - catalogue==2.0.10
      - celery==5.2.7
      - certifi==2024.12.14
      - chardet==5.2.0
      - charset-normalizer==3.3.2
      - ci-info==0.3.0
      - click==8.1.8
      - click-didyoumean==0.3.1
      - click-plugins==1.1.1
      - click-repl==0.3.0
      - cloudpathlib==0.16.0
      - cloudpickle==3.0.0
      - colorama==0.4.6
      - coloredlogs==15.0.1
      - confection==0.1.4
      - configobj==5.0.8
      - configparser==7.0.0
      - cryptography==44.0.1
      - cymem==2.0.8
      - dataclasses-json==0.6.4
      - dbutils==3.1.0
      - deepsearch-glm==1.0.0
      - defusedxml==0.7.1
      - deprecated==1.2.15
      - dill==0.3.8
      - dirtyjson==1.0.8
      - distro==1.9.0
      - dnspython==2.6.1
      - docling==2.12.0
      - docling-core==2.11.0
      - docling-ibm-models==3.1.0
      - docling-parse==3.0.0
      - docopt==0.6.2
      - docutils==0.16
      - easyocr==1.7.2
      - effdet==0.4.1
      - email-validator==2.2.0
      - emoji==2.14.0
      - et-xmlfile==2.0.0
      - etelemetry==0.3.1
      - eval-type-backport==0.2.0
      - extra-streamlit-components==0.1.71
      - fastapi==0.115.12
      - fastapi-cli==0.0.4
      - fastjsonschema==2.20.0
      - ffmpy==0.5.0
      - filelock==3.13.4
      - filetype==1.2.0
      - flask==3.1.0
      - flask-cors==4.0.1
      - flatbuffers==24.3.25
      - frozenlist==1.4.1
      - fsspec==2024.3.1
      - fuzzywuzzy==0.18.0
      - gensim==4.3.2
      - gevent==24.10.3
      - gitdb==4.0.12
      - gitpython==3.1.44
      - google-api-core==2.24.0
      - google-auth==2.37.0
      - google-cloud-core==2.4.3
      - google-cloud-kms==3.4.1
      - google-cloud-storage==3.1.0
      - google-cloud-vision==3.9.0
      - google-crc32c==1.7.1
      - google-resumable-media==2.7.2
      - google-search-results==2.4.2
      - googleapis-common-protos==1.66.0
      - greenlet==3.1.1
      - groovy==0.1.2
      - grpc-google-iam-v1==0.14.2
      - grpcio==1.68.1
      - grpcio-status==1.68.1
      - gtts==2.5.1
      - gunicorn==23.0.0
      - gym==0.26.2
      - gym-notices==0.0.8
      - h11==0.14.0
      - httpcore==1.0.5
      - httplib2==0.22.0
      - httptools==0.6.1
      - httpx==0.27.0
      - huggingface-hub==0.30.1
      - humanfriendly==10.0
      - idna==3.7
      - imageio==2.36.1
      - iniconfig==2.1.0
      - iopath==0.1.10
      - ipython==8.12.3
      - isodate==0.6.1
      - itsdangerous==2.2.0
      - jellyfish==1.0.3
      - jinja2==3.1.3
      - jiter==0.8.2
      - jmespath==1.0.1
      - joblib==1.4.0
      - jsonformer==0.12.0
      - jsonlines==3.1.0
      - jsonpatch==1.33
      - jsonpath-python==1.0.6
      - jsonpickle==4.0.5
      - jsonpointer==2.4
      - jsonref==1.1.0
      - jsonschema==4.22.0
      - jsonschema-specifications==2023.12.1
      - jupyterlab-pygments==0.3.0
      - kombu==5.2.3
      - langchain==0.2.17
      - langchain-anthropic==0.1.23
      - langchain-community==0.2.19
      - langchain-core==0.2.43
      - langchain-openai==0.1.25
      - langchain-text-splitters==0.2.4
      - langcodes==3.4.0
      - langdetect==1.0.9
      - langfuse==2.60.2
      - langsmith==0.1.147
      - language-data==1.2.0
      - layoutparser==0.3.4
      - lazy-loader==0.4
      - levenshtein==0.25.1
      - linkedin-api==2.3.1
      - llama-index-core==0.12.5
      - llama-parse==0.5.17
      - llvmlite==0.44.0
      - loguru==0.7.2
      - looseversion==1.3.0
      - lxml==5.3.1
      - marisa-trie==1.1.0
      - markdown==3.7
      - markdown-it-py==3.0.0
      - marko==2.1.2
      - markupsafe==2.1.5
      - marshmallow==3.21.1
      - mdurl==0.1.2
      - megaparse==0.0.50
      - megaparse-sdk==0.1.9
      - mistune==3.0.2
      - more-itertools==10.7.0
      - mpire==2.10.2
      - mpmath==1.3.0
      - msal==1.32.0
      - msal-extensions==1.3.1
      - multidict==6.0.5
      - multiprocess==0.70.16
      - murmurhash==1.0.10
      - mypy-extensions==1.0.0
      - mysql-connector-python==9.1.0
      - narwhals==1.34.0
      - nats-py==2.9.0
      - nbclient==0.10.0
      - nbconvert==7.16.4
      - nbformat==5.10.4
      - neo4j==5.28.1
      - networkx==3.3
      - nibabel==5.2.1
      - ninja==********
      - nipype==1.8.6
      - nltk==3.9.1
      - numba==0.61.2
      - olefile==0.47
      - omegaconf==2.3.0
      - onnx==1.17.0
      - onnxruntime==1.20.1
      - onnxtr==0.6.0
      - openai==1.57.4
      - openai-whisper==20240930
      - opencv-python==********
      - opencv-python-headless==*********
      - openpyxl==3.1.5
      - orjson==3.10.1
      - ortools==9.12.4544
      - outcome==1.3.0.post0
      - pandas==2.2.2
      - pandocfilters==1.5.1
      - pathlib==1.0.1
      - pathos==0.3.2
      - pdf2image==1.17.0
      - pdfminer-six==20231228
      - pikepdf==9.4.2
      - pillow==10.4.0
      - pillow-heif==0.21.0
      - pipreqs==0.5.0
      - playwright==1.49.1
      - plotly==6.0.1
      - pluggy==1.5.0
      - portalocker==3.0.0
      - pox==0.3.4
      - ppft==*******
      - preshed==3.0.9
      - prompt-toolkit==3.0.50
      - proto-plus==1.25.0
      - protobuf==5.29.4
      - prov==2.0.0
      - psutil==6.1.0
      - psycopg2==2.9.9
      - psycopg2-binary==2.9.9
      - pyarrow==19.0.1
      - pyasn1==0.6.0
      - pyasn1-modules==0.4.1
      - pyclipper==1.3.0.post6
      - pycocotools==2.0.8
      - pycryptodome==3.21.0
      - pydantic==2.9.2
      - pydantic-core==2.23.4
      - pydantic-settings==2.7.0
      - pydeck==0.9.1
      - pydot==2.0.0
      - pydub==0.25.1
      - pyee==12.0.0
      - pyflakes==3.2.0
      - pygame==2.5.2
      - pyjwt==2.9.0
      - pymupdf==1.24.2
      - pymupdfb==1.24.1
      - pymysql==1.1.1
      - pymysql-pool==0.4.6
      - pyp==3.0.9
      - pypandoc==1.14
      - pypdf==5.1.0
      - pypdf2==3.0.1
      - pypdfium2==4.30.0
      - pytesseract==0.3.10
      - pytest==8.3.5
      - python-bidi==0.6.3
      - python-docx==1.1.2
      - python-dotenv==1.0.1
      - python-iso639==2024.10.22
      - python-magic==0.4.27
      - python-multipart==0.0.20
      - python-oxmsg==0.0.1
      - python-pptx==1.0.2
      - pytz==2024.1
      - pyvis==0.3.2
      - pyxnat==1.6.2
      - pyyaml==6.0.1
      - rapidfuzz==3.8.1
      - rdflib==7.0.0
      - redis==4.5.5
      - referencing==0.35.1
      - regex==2024.4.16
      - requests-toolbelt==1.0.0
      - rich==13.7.1
      - rpds-py==0.18.1
      - rsa==4.7.2
      - rtree==1.3.0
      - ruff==0.11.2
      - s3transfer==0.11.4
      - safehttpx==0.1.6
      - safetensors==0.4.3
      - scikit-image==0.25.0
      - selenium==4.23.1
      - semantic-version==2.10.0
      - semchunk==2.2.0
      - sentence-transformers==3.2.1
      - serpapi==0.1.5
      - shapely==2.0.6
      - shellingham==1.5.4
      - simplejson==3.19.2
      - skillner==1.0.3
      - smart-open==6.4.0
      - smmap==5.0.2
      - sniffio==1.3.1
      - sortedcontainers==2.4.0
      - soupsieve==2.5
      - spacy-legacy==3.0.12
      - spacy-loggers==1.0.5
      - sqlalchemy==2.0.29
      - srsly==2.4.8
      - starlette==0.46.1
      - streamlit==1.44.1
      - streamlit-authenticator==0.4.2
      - striprtf==0.0.29
      - sympy==1.12
      - tabulate==0.9.0
      - tenacity==8.2.3
      - termcolor==2.4.0
      - threadpoolctl==3.4.0
      - tifffile==2024.12.12
      - tiktoken==0.8.0
      - timm==1.0.12
      - tinycss2==1.3.0
      - tokenizers==0.20.1
      - toml==0.10.2
      - tomlkit==0.13.2
      - torchvision==0.17.2
      - tqdm==4.66.2
      - traits==6.3.2
      - transformers==4.46.1
      - trio==0.27.0
      - trio-websocket==0.11.1
      - typer==0.12.5
      - typing-inspect==0.9.0
      - tzdata==2025.1
      - uvicorn==0.29.0
      - uvloop==0.19.0
      - vine==5.1.0
      - wasabi==1.1.2
      - watchfiles==0.22.0
      - webdriver-manager==4.0.2
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - websockets==12.0
      - werkzeug==3.1.3
      - wrapt==1.17.0
      - wsproto==1.2.0
      - xlrd==2.0.1
      - xlsxwriter==3.2.0
      - yarg==0.1.9
      - yarl==1.9.4
      - zope-event==5.0
      - zope-interface==7.1.1

