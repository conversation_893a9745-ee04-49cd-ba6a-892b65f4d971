# import csv
# from fuzzywuzzy import process
# from typing import *
# from exception import CustomException
# import sys
# from test_database import *
# from utils import *
# from fuzzywuzzy import process, fuzz

# db_config = {
#     'host': 'database-1.cgiynjn2jmwt.us-west-2.rds.amazonaws.com',
#     'database': 'Talentlab_ranking',
#     'user': 'admin',
#     'password': 'talentteam1!'
# }



# # def normalize_company_name(company: str) -> str:
# #     """
# #     Normalize company names by removing common suffixes and converting to lowercase.
# #     """
# #     return re.sub(r'\b(inc|llc|ltd|corporation|corp|co)\b', '', company.lower()).strip()
# def normalize_company_name(company: str) -> str:
#     """
#     Normalize company names by removing common suffixes, special characters, and converting to lowercase.
#     """
#     suffixes = r'\b(inc|llc|ltd|corporation|corp|co|plc|gmbh|pte|limited)\b'
#     company = re.sub(r'[^a-zA-Z0-9\s]', '', company)  # Remove special characters
#     return re.sub(suffixes, '', company.lower(), flags=re.IGNORECASE).strip()


# # def fuzzy_match(company_name: str, company_list: List[str], threshold: int = 80) -> Optional[str]:
# #     """
# #     Perform fuzzy matching to find the best match for a company name from a list.
# #     """
# #     normalized_list = [normalize_company_name(company) for company in company_list]
# #     normalized_company = normalize_company_name(company_name)
# #     match, score = process.extractOne(normalized_company, normalized_list, scorer=fuzz.token_sort_ratio)
# #     return match if score >= threshold else None
# def fuzzy_match(company_name: str, company_list: List[str], threshold: int = 80, scorer=fuzz.token_sort_ratio) -> Optional[str]:
#     """
#     Perform fuzzy matching to find the best match for a company name from a list.
#     """
#     normalized_list = [normalize_company_name(company) for company in company_list]
#     normalized_company = normalize_company_name(company_name)
#     match, score = process.extractOne(normalized_company, normalized_list, scorer=scorer)
#     return match if score >= threshold else None


# class CompanyExtractor:
#     def __init__(self, information_extracted: List[Dict[str, Any]]) -> None:
#         """
#         Initialize the CompanyExtractor with the extracted data.

#         :param information_extracted: List of extracted information (dictionaries).
#         :type information_extracted: List[Dict[str, Any]]
#         """
#         self.information_extracted = information_extracted
#         self.companies = []

#     def extract_company_name(
#         self,
#     ) -> None:
#         """
#         Extracts company names from the extracted information and stores them in the 'companies' attribute.

#         :return: None
#         :rtype: None
#         """
#         try:
#             for item in self.information_extracted:
#                 if isinstance(item, dict) and 'Experiences' in item:
#                     company_list: List[str] = []
#                     for experience in item['Experiences']:
#                         company_list.append(experience.get('Company', ''))
#                     self.companies.append(company_list)
#                 else:
#                     self.companies.append([])
#         except Exception as e:
#             raise CustomException(e, sys)     

#     def get_companies(self):
#         return self.companies


# class CompanyDataLoader:
#     def __init__(self, csv_file: str) -> None:
#         """
#         Initialize the CompanyDataLoader with the path to the CSV file.

#         :param csv_file: The path to the CSV file.
#         :type csv_file: str
#         """
#         self.csv_file = csv_file
#         self.company_data = self.load_company_data()

#     def load_company_data(self) -> Dict[str, str]:
#         """
#         Loads company data from a CSV file and returns it as a dictionary.

#         Args:
#             self (CompanyDataLoader): The object instance.

#         Returns:
#             Dict[str, str]: A dictionary where the keys are the first column values from the CSV and the values are the second column values.
#         """
#         data: Dict[str, str] = {}
#         try:
#             with open(self.csv_file, 'r', encoding='utf-8-sig') as file:
#                 reader = csv.reader(file)
#                 next(reader)  # Skip header
#                 for row in reader:
#                     data[row[0]] = row[1]
#         except FileNotFoundError as e:
#             print(f"Error: The file {self.csv_file} was not found: {e}")
#         except Exception as e:
#             print(f"An error occurred while loading the CSV file: {e}")
#         return data

# # class CompanyDataLoader:
# #     _cache = None  # Class-level cache to store company data

# #     def __init__(self, csv_file: str) -> None:
# #         """
# #         Initialize the CompanyDataLoader with the path to the CSV file.
# #         Uses cached data if available.

# #         :param csv_file: The path to the CSV file.
# #         :type csv_file: str
# #         """
# #         self.csv_file = csv_file

# #         # Load only once and store it in class-level cache
# #         if CompanyDataLoader._cache is None:
# #             CompanyDataLoader._cache = self._load_company_data()

# #         # Set instance-level variable to cached data
# #         self.company_data = CompanyDataLoader._cache

# #     def _load_company_data(self) -> Dict[str, str]:
# #         """
# #         Loads company data from a CSV file and caches it.

# #         Returns:
# #             Dict[str, str]: A dictionary where the keys are company names
# #                             and the values are company rankings.
# #         """
# #         data: Dict[str, str] = {}
# #         try:
# #             with open(self.csv_file, 'r', encoding='utf-8-sig') as file:
# #                 reader = csv.reader(file)
# #                 next(reader)  # Skip header
# #                 for row in reader:
# #                     data[row[0]] = row[1]  # Assuming first column is key, second is value
# #             print("✅ Company data loaded from CSV (cached).")
# #         except FileNotFoundError as e:
# #             print(f"❌ Error: The file {self.csv_file} was not found: {e}")
# #         except Exception as e:
# #             print(f"❌ An error occurred while loading the CSV file: {e}")
# #         return data

# #     def get_company_data(self) -> Dict[str, str]:
# #         """ Returns cached company data. """
# #         return CompanyDataLoader._cache

# class CompanyRankFinder:
#     def __init__(self, company_data: Dict[str, str]) -> None:
#         """
#         Initialize the CompanyRankFinder with the company data.

#         :param company_data: A dictionary where the keys are the company names and the values are the company ranks.
#         :type company_data: Dict[str, str]
#         """
#         self.company_data = company_data

#     def find_company_rank(self, company_name: str) -> Optional[int]:
#         """
#         Find the rank of a company based on the company name and company data.

#         Args:
#             company_name (str): The name of the company to find the rank for.

#         Returns:
#             int or None: The rank of the company if found, otherwise None.
#         """
#         try:
#             match = process.extractOne(company_name, self.company_data.keys())
#             if match and match[1] >= 85:  # Adjust the threshold as per requirement
#                 return int(self.company_data[match[0]])  # Convert rank to integer
#             return None
        
#         except Exception as e:
#             raise CustomException(e, sys)

# class CompanyRankEvaluator:
#     def __init__(self, csv_file):
#         self.data_loader = CompanyDataLoader(csv_file)
#         self.rank_finder = CompanyRankFinder(self.data_loader.company_data)

#     def query_company_preferences(self, JobID: str) -> Dict[str, str]:
#         """
#         Queries the database to get the company preferences for a given JobID.
        
#         Args:
#             job_id (str): The JobID to query for preferences.

#         Returns:
#             Dict[str, str]: A dictionary where keys are company names and values are hire labels.
#         """
#         try:
#             query = """
#             SELECT company_name, hire_label 
#             FROM company_preferences 
#             WHERE JobID = %s
#             """
#             with MySQLConnection(**db_config) as conn:
#                 with conn.cursor() as cursor:
#                     cursor.execute(query, (JobID,))
#                     results = cursor.fetchall()
                
#             # Convert the results into a dictionary: company_name -> hire_label
#             company_preferences = {row[0]: row[1] for row in results}
            
#             return company_preferences

#         except Exception as e:
#             raise CustomException(e, sys)      

#     # def adjust_score_preference(
#     #     self, company_name: str, company_preferences: Dict[str, str]
#     # ) -> int:
#     #     """
#     #     Adjust the company score based on the company preference label (DH, GC, BC, DNH).
        
#     #     Args:
#     #         company_name (str): The company name to adjust the score for.
#     #         company_preferences (Dict[str, str]): The company preferences dictionary from the database.

#     #     Returns:
#     #         int: The adjusted score based on the company preference.
#     #     """
#     #     hire_label = company_preferences.get(company_name, None)
        
#     #     if hire_label == "DH":  # Definite Hire
#     #         return 2  # Increase score
#     #     elif hire_label == "GC":  # Good Company
#     #         return 1  # Slightly increase score
#     #     elif hire_label == "BC":  # Bad Company
#     #         return -1  # Slightly decrease score
#     #     elif hire_label == "DNH":  # Do Not Hire
#     #         return -2  # Decrease score
#     #     return 0  # No preference, no change         
    
#     def evaluate_company_rank(
#         self, companies_list: List[List[str]]
#     ) -> List[int]:
#         """
#         Evaluate and find the best company rank for each list of companies.

#         Parameters:
#             companies_list (List[List[str]]): A list of lists, where each inner list contains company names.

#         Returns:
#             List[int]: A list of the best ranks for each list of companies.
#         """
#         company_ranks = []

#         for company_list in companies_list:
#             best_company_rank = float('inf')

#             for company_name in company_list:
#                 rank = self.rank_finder.find_company_rank(company_name)
#                 if rank is not None and rank < best_company_rank:
#                     best_company_rank = rank

#             if best_company_rank == float('inf'):
#                 company_ranks.append(1000)  # Default rank if no valid rank found
#             else:
#                 company_ranks.append(int(best_company_rank))

#         return company_ranks
    
#     def get_company_tier_and_value(self, company_rank: int) -> Tuple[str, int]:
#         """
#         Determines the tier and value of a company based on its rank.

#         Parameters:
#         company_rank (int): The rank of the company.

#         Returns:
#         Tuple[str, int]: A tuple containing the tier and value of the company.
#         """    
#         try:
#             if company_rank <= 500:
#                 return "Tier 1", 1
#             else:
#                 return "Tier 2", 0
            
#         except Exception as e:
#             raise CustomException(e, sys)
        
#     def evaluate_company_values(
#         self, company_ranks: List[Optional[int]]
#     ) -> List[Dict[str, int]]:
#         """
#         Evaluate and determine the tier and value for each company's rank.

#         Parameters:
#             company_ranks (List[Optional[int]]): A list of company ranks.

#         Returns:
#             List[Dict[str, int]]: A list of dictionaries with company discrete values.
#         """
#         try:
#             company_value_list = []

#             for rank in company_ranks:
#                 if rank == 1000 or rank is None:
#                     tier = "Tier 2"
#                     company_value = 0
#                     print(f"The company is not listed in the fortune 500. The company's tier is {tier}. "
#                         f"The discrete label is {company_value}.")
#                 else:
#                     tier, company_value = self.get_company_tier_and_value(rank)
#                     print(f"The rank {rank} is in {tier} with value {company_value}.")

#                 company_dict = {"Company_discrete": company_value}
#                 company_value_list.append(company_dict)

#             return company_value_list
        
#         except Exception as e:
#             raise CustomException(e, sys)
        

#     # def query_company_preferences(self, JobID: str) -> Dict[str, str]:
#     #     """
#     #     Queries the database to get the company preferences for a given JobID.
        
#     #     Args:
#     #         job_id (str): The JobID to query for preferences.

#     #     Returns:
#     #         Dict[str, str]: A dictionary where keys are company names and values are hire labels.
#     #     """
#     #     try:
#     #         query = """
#     #         SELECT company_name, hire_label, blackListCE
#     #         FROM company_preferences 
#     #         WHERE JobID = %s
#     #         """
#     #         with MySQLConnection(**db_config) as conn:
#     #             with conn.cursor() as cursor:
#     #                 cursor.execute(query, (JobID,))
#     #                 results = cursor.fetchall()
                
#     #         # Convert the results into a dictionary: company_name -> hire_label
#     #         company_preferences = {row[0]: row[1] for row in results}
#     #         logging.info(f"Company preferences for JobID {JobID}: {company_preferences}")
            
#     #         return company_preferences

#     #     except Exception as e:
#     #         raise CustomException(e, sys)


#     # def adjust_score_preference(self, company_name: str, company_preferences: Dict[str, str]) -> int:
#     #     """
#     #     Adjust the company score based on the company preference label (DH, GC, BC, DNH).
        
#     #     Args:
#     #         company_name (str): The company name to adjust the score for.
#     #         company_preferences (Dict[str, str]): The company preferences dictionary from the database.

#     #     Returns:
#     #         int: The adjusted score based on the company preference.
#     #     """
#     #     normalized_prefs = {normalize_company_name(name): label for name, label in company_preferences.items()}
#     #     match = fuzzy_match(company_name, normalized_prefs.keys())

#     #     if match:
#     #         hire_label = normalized_prefs[match]
#     #         if hire_label == "DH":  # Definite Hire
#     #             return 2  # Increase score
#     #         elif hire_label == "GC":  # Good Company
#     #             return 1  # Slightly increase score
#     #         elif hire_label == "BC":  # Bad Company
#     #             return -1  # Slightly decrease score
#     #         elif hire_label == "DNH":  # Do Not Hire
#     #             return -2  # Decrease score
#     #     return 0  # No preference, no change        

#     def query_company_preferences(self, JobID: str) -> Dict[str, Union[str, Dict[str, bool]]]:
#         """
#         Queries the database to get the company preferences for a given JobID.

#         Args:
#             JobID (str): The JobID to query for preferences.

#         Returns:
#             Dict[str, Union[str, Dict[str, bool]]]: 
#             A dictionary where keys are company names. Values are either hire labels or a dictionary containing 
#             both hire label and blackListCE information for `DNH` companies.
#         """
#         try:
#             query = """
#             SELECT company_name, hire_label, blackListCE
#             FROM company_preferences 
#             WHERE JobID = %s
#             """
#             with MySQLConnection(**db_config) as conn:
#                 with conn.cursor() as cursor:
#                     cursor.execute(query, (JobID,))
#                     results = cursor.fetchall()

#             # Convert the results into a dictionary
#             company_preferences = {}
#             for row in results:
#                 company_name, hire_label, blackListCE = row
#                 if hire_label == "DNH":
#                     company_preferences[company_name] = {"hire_label": hire_label, "blackListCE": blackListCE}
#                 else:
#                     company_preferences[company_name] = hire_label

#             logging.info(f"Company preferences for JobID {JobID}: {company_preferences}")
#             return company_preferences

#         except Exception as e:
#             raise CustomException(e, sys)

#     def adjust_score_preference(self, company_name: str, company_preferences: Dict[str, Union[str, Dict[str, bool]]]) -> int:
#         """
#         Adjust the company score based on the company preference label (DH, GC, BC, DNH).
        
#         Args:
#             company_name (str): The company name to adjust the score for.
#             company_preferences (Dict[str, Union[str, Dict[str, bool]]]): The company preferences dictionary from the database.

#         Returns:
#             int: The adjusted score based on the company preference.
#         """
#         # Normalize company preferences for fuzzy matching
#         normalized_prefs = {
#             normalize_company_name(name): prefs 
#             for name, prefs in company_preferences.items()
#         }
#         match = fuzzy_match(company_name, normalized_prefs.keys())

#         if match:
#             prefs = normalized_prefs[match]
#             if isinstance(prefs, dict):  # Handle nested structure for DNH
#                 hire_label = prefs["hire_label"]
#                 blackListCE = prefs.get("blackListCE", False)
#                 if hire_label == "DNH" and blackListCE:
#                     logging.warning(f"Company {company_name} is blacklisted for CE.")
#                 return 0  # Decrease score for DNH
#             else:
#                 hire_label = prefs
#                 if hire_label == "DH":  # Definite Hire
#                     return 0  # Increase score
#                 elif hire_label == "GC":  # Good Company
#                     return 0  # Slightly increase score
#                 elif hire_label == "BC":  # Bad Company
#                     return 0  # Slightly decrease score
#         return 0  # No preference, no change

    
import csv
from typing import *
from app.rank.exception import CustomException
import sys
# from test_database import *
from app.rank.utils import *
from functools import lru_cache
import numpy as np
import re
from app.logger import logging

from rapidfuzz import process, fuzz

db_config = {
    'host': 'database-1.cgiynjn2jmwt.us-west-2.rds.amazonaws.com',
    'database': 'Talentlab_ranking',
    'user': 'admin',
    'password': 'talentteam1!'
}



def normalize_company_name(company: str) -> str:
    """
    Normalize company names by removing common suffixes, special characters, and converting to lowercase.
    """
    suffixes = r'\b(inc|llc|ltd|corporation|corp|co|plc|gmbh|pte|limited)\b'
    company = re.sub(r'[^a-zA-Z0-9\s]', '', company)  # Remove special characters
    return re.sub(suffixes, '', company.lower(), flags=re.IGNORECASE).strip()

def fuzzy_match(company_name: str, company_list: List[str], threshold: int = 80, scorer=fuzz.token_sort_ratio) -> Optional[str]:
    """
    Perform fuzzy matching to find the best match for a company name from a list.
    """
    normalized_list = [normalize_company_name(company) for company in company_list]
    normalized_company = normalize_company_name(company_name)
    match, score = process.extractOne(normalized_company, normalized_list, scorer=scorer)
    return match if score >= threshold else None



class CompanyExtractor:
    def __init__(self, information_extracted: List[Dict[str, Any]]) -> None:
        """
        Initialize the CompanyExtractor with the extracted data.

        :param information_extracted: List of extracted information (dictionaries).
        :type information_extracted: List[Dict[str, Any]]
        """
        self.information_extracted = information_extracted
        self.companies = []

    def extract_company_name(
        self,
    ) -> None:
        """
        Extracts company names from the extracted information and stores them in the 'companies' attribute.

        :return: None
        :rtype: None
        """
        try:
            for item in self.information_extracted:
                if isinstance(item, dict) and 'Experiences' in item:
                    company_list: List[str] = []
                    for experience in item['Experiences']:
                        company_list.append(experience.get('Company', ''))
                    self.companies.append(company_list)
                else:
                    self.companies.append([])
        except Exception as e:
            raise CustomException(e, sys)     

    def get_companies(self):
        return self.companies


class CompanyDataLoader:
    def __init__(self, csv_file: str) -> None:
        """
        Initialize the CompanyDataLoader with the path to the CSV file.

        :param csv_file: The path to the CSV file.
        :type csv_file: str
        """
        self.csv_file = csv_file
        self.company_data = self.load_company_data()

    def load_company_data(self) -> Dict[str, str]:
        """
        Loads company data from a CSV file and returns it as a dictionary.

        Args:
            self (CompanyDataLoader): The object instance.

        Returns:
            Dict[str, str]: A dictionary where the keys are the first column values from the CSV and the values are the second column values.
        """
        data: Dict[str, str] = {}
        try:
            with open(self.csv_file, 'r', encoding='utf-8-sig') as file:
                reader = csv.reader(file)
                next(reader)  # Skip header
                for row in reader:
                    data[row[0]] = row[1]
        except FileNotFoundError as e:
            print(f"Error: The file {self.csv_file} was not found: {e}")
        except Exception as e:
            print(f"An error occurred while loading the CSV file: {e}")
        return data


class CompanyRankFinder:
    def __init__(self, company_data: Dict[str, str]) -> None:
        """
        Initialize the CompanyRankFinder with the company data.

        :param company_data: A dictionary where the keys are the company names and the values are the company ranks.
        :type company_data: Dict[str, str]
        """
        self.company_data = company_data

    def find_company_rank(self, company_name: str) -> Optional[int]:
        """
        Find the rank of a company based on fuzzy matching.

        Args:
            company_name (str): The name of the company to find the rank for.

        Returns:
            Optional[int]: The rank of the company if found, otherwise None.
        """
        try:
            if not company_name:  # ✅ Prevent empty string processing
                return None

            match = process.extractOne(company_name, self.company_data.keys(), scorer=fuzz.WRatio)

            if match and match[1] >= 85:  # ✅ Faster fuzzy match
                return int(self.company_data.get(match[0], 1000))  # ✅ Default rank if missing

            return None  # ✅ Return None if match score is too low

        except Exception as e:
            raise CustomException(e, sys.exc_info())  # ✅ Pass full traceback

class CompanyRankEvaluator:
    def __init__(self, csv_file):
        self.data_loader = CompanyDataLoader(csv_file)
        self.rank_finder = CompanyRankFinder(self.data_loader.company_data)


    @lru_cache(maxsize=1000)  # Cache results for 1000 JobIDs
    def query_company_preferences(self, JobID: str) -> Dict[str, Union[str, Dict[str, bool]]]:
        """Caches and fetches company preferences efficiently."""
        try:
            query = """
            SELECT company_name, hire_label, blackListCE
            FROM company_preferences 
            WHERE JobID = %s
            """
            with MySQLConnection(**db_config) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (JobID,))
                    results = cursor.fetchall()

            company_preferences = {
                row[0]: {"hire_label": row[1], "blackListCE": row[2]} if row[1] == "DNH" else row[1]
                for row in results
            }

            logging.info(f"Cached company preferences for JobID {JobID}")
            return company_preferences

        except Exception as e:
            raise CustomException(e, sys)
      
    
    # def evaluate_company_rank(
    #     self, companies_list: List[List[str]]
    # ) -> List[int]:
    #     """
    #     Evaluate and find the best company rank for each list of companies.

    #     Parameters:
    #         companies_list (List[List[str]]): A list of lists, where each inner list contains company names.

    #     Returns:
    #         List[int]: A list of the best ranks for each list of companies.
    #     """
    #     company_ranks = []

    #     for company_list in companies_list:
    #         best_company_rank = float('inf')

    #         for company_name in company_list:
    #             rank = self.rank_finder.find_company_rank(company_name)
    #             if rank is not None and rank < best_company_rank:
    #                 best_company_rank = rank

    #         if best_company_rank == float('inf'):
    #             company_ranks.append(1000)  # Default rank if no valid rank found
    #         else:
    #             company_ranks.append(int(best_company_rank))

    #     return company_ranks

    # def evaluate_company_rank(self, companies_list: List[List[str]]) -> List[int]:
    #     """Optimized: Uses NumPy to minimize nested loops."""
    #     company_ranks = np.full(len(companies_list), 1000)  # Default rank

    #     for idx, company_list in enumerate(companies_list):
    #         ranks = [self.rank_finder.find_company_rank(company) for company in company_list]
    #         ranks = [r for r in ranks if r is not None]  # Remove None values

    #         if ranks:
    #             company_ranks[idx] = np.min(ranks)  # Use min instead of max

    #     return company_ranks.tolist()


    def evaluate_company_rank(self, companies_list: List[List[str]]) -> List[int]:
        """Optimized: Uses NumPy & avoids unnecessary list operations."""
        company_ranks = np.full(len(companies_list), 1000)  # Default rank

        for idx, company_list in enumerate(companies_list):
            ranks = list(filter(None, (self.rank_finder.find_company_rank(company) for company in company_list)))

            if ranks:
                company_ranks[idx] = min(ranks)  # Built-in `min()` is faster for lists

        return company_ranks.tolist()

    
    def get_company_tier_and_value(self, company_rank: int) -> Tuple[str, int]:
        """
        Determines the tier and value of a company based on its rank.

        Parameters:
        company_rank (int): The rank of the company.

        Returns:
        Tuple[str, int]: A tuple containing the tier and value of the company.
        """    
        try:
            if company_rank <= 500:
                return "Tier 1", 1
            else:
                return "Tier 2", 0
            
        except Exception as e:
            raise CustomException(e, sys)
        
    # def evaluate_company_values(
    #     self, company_ranks: List[Optional[int]]
    # ) -> List[Dict[str, int]]:
    #     """
    #     Evaluate and determine the tier and value for each company's rank.

    #     Parameters:
    #         company_ranks (List[Optional[int]]): A list of company ranks.

    #     Returns:
    #         List[Dict[str, int]]: A list of dictionaries with company discrete values.
    #     """
    #     try:
    #         company_value_list = []

    #         for rank in company_ranks:
    #             if rank == 1000 or rank is None:
    #                 tier = "Tier 2"
    #                 company_value = 0
    #                 print(f"The company is not listed in the fortune 500. The company's tier is {tier}. "
    #                     f"The discrete label is {company_value}.")
    #             else:
    #                 tier, company_value = self.get_company_tier_and_value(rank)
    #                 print(f"The rank {rank} is in {tier} with value {company_value}.")

    #             company_dict = {"Company_discrete": company_value}
    #             company_value_list.append(company_dict)

    #         return company_value_list
        
    #     except Exception as e:
    #         raise CustomException(e, sys)



    def evaluate_company_values(self, company_ranks: List[Optional[int]]) -> List[Dict[str, int]]:
        """Optimized function using NumPy to process company ranks efficiently."""
        company_ranks = np.array(company_ranks)
        
        # Vectorized classification
        tier1_mask = company_ranks <= 500
        tier2_mask = company_ranks > 500

        company_values = np.where(tier1_mask, 1, 0)

        return [{"Company_discrete": val} for val in company_values]

        
   

    def query_company_preferences(self, JobID: str) -> Dict[str, Union[str, Dict[str, bool]]]:
        """
        Queries the database to get the company preferences for a given JobID.

        Args:
            JobID (str): The JobID to query for preferences.

        Returns:
            Dict[str, Union[str, Dict[str, bool]]]: 
            A dictionary where keys are company names. Values are either hire labels or a dictionary containing 
            both hire label and blackListCE information for `DNH` companies.
        """
        try:
            query = """
            SELECT company_name, hire_label, blackListCE
            FROM company_preferences 
            WHERE JobID = %s
            """
            with MySQLConnection(**db_config) as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (JobID,))
                    results = cursor.fetchall()

            # Convert the results into a dictionary
            company_preferences = {}
            for row in results:
                company_name, hire_label, blackListCE = row
                if hire_label == "DNH":
                    company_preferences[company_name] = {"hire_label": hire_label, "blackListCE": blackListCE}
                else:
                    company_preferences[company_name] = hire_label

            logging.info(f"Company preferences for JobID {JobID}: {company_preferences}")
            return company_preferences

        except Exception as e:
            raise CustomException(e, sys)


    def adjust_score_preference(self, company_name: str, company_preferences: Dict[str, Union[str, Dict[str, bool]]]) -> int:
        """
        Adjust the company score based on the company preference label (DH, GC, BC, DNH).
        
        Args:
            company_name (str): The company name to adjust the score for.
            company_preferences (Dict[str, Union[str, Dict[str, bool]]]): The company preferences dictionary from the database.

        Returns:
            int: The adjusted score based on the company preference.
        """
        # Normalize company preferences for fuzzy matching
        normalized_prefs = {
            normalize_company_name(name): prefs 
            for name, prefs in company_preferences.items()
        }
        match = fast_fuzzy_match(company_name, normalized_prefs.keys())

        if match:
            prefs = normalized_prefs[match]
            if isinstance(prefs, dict):  # Handle nested structure for DNH
                hire_label = prefs["hire_label"]
                blackListCE = prefs.get("blackListCE", False)
                if hire_label == "DNH" and blackListCE:
                    logging.warning(f"Company {company_name} is blacklisted for CE.")
                return 0  # Decrease score for DNH
            else:
                hire_label = prefs
                if hire_label == "DH":  # Definite Hire
                    return 0  # Increase score
                elif hire_label == "GC":  # Good Company
                    return 0  # Slightly increase score
                elif hire_label == "BC":  # Bad Company
                    return 0  # Slightly decrease score
        return 0  # No preference, no change

    





@staticmethod
def fast_fuzzy_match(company_name: str, company_list: List[str], threshold: int = 80) -> Optional[str]:
    """Uses batch distance calculations for faster fuzzy matching."""
    matches = process.cdist(
        [company_name], company_list, scorer=fuzz.token_sort_ratio, score_cutoff=threshold
    )

    if matches and matches[0]:  # If there is a valid match
        return matches[0][0]  # Best match

    return None





