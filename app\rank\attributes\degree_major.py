# from typing import *
# from extracter.prompt_generator import *
# from utils import *
# from exception import CustomException
# import sys
# from concurrent.futures import ThreadPoolExecutor
# from concurrent.futures import ThreadPoolExecutor, as_completed

# class DegreeMajorExtract:
#     def __init__(self, degree: str = '', major: Optional[str] = None):
#         self.degree = degree
#         self.major = major if major is not None else ''  

#     def __str__(self):
#         return f"Degree: {self.degree}, Major: {self.major}"

# class DegreeMajorInfo:
#     def __init__(self, universities: Optional[List[Dict[str, str]]] = None):
#         self.universities = []
#         if universities:
#             for uni in universities:
#                 degree = uni.get('Degree', '')
#                 major = uni.get('Major', '')
#                 self.universities.append(DegreeMajorExtract(degree, major))

#     def get_degrees(self) -> List[str]:
#         return [uni.degree for uni in self.universities]

#     def get_majors(self) -> List[str]:
#         return [uni.major for uni in self.universities if uni.major]  # Filter out empty majors
    
    
# def process_information(information_extracted: List[Optional[Dict]], max_workers: int = 7) -> (List[str], List[str]):
#     degree_list = []
#     major_list = []

#     def process_item(item):
#         if item is not None and isinstance(item, dict):
#             universities_info = item.get('Universities', [])
#             if isinstance(universities_info, list):
#                 education_info = DegreeMajorInfo(universities_info)
#                 degrees = ",".join(filter(None, education_info.get_degrees())).strip(',')
#                 majors = ",".join(filter(None, education_info.get_majors())).strip(',')
#             else:
#                 degrees = ""
#                 majors = ""
#         else:
#             degrees = ""
#             majors = ""

#         logging.info(f"Degrees: {degrees}, Majors: {majors}")    
        
#         return degrees, majors

#     with ThreadPoolExecutor(max_workers=max_workers) as executor:
#         futures = [executor.submit(process_item, item) for item in information_extracted]
        
#         for future in as_completed(futures):
#             try:
#                 degrees, majors = future.result()
#                 degree_list.append(degrees)
#                 major_list.append(majors)
#             except Exception as e:
#                 # Handle exceptions from futures if needed
#                 print(f"Error processing item: {e}")
#     return major_list, degree_list


# # def process_information(information_extracted: List[Optional[Dict]]) -> (List[str], List[str]):
# #     try:
# #         degree_list = []
# #         major_list = []

# #         for item in information_extracted:
# #             if item is not None and isinstance(item, dict):
# #                 universities_info = item.get('Universities', [])
# #                 if isinstance(universities_info, list):
# #                     education_info = DegreeMajorInfo(universities_info)
# #                     degrees = ",".join(filter(None, education_info.get_degrees())).strip(',')
# #                     majors = ",".join(filter(None, education_info.get_majors())).strip(',')
# #                 else:
# #                     degrees = ""
# #                     majors = ""
# #             else:
# #                 degrees = ""
# #                 majors = ""
            
# #             degree_list.append(degrees)
# #             major_list.append(majors)

# #         return major_list, degree_list
    
# #     except Exception as e:
# #         import sys
# #         raise CustomException(e, sys)


# class DegreeMajorExtractor:
#     def __init__(self, model: str, client, temp: float = 0, top_p: float = 1.0, tokens: int = 200):
#         self.model = model
#         self.client = client
#         self.temp = temp
#         self.top_p = top_p
#         self.tokens = tokens

#     def degree_major_completion(self, prompt: str) -> Optional[Tuple[str, dict]]:
#         try:
#             completion = self.client.chat.completions.create(
#                 model=self.model,
#                 response_format={"type": "json_object"},
#                 messages=[
#                     {"role": "system", "content": "You are a really helpful assistant."},
#                     {"role": "user", "content": prompt},
#                     {"role": "system", "content": '{"type": "json_object"}'},
#                 ],
#                 temperature=self.temp,
#                 max_tokens=self.tokens,
#                 top_p=self.top_p,
#             )
#             return completion.choices[0].message.content, completion.usage

#         except Exception as e:
#             print('ERROR in completion function:', e)
#             return None

#     def extract_degree_major(self, major_list: List[str], degree_list: List[str], job_description) -> List[str]:
#         try:
#             degree_major_extracted = []

#             for major, degree in zip(major_list, degree_list):
#                 prompt_generator = DegreeMajorPromptGenerator(degree, major, job_description)
#                 prompt = prompt_generator.generate_degree_major_prompt()

#                 # logging.info(f"Degree Major prompt: {prompt}")

                
#                 result = self.degree_major_completion(prompt)
#                 if result:
#                     content, usage = result
#                     logging.info(f"Degree Major tokens: {usage}")
#                     degree_major_extracted.append(JSONy.print_json_info(content))
            
#             return degree_major_extracted
        
#         except Exception as e:
#             raise CustomException(e, sys)

#     # @staticmethod
#     # def print_json_info(content: str) -> str:
#     #     # Assuming print_json_info is a function that processes the JSON string and returns the required information
#     #     # Replace with actual implementation
#     #     return content  # This should be replaced with actual processing logic



from typing import *
from app.rank.extracter.prompt_generator import *
from app.rank.utils import *
from app.rank.exception import CustomException
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from functools import lru_cache

class DegreeMajorExtract:
    def __init__(self, degree: str = '', major: Optional[str] = None):
        self.degree = degree
        self.major = major if major is not None else ''  

    def __str__(self):
        return f"Degree: {self.degree}, Major: {self.major}"

class DegreeMajorInfo:
    def __init__(self, universities: Optional[List[Dict[str, str]]] = None):
        self.universities = []
        if universities:
            for uni in universities:
                degree = uni.get('Degree', '')
                major = uni.get('Major', '')
                self.universities.append(DegreeMajorExtract(degree, major))

    def get_degrees(self) -> List[str]:
        return [uni.degree for uni in self.universities]

    def get_majors(self) -> List[str]:
        return [uni.major for uni in self.universities if uni.major]  # Filter out empty majors
    
    
def process_information(information_extracted: List[Optional[Dict]], max_workers: int = 7) -> (List[str], List[str]):
    degree_list = []
    major_list = []

    def process_item(item):
        if item is not None and isinstance(item, dict):
            universities_info = item.get('Universities', [])
            if isinstance(universities_info, list):
                education_info = DegreeMajorInfo(universities_info)
                degrees = ",".join(filter(None, education_info.get_degrees())).strip(',')
                majors = ",".join(filter(None, education_info.get_majors())).strip(',')
            else:
                degrees = ""
                majors = ""
        else:
            degrees = ""
            majors = ""

        logging.info(f"Degrees: {degrees}, Majors: {majors}")    
        
        return degrees, majors

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = [executor.submit(process_item, item) for item in information_extracted]
        
        for future in as_completed(futures):
            try:
                degrees, majors = future.result()
                degree_list.append(degrees)
                major_list.append(majors)
            except Exception as e:
                # Handle exceptions from futures if needed
                print(f"Error processing item: {e}")
    return major_list, degree_list




class DegreeMajorExtractor:
    def __init__(self, model: str, client):
        self.model = model
        self.client = client

    @lru_cache(maxsize=500)  # Cache up to 500 calls
    def degree_major_completion(self, prompt: str):
        """Calls the OpenAI API (GPT) with caching."""
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": "You are a really helpful assistant."},
                    {"role": "user", "content": prompt},
                    {"role": "system", "content": '{"type": "json_object"}'},
                ],
                temperature=0,
                max_tokens=200,
                top_p=1.0,
            )
            return completion.choices[0].message.content

        except Exception as e:
            logging.error(f"Error in degree_major_completion: {e}")
            return None

    def extract_degree_major(self, major_list, degree_list, job_description):
        """Optimized: Uses parallel API calls & caching"""
        try:
            degree_major_extracted = []
            prompts = [
                DegreeMajorPromptGenerator(degree, major, job_description).generate_degree_major_prompt()
                for major, degree in zip(major_list, degree_list)
            ]

            with ThreadPoolExecutor() as executor:
                results = list(executor.map(self.degree_major_completion, prompts))

            for content in results:
                if content:
                    degree_major_extracted.append(JSONy.print_json_info(content))
                else:
                    degree_major_extracted.append({})  # Maintain structure

            return degree_major_extracted

        except Exception as e:
            raise CustomException(e, sys)



