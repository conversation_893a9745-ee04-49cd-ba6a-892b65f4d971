from app.rank.extracter.prompt_generator import Job<PERSON>romptGenerator
from app.rank.exception import CustomException
import sys

class JobTitleExtractor:
    def __init__(self, information_extracted, job_description_str):
        self.information_extracted = information_extracted
        self.job_titles = []
        self.prompt_generator = JobPromptGenerator(job_description_str)

    def extract_job_titles(self):
        try:
            for item in self.information_extracted:
                job_title_str = ""
                if item is not None and isinstance(item, dict):
                    if 'Experiences' in item and isinstance(item['Experiences'], list):
                        for experience in item['Experiences']:
                            if isinstance(experience, dict):
                                if 'Job Title' in experience:
                                    job_title = experience['Job Title']
                                    job_title_str += job_title + ","
                                elif 'Position' in experience:
                                    job_title = experience['Position']
                                    job_title_str += job_title + ","
                                elif 'Title' in experience:
                                    job_title = experience['Title']
                                    job_title_str += job_title + ","    
                                elif 'JobTitle' in experience:
                                    job_title = experience['JobTitle']
                                    job_title_str += job_title + ","
                                elif 'Job title' in experience:
                                    job_title = experience['Job title']
                                    job_title_str += job_title + ","
                self.job_titles.append(job_title_str.strip(','))

        except Exception as e:
            raise CustomException(e, sys)         

    def get_job_titles(self):
        return self.job_titles

    def job_title_completion(self, client, prompt, model, temp=0, top_p=1.0, tokens=200):
        try:
            completion = client.chat.completions.create(
                model=model,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": "You are a really helpful assistant."},
                    {"role": "user", "content": prompt},
                    {"role": "system", "content": '{"type": "json_object"}'},
                ],
                temperature=temp,
                max_tokens=tokens,
                top_p=top_p,
            )
            return completion.choices[0].message.content, completion.usage
        except Exception as e:
            raise CustomException(e, sys)

    def job_format(self, user_query):  
        try:
            prompt = self.prompt_generator.generate_job_title_prompt(user_query)
            return prompt
        except Exception as e:
            raise CustomException(e, sys)