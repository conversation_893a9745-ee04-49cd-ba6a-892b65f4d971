import re
import spacy
from sklearn.feature_extraction.text import CountVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from nltk.corpus import stopwords
from skillNer.general_params import SK<PERSON>L_DB
from skillNer.skill_extractor_class import SkillExtractor
from spacy.matcher import PhraseMatcher
from app.rank.exception import CustomException
import sys

from concurrent.futures import ThreadPoolExecutor   



class SkillExtractor_candidate:
    def __init__(self, information_extracted):
        self.information_extracted = information_extracted
        self.skills = []

    def extract_skills(self):
        try:
            for item in self.information_extracted:
                if item is not None and isinstance(item, dict):
                    if 'Skills' in item and item['Skills'] is not None:
                        skill_list = item['Skills']
                    else:
                        skill_list = []
                else:
                    skill_list = []  

                self.skills.append(skill_list)

        except Exception as e:
            raise CustomException(e, sys)                

    def get_skills(self):
        return self.skills
    
class SkillExtractorClass:
    def __init__(self, nlp_model, skill_db):
        self.nlp = spacy.load(nlp_model)
        self.skill_extractor = SkillExtractor(self.nlp, skill_db, PhraseMatcher)
    
    def extract_skills(self, text):
        try:
            # processed_text = self.preprocess_text(text)
            annotations = self.skill_extractor.annotate(text)
            skills = [a['doc_node_value'] for a in annotations['results']['ngram_scored'] + annotations['results']['full_matches']]
            return skills
        
        except Exception as e:
            raise CustomException(e, sys)
        

class KeywordExtractor:
    def __init__(self):
        self.nlp = spacy.load("en_core_web_sm")
        self.stops = set(stopwords.words("english"))

    def clean_text(self, text):
        try:
            # Ensure the input is a string
            if isinstance(text, list):
                text = ' '.join(text)
            elif not isinstance(text, str):
                raise ValueError("Input text must be a string or a list of strings.")
            
            text = re.sub("[^a-zA-Z+3]", " ", text)
            text = text.lower()
            return [word for word in text.split() if word not in self.stops]
        
        except Exception as e:
            raise CustomException(e, sys)

    def extract_ngrams(self, text, n=2):
        try:
            doc = self.nlp(text)
            ngrams = []
            for i in range(len(doc) - n + 1):
                ngram = " ".join([token.text for token in doc[i:i + n]])
                ngrams.append(ngram)
            return ngrams
        
        except Exception as e:
            raise CustomException(e, sys)

    def extract_keywords(self, text, ngram_range=(1, 1), recruiter_skills=None):
        try:
            cleaned_text = self.clean_text(text)
            ngrams = []
            for n in range(ngram_range[0], ngram_range[1] + 1):
                ngrams.extend(self.extract_ngrams(" ".join(cleaned_text), n))
            
            if recruiter_skills:
                recruiter_skills_lower = [skill.lower() for skill in recruiter_skills]
                keywords = [word for word in ngrams if word.lower() in recruiter_skills_lower]
            else:
                keywords = ngrams
            
            return keywords
        
        except Exception as e:
            raise CustomException(e, sys)        

class SimilarityCalculator:
    def __init__(self):
        self.vectorizer = CountVectorizer(lowercase=True, stop_words='english')

    def calculate_similarity(self, resume_keywords, job_keywords):
        try:
            resume_keywords_text = " ".join(resume_keywords)
            job_keywords_text = " ".join(job_keywords)
            all_keywords_text = [resume_keywords_text, job_keywords_text]
            vectorized_keywords = self.vectorizer.fit_transform(all_keywords_text).toarray()
            cosine_sim = cosine_similarity([vectorized_keywords[0]], [vectorized_keywords[1]])[0][0]
            
            return cosine_sim
        
        except Exception as e:
            raise CustomException(e, sys)






