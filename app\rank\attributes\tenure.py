from typing import List, Dict, Optional
import json
from app.logger import logging
from app.rank.extracter.prompt_generator import *
from app.rank.utils import *
from app.rank.exception import CustomException
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime



# class DateExtractor:
#     def __init__(self, data: List[Optional[Dict]]):
#         self.data = data
    
#     def extract_dates(self) -> List[str]:
#         try:
#             dates_list = []
#             for item in self.data:
#                 dates_list.append(self._process_item(item))
#             return dates_list
        
#         except Exception as e:
#             raise CustomException(e, sys)
    
#     def _process_item(self, item: Optional[Dict]) -> str:
#         try:
#             if item is not None and isinstance(item, Dict):
#                 experiences = item.get('Experiences')
#                 if experiences is not None:
#                     return self._extract_dates_from_experiences(experiences)
#             return ""
        
#         except Exception as e:
#             raise CustomException(e, sys)
    
#     def _extract_dates_from_experiences(self, experiences: List[Dict]) -> str:
#         try:
#             date_strings = ""
            
#             for experience in experiences:
#                 tenure_str = experience.get('Tenure')
#                 if tenure_str:
#                     try:
#                         start_date, end_date = tenure_str.split(' - ')
#                         date_strings += f"{start_date} - {end_date},"
#                     except ValueError:
#                         # Handle the case where the tenure string does not split correctly
#                         print(f"Invalid tenure format: {tenure_str}")
#             # logging.info(f"Date strings: {date_strings}")
#             return date_strings.rstrip(',')
        
#         except Exception as e:
#             raise CustomException(e, sys)
        
class DateExtractor:
    def __init__(self, data: List[Optional[Dict]]):
        self.data = data
    
    def extract_dates(self) -> List[str]:
        try:
            dates_list = []
            for item in self.data:
                dates_list.append(self._process_item(item))
            return dates_list
        
        except Exception as e:
            logging.error(f"Error extracting dates: {e}")
            raise CustomException(e, sys)
    
    def _process_item(self, item: Optional[Dict]) -> str:
        try:
            if item is not None and isinstance(item, Dict):
                experiences = item.get('Experiences')
                if experiences is not None:
                    return self._extract_dates_from_experiences(experiences)
            return ""
        
        except Exception as e:
            logging.error(f"Error processing item: {e}")
            raise CustomException(e, sys)
    
    # def _extract_dates_from_experiences(self, experiences: List[Dict]) -> str:
    #     try:
    #         date_strings = ""
    #         for experience in experiences:
    #             tenure_str = experience.get('Tenure')
    #             if tenure_str:
    #                 if " - " in tenure_str:
    #                     try:
    #                         start_date, end_date = tenure_str.split(' - ')
    #                         date_strings += f"{start_date} - {end_date},"
    #                     except ValueError:
    #                         logging.warning(f"Invalid tenure format: {tenure_str}")
    #                 else:
    #                     logging.warning(f"Unrecognized tenure format: {tenure_str}")
    #         return date_strings.rstrip(',')
        
    #     except Exception as e:
    #         logging.error(f"Error extracting dates from experiences: {e}")
    #         raise CustomException(e, sys)

    # def _extract_dates_from_experiences(self, experiences: List[Dict]) -> str:
    #     try:
    #         date_strings = ""
    #         current_date = datetime.now().strftime("%B %Y")
    #         for experience in experiences:
    #             tenure_str = experience.get('Tenure')
    #             if tenure_str:
    #                 if " - " in tenure_str:
    #                     try:
    #                         start_date, end_date = tenure_str.split(' - ')
    #                         if end_date.strip().lower() == "present":
    #                             end_date = current_date
    #                         date_strings += f"{start_date} - {end_date},"
    #                     except ValueError:
    #                         logging.warning(f"Invalid tenure format: {tenure_str}")
    #                 else:
    #                     logging.warning(f"Unrecognized tenure format: {tenure_str}")
    #         return date_strings.rstrip(',')
        
    #     except Exception as e:
    #         logging.error(f"Error extracting dates from experiences: {e}")
    #         raise CustomException(e)
    # def _extract_dates_from_experiences(self, experiences: List[Dict]) -> str:
    #     try:
    #         date_strings = []
    #         current_date = datetime.now().strftime("%B %Y")
            
    #         for experience in experiences:
    #             tenure_str = experience.get('Tenure')
    #             if tenure_str:
    #                 try:
    #                     # Handle tenure with " - " separator
    #                     if " - " in tenure_str:
    #                         start_date, end_date = tenure_str.split(' - ')
    #                         start_date = self._validate_and_format_date(start_date)
    #                         end_date = self._validate_and_format_date(end_date, current_date)
                            
    #                         # If either date is invalid, mark the range as "Unknown"
    #                         if not start_date or not end_date:
    #                             logging.warning(f"Invalid date range: {tenure_str}. Marking as 'Unknown'.")
    #                             date_strings.append("Unknown")
    #                         else:
    #                             date_strings.append(f"{start_date} - {end_date}")
    #                     else:
    #                         logging.warning(f"Unrecognized tenure format: {tenure_str}. Marking as 'Unknown'.")
    #                         date_strings.append("Unknown")
    #                 except ValueError as ve:
    #                     logging.warning(f"Error parsing tenure: {tenure_str}, Error: {ve}")
    #                     date_strings.append("Unknown")
    #         return ", ".join(date_strings)
        
    #     except Exception as e:
    #         logging.error(f"Error extracting dates from experiences: {e}")
    #         raise CustomException(e)

    # def _validate_and_format_date(self, date_str: str, default: str = None) -> Optional[str]:
    #     try:
    #         # Handle "Present" or empty end date
    #         if date_str.strip().lower() == "present" or date_str.strip() == "0/0":
    #             return default
            
    #         # Split and handle invalid months in "month/year" format
    #         if "/" in date_str:
    #             parts = date_str.strip().split("/")
    #             month = int(parts[0]) if parts[0].isdigit() else None
    #             year = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else None
                
    #             if month and 1 <= month <= 12 and year:
    #                 return datetime(year, month, 1).strftime("%B %Y")
    #             elif year:  # If the month is invalid, reject the date
    #                 logging.warning(f"Invalid month in date: {date_str}. Marking as 'Unknown'.")
    #                 return None
            
    #         # Handle year-only format
    #         if date_str.isdigit():
    #             return datetime(int(date_str.strip()), 1, 1).strftime("%B %Y")
            
    #         # Log unrecognized format
    #         logging.warning(f"Unrecognized date format: {date_str}. Marking as 'Unknown'.")
    #         return None
    #     except Exception as e:
    #         logging.error(f"Error validating date: {date_str}, Error: {e}")
    #         return None

    def _extract_dates_from_experiences(self, experiences: List[Dict]) -> str:
        try:
            date_strings = []
            current_date = datetime.now().strftime("%B %Y")
            
            for experience in experiences:
                tenure_str = experience.get('Tenure')
                if tenure_str:
                    try:
                        # Handle tenure with " - " separator
                        if " - " in tenure_str:
                            start_date, end_date = tenure_str.split(' - ')
                            start_date = self._validate_and_format_date(start_date, fallback_to_start=True)
                            end_date = self._validate_and_format_date(end_date, current_date)
                            
                            # Add the calculated date range to the list
                            if start_date and end_date:
                                date_strings.append(f"{start_date} - {end_date}")
                            else:
                                logging.warning(f"Incomplete date range: {tenure_str}. Calculations may be affected.")
                        else:
                            logging.warning(f"Unrecognized tenure format: {tenure_str}")
                    except ValueError as ve:
                        logging.warning(f"Error parsing tenure: {tenure_str}, Error: {ve}")
            return ", ".join(date_strings)
        
        except Exception as e:
            logging.error(f"Error extracting dates from experiences: {e}")
            raise CustomException(e)

    def _validate_and_format_date(self, date_str: str, default: str = None, fallback_to_start: bool = False) -> Optional[str]:
        try:
            # Handle "Present" or empty end date
            if date_str.strip().lower() == "present" or date_str.strip() == "0/0":
                return default
            
            # Split and handle invalid months in "month/year" format
            if "/" in date_str:
                parts = date_str.strip().split("/")
                month = int(parts[0]) if parts[0].isdigit() and 1 <= int(parts[0]) <= 12 else 1  # Default invalid months to January
                year = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else None
                
                if year:
                    return datetime(year, month, 1).strftime("%B %Y")
                elif fallback_to_start:  # If year is missing, fall back to a start year
                    logging.warning(f"Invalid year in date: {date_str}. Using fallback year 1900.")
                    return datetime(1900, month, 1).strftime("%B %Y")
            
            # Handle year-only format
            if date_str.isdigit():
                return datetime(int(date_str.strip()), 1, 1).strftime("%B %Y")
            
            # Log unrecognized format
            logging.warning(f"Unrecognized date format: {date_str}.")
            return None
        except Exception as e:
            logging.error(f"Error validating date: {date_str}, Error: {e}")
            return None


        
        
class ExperienceCalculator:
    def __init__(self, model, datelist, date_format, job_description, client, temp=0, top_p=1.0, tokens=1000):
        self.model = model
        self.temp = temp
        self.top_p = top_p
        self.tokens = tokens
        self.date_prompt_generator = DatePrompt(datelist, date_format, job_description)
        self.client= client
    def date_format_prompt(self, user_query, input_format):
        return self.date_prompt_generator(user_query, input_format)

    # def dates_extraction_completion(self, prompt, client):
    #     try:
    #         completion = client.chat.completions.create(
    #             model=self.model,
    #             response_format={"type": "json_object"},
    #             messages=[
    #                 {"role": "system", "content": "You are a really helpful assistant."},
    #                 {"role": "user", "content": prompt},
    #                 {"role": "system", "content": '{"type": "json_object"}'},
    #             ],
    #             temperature=self.temp,
    #             max_tokens=self.tokens,
    #             top_p=self.top_p,
    #         )
    #         return completion.choices[0].message.content, completion.usage

    #     except Exception as e:
    #         print('ERROR in completion function:', e)
    #         return None

    def dates_extraction_completion(self, prompt, client):
        try:
            completion = client.chat.completions.create(
                model=self.model,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": "You are a really helpful assistant."},
                    {"role": "user", "content": prompt},
                    {"role": "system", "content": '{"type": "json_object"}'},
                ],
                temperature=self.temp,
                max_tokens=self.tokens,
                top_p=self.top_p,
            )
            # Extract content from completion response
            if completion.choices:
                json_content, usage = completion.choices[0].message.content, completion.usage
                logging.info(f"Date tokens: {usage}")
                return json_content
            else:
                logging.warning("No completion content found.")
                return None

        except Exception as e:
            logging.error(f'ERROR in completion function: {e}')
            return None

    @staticmethod
    def parse_json_info(json_content):
        try:
            data = json.loads(json_content)
            return data.get('tenure')
        except json.JSONDecodeError as e:
            logging.error(f"Error decoding JSON: {e}")
            return None

    @staticmethod
    def experience_level(years):
        years = float(years)
        if years < 2:
            return '1'  # Junior
        elif 2 <= years < 4:
            return '2'  # Associate
        elif 4 <= years < 6:
            return '3'  # Mid-level
        elif 6 <= years < 8:
            return '4'  # Senior
        elif years >= 8:
            return '5'  # Executive

    # def process_dates(self, dates_list, format_string):
    #     try:
    #         dates_extracted = []
    #         for datelist in dates_list:
    #             prompt_example = self.date_format_prompt(user_query=datelist, input_format=format_string)
        
    #             result = self.dates_extraction_completion(prompt_example, self.client)
    #             if result:
    #                 tenure = self.parse_json_info(result[0])
    #                 # tenure = JSONy.print_json_info(result[0])
    #                 if tenure:
    #                     dates_extracted.append(tenure)
    #         return dates_extracted
        
    #     except Exception as e:
    #         raise CustomException(e, sys)

    def process_dates(self, dates_list, format_string):
        try:
            dates_extracted = []
            
            def process_tenure(datelist):
                prompt_example = self.date_format_prompt(user_query=datelist, input_format=format_string)
                result = self.dates_extraction_completion(prompt_example, self.client)
                if result:
                    tenure = self.parse_json_info(result)
                    if tenure:
                        return tenure
            
            with ThreadPoolExecutor() as executor:
                futures = {executor.submit(process_tenure, datelist): datelist for datelist in dates_list}
                for future in as_completed(futures):
                    try:
                        tenure = future.result()
                        if tenure:
                            dates_extracted.append(tenure)
                    except Exception as e:
                        logging.error(f"Error processing tenure: {e}")
            logging.info(f"Dates extracted: {dates_extracted}")
            return dates_extracted
        
        except Exception as e:
            logging.error(f"Error processing dates: {e}")
            return []
