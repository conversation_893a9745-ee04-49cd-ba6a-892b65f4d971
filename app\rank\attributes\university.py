# import csv
# from fuzzywuzzy import process
# import os
# import sys
# from typing import Dict

# module_path = os.path.abspath(os.path.join('..'))  # Adjust the path as needed
# if module_path not in sys.path:
#     sys.path.append(module_path)

# from exception import CustomException
# from logger import logging

# class UniversityExtractor:
#     def __init__(self, information_extracted):
#         self.information_extracted = information_extracted
#         self.universities = []
#         logging.basicConfig(level=logging.DEBUG)

#     # def extract_universities(self):
#     #     try:
#     #         for item in self.information_extracted:
#     #             if isinstance(item, dict) and 'Universities' in item:
#     #                 uni_list = []
#     #                 for university in item['Universities']:
#     #                     if isinstance(university, dict) and 'Name' in university:
#     #                         uni_list.append(university['Name'])
#     #                 self.universities.append(uni_list)
#     #             else:
#     #                 self.universities.append([])

#     #     except Exception as e:
#     #         raise CustomException(e, sys)           

#     def extract_universities(self):
#         try:
#             if self.information_extracted is None:
#                 raise ValueError("information_extracted is None")

#             for item in self.information_extracted:
#                 if item is None:
#                     logging.warning("Encountered a None item in information_extracted")
#                     self.universities.append([])
#                     continue

#                 if isinstance(item, dict) and 'Universities' in item:
#                     uni_list = []
#                     universities = item.get('Universities')
                    
#                     if universities is None:
#                         logging.warning("Universities key has None value in item: %s", item)
#                         self.universities.append([])
#                         continue

#                     for university in universities:
#                         if isinstance(university, dict) and 'Name' in university:
#                             uni_list.append(university['Name'])
#                     self.universities.append(uni_list)
#                 else:
#                     self.universities.append([])

#         except Exception as e:
#             logging.error("Error during extracting universities: %s", str(e))
#             raise CustomException(e, sys)            

#     def get_universities(self):
#         return self.universities
    

# class UniversityDataLoader:
#     def __init__(self, csv_file):
#         self.csv_file = csv_file
#         self.universities_data = self.load_university_data()

#     def load_university_data(self):
#         """
#         Loads university data from a CSV file and returns it as a dictionary.

#         Returns:
#         dict: A dictionary where university names are keys and corresponding data are values.
#         """
#         try:
#             data = {}
#             try:
#                 with open(self.csv_file, 'r', encoding='utf-8-sig') as file:
#                     reader = csv.reader(file)
#                     next(reader)  # Skip header
#                     for row in reader:
#                         data[row[1]] = row[0]  # Assuming the university name is in the second column and rank in the first
#             except FileNotFoundError:
#                 print(f"Error: The file {self.csv_file} was not found.")
#             except Exception as e:
#                 print(f"An error occurred while loading the CSV file: {e}")
#             return data
        
#         except Exception as e:
#             raise CustomException(e, sys)


# # class UniversityDataLoader:
# #     _cache = None  # Class-level cache to store university data

# #     def __init__(self, csv_file: str) -> None:
# #         """
# #         Initialize the UniversityDataLoader with the path to the CSV file.
# #         Uses cached data if available.

# #         :param csv_file: The path to the CSV file.
# #         :type csv_file: str
# #         """
# #         self.csv_file = csv_file

# #         # Load only once and store in class-level cache
# #         if UniversityDataLoader._cache is None:
# #             UniversityDataLoader._cache = self._load_university_data()

# #         # Set instance-level variable to cached data
# #         self.universities_data = UniversityDataLoader._cache

# #     def _load_university_data(self) -> Dict[str, str]:
# #         """
# #         Loads university data from a CSV file and caches it.

# #         Returns:
# #             Dict[str, str]: A dictionary where university names are keys 
# #                             and their corresponding rankings are values.
# #         """
# #         data: Dict[str, str] = {}
# #         try:
# #             with open(self.csv_file, 'r', encoding='utf-8-sig') as file:
# #                 reader = csv.reader(file)
# #                 next(reader)  # Skip header
# #                 for row in reader:
# #                     data[row[1]] = row[0]  # Assuming university name is in column 2, rank in column 1
# #             print("✅ University data loaded from CSV (cached).")
# #         except FileNotFoundError:
# #             print(f"❌ Error: The file {self.csv_file} was not found.")
# #         except Exception as e:
# #             print(f"❌ An error occurred while loading the CSV file: {e}")
# #         return data

# #     def get_university_data(self) -> Dict[str, str]:
# #         """ Returns cached university data. """
# #         return UniversityDataLoader._cache

    

# class UniversityRankFinder:
#     def __init__(self, universities_data):
#         self.universities_data = universities_data

#     def find_rank(self, university_name):
#         """
#         Finds the rank of a university based on the university name and university data.

#         Parameters:
#             university_name (str): The name of the university to find the rank for.

#         Returns:
#             str or None: The rank of the university if found, otherwise None.
#         """
#         try:
#             match = process.extractOne(university_name, self.universities_data.keys())
#             if match and match[1] >= 87:  # Adjust the threshold as per requirement
#                 return self.universities_data[match[0]]
#             return None
        
#         except Exception as e:
#             raise CustomException(e, sys)

# class UniversityRankEvaluator:
#     def __init__(self, csv_file):
#         self.data_loader = UniversityDataLoader(csv_file)
#         self.rank_finder = UniversityRankFinder(self.data_loader.universities_data)

#     # def evaluate_university_rank(self, uni_name_list):
#     #     """
#     #     Evaluate and find the best university rank for each list of universities.

#     #     Parameters:
#     #         uni_name_list (list of list of str): A list of lists, where each inner list contains university names.

#     #     Returns:
#     #         list of int: A list of the best ranks for each list of universities, or None if no valid rank found.
#     #     """
#     #     try:
#     #         university_ranks = []

#     #         for university_list in uni_name_list:
#     #             # logging.info(f"University list: {university_list}")
#     #             best_rank = float('inf')  # Initialize to positive infinity to ensure any rank found is better

#     #             for university_name in university_list:
#     #                 logging.info(f"Processing {university_name}")
#     #                 rank = self.rank_finder.find_rank(university_name)
#     #                 logging.info(f"Retrieved rank for {university_name}: {rank}")

#     #                 if rank is None:
#     #                     best_rank= 1000

#     #                 elif rank is not None and rank.isdigit():
#     #                     rank = int(rank)
#     #                     if rank < best_rank:
#     #                         best_rank = rank
#     #                 else:
#     #                     logging.warning(f"Invalid rank for {university_name}: {rank}")

#     #             # If no valid rank found, append None
#     #             university_ranks.append(best_rank if best_rank != float('inf') else None)

#     #         logging.info(f"University ranks: {university_ranks}")    
#     #         print("university_ranks", university_ranks)

#     #         return university_ranks
        
#     #     except Exception as e:
#     #         raise CustomException(e, sys)


#     def evaluate_university_rank(self, uni_name_list):
#         """
#         Evaluate and find the best university rank for each list of universities.

#         Parameters:
#             uni_name_list (list of list of str): A list of lists, where each inner list contains university names.

#         Returns:
#             list of int: A list of the best ranks for each list of universities.
#         """
#         try:
#             university_ranks = []

#             for university_list in uni_name_list:
#                 logging.info(f"University list: {university_list}")
#                 best_rank = float('inf')  # Initialize to positive infinity to ensure any rank found is better

#                 for university_name in university_list:
#                     rank = self.rank_finder.find_rank(university_name)
#                     if rank is not None and int(rank) < best_rank:
#                         best_rank = int(rank)

#                 university_ranks.append(best_rank if best_rank != float('inf') else None)  # None if no valid rank found

#             logging.info(f"University ranks: {university_ranks}")    
#             print("university_ranks", university_ranks)

#             return university_ranks
        
#         except Exception as e:
#             raise CustomException(e, sys)



#     def get_tier_and_value(self, qs_rank):
#         """
#         Returns the tier and value based on the input QS rank.

#         Parameters:
#             qs_rank (int): The QS rank to determine the tier and value.

#         Returns:
#             tuple: A tuple containing the tier and value based on the QS rank.
#         """    
#         try:
#             if qs_rank is None or qs_rank > 200:
#                 return "Tier 5", 0
#             elif qs_rank > 150:
#                 return "Tier 4", 0.25
#             elif qs_rank > 100:
#                 return "Tier 3", 0.50
#             elif qs_rank > 50:
#                 return "Tier 2", 0.75
#             return "Tier 1", 1 
#         # except Exception as e:
#         #     raise CustomException(e, sys)
#         # try:
#         #     if not isinstance(qs_rank, int) or qs_rank < 0:
#         #         raise ValueError("QS rank must be a non-negative integer")

#         #     if qs_rank > 100:
#         #         return "Tier 5", 0
#         #     elif qs_rank > 70:
#         #         return "Tier 4", 1
#         #     elif qs_rank > 50:
#         #         return "Tier 3", 2
#         #     elif qs_rank > 30:
#         #         return "Tier 2", 3
#         #     elif qs_rank > 0:
#         #         return "Tier 1", 4 
#         #     else:
#         #         raise ValueError("Invalid QS rank")

#         except ValueError as e:
#             raise CustomException(e)
#         except Exception as e:
#             raise CustomException(f"An unexpected error occurred: {e}")        

#     def evaluate_university_values(self, university_ranks):
#         """
#         Evaluate and determine the tier and value for each university's rank.

#         Parameters:
#             university_ranks (list of int): A list of university ranks.

#         Returns:
#             list of dict: A list of dictionaries with university discrete values.
#         """
#         try:
#             university_value_list = []
#             # logging.info(f" makldhasjdlhakjsdbaksjdhk University ranks: {university_ranks}")
#             for rank in university_ranks:
#                 # logging.info(f" best rank of the university candidate: {rank}")
#                 tier, value = self.get_tier_and_value(rank)
#                 university_dict = {"university_discrete": value}
#                 university_value_list.append(university_dict)
            
#             return university_value_list
        
#         except Exception as e:
#             raise CustomException(e, sys)
    
    


# # if __name__ == "__main__":

# #     universities=[['University of Rajasthan','IIT vadodara','Harvard University','ghjfgu'],['IIT vadodara'],['Friedrich-Alexander-Universität Erlangen-Nürnberg','Hanyang University']]


# #     evaluator = UniversityRankEvaluator('/Users/<USER>/Desktop/Talent_production/data/mapping/2023 QS World University Rankings.csv')
# #     university_ranks = evaluator.evaluate_university_rank(universities)
# #     university_values = evaluator.evaluate_university_values(university_ranks)


import csv
# from fuzzywuzzy import process
from rapidfuzz import process, fuzz
import os
import sys
from typing import Dict

module_path = os.path.abspath(os.path.join('..'))  # Adjust the path as needed
if module_path not in sys.path:
    sys.path.append(module_path)

from app.rank.exception import CustomException
from app.logger import logging

class UniversityExtractor:
    def __init__(self, information_extracted):
        self.information_extracted = information_extracted
        self.universities = []
        logging.basicConfig(level=logging.DEBUG)

    # def extract_universities(self):
    #     try:
    #         for item in self.information_extracted:
    #             if isinstance(item, dict) and 'Universities' in item:
    #                 uni_list = []
    #                 for university in item['Universities']:
    #                     if isinstance(university, dict) and 'Name' in university:
    #                         uni_list.append(university['Name'])
    #                 self.universities.append(uni_list)
    #             else:
    #                 self.universities.append([])

    #     except Exception as e:
    #         raise CustomException(e, sys)           

    def extract_universities(self):
        try:
            if self.information_extracted is None:
                raise ValueError("information_extracted is None")

            for item in self.information_extracted:
                if item is None:
                    logging.warning("Encountered a None item in information_extracted")
                    self.universities.append([])
                    continue

                if isinstance(item, dict) and 'Universities' in item:
                    uni_list = []
                    universities = item.get('Universities')
                    
                    if universities is None:
                        logging.warning("Universities key has None value in item: %s", item)
                        self.universities.append([])
                        continue

                    for university in universities:
                        if isinstance(university, dict) and 'Name' in university:
                            uni_list.append(university['Name'])
                    self.universities.append(uni_list)
                else:
                    self.universities.append([])

        except Exception as e:
            logging.error("Error during extracting universities: %s", str(e))
            raise CustomException(e, sys)            

    def get_universities(self):
        return self.universities
    

class UniversityDataLoader:
    def __init__(self, csv_file):
        self.csv_file = csv_file
        self.universities_data = self.load_university_data()

    def load_university_data(self):
        """
        Loads university data from a CSV file and returns it as a dictionary.

        Returns:
        dict: A dictionary where university names are keys and corresponding data are values.
        """
        try:
            data = {}
            try:
                with open(self.csv_file, 'r', encoding='utf-8-sig') as file:
                    reader = csv.reader(file)
                    next(reader)  # Skip header
                    for row in reader:
                        data[row[1]] = row[0]  # Assuming the university name is in the second column and rank in the first
            except FileNotFoundError:
                print(f"Error: The file {self.csv_file} was not found.")
            except Exception as e:
                print(f"An error occurred while loading the CSV file: {e}")
            return data
        
        except Exception as e:
            raise CustomException(e, sys)

    

class UniversityRankFinder:
    def __init__(self, universities_data):
        self.universities_data = universities_data

    def find_rank(self, university_name):
        """
        Finds the rank of a university based on the university name and university data.

        Parameters:
            university_name (str): The name of the university to find the rank for.

        Returns:
            str or None: The rank of the university if found, otherwise None.
        """
        try:
            match = process.extractOne(university_name, self.universities_data.keys(),scorer=fuzz.WRatio)
            if match and match[1] >= 87:  # Adjust the threshold as per requirement
                return self.universities_data[match[0]]
            return None
        
        except Exception as e:
            raise CustomException(e, sys)

class UniversityRankEvaluator:
    def __init__(self, csv_file):
        self.data_loader = UniversityDataLoader(csv_file)
        self.rank_finder = UniversityRankFinder(self.data_loader.universities_data)


    def evaluate_university_rank(self, uni_name_list):
        """
        Evaluate and find the best university rank for each list of universities.

        Parameters:
            uni_name_list (list of list of str): A list of lists, where each inner list contains university names.

        Returns:
            list of int: A list of the best ranks for each list of universities.
        """
        try:
            university_ranks = []

            for university_list in uni_name_list:
                logging.info(f"University list: {university_list}")
                best_rank = float('inf')  # Initialize to positive infinity to ensure any rank found is better

                for university_name in university_list:
                    rank = self.rank_finder.find_rank(university_name)
                    if rank is not None and int(rank) < best_rank:
                        best_rank = int(rank)

                university_ranks.append(best_rank if best_rank != float('inf') else None)  # None if no valid rank found

            logging.info(f"University ranks: {university_ranks}")    
            print("university_ranks", university_ranks)

            return university_ranks
        
        except Exception as e:
            raise CustomException(e, sys)



    def get_tier_and_value(self, qs_rank):
        """
        Returns the tier and value based on the input QS rank.

        Parameters:
            qs_rank (int): The QS rank to determine the tier and value.

        Returns:
            tuple: A tuple containing the tier and value based on the QS rank.
        """    
        try:
            if qs_rank is None or qs_rank > 200:
                return "Tier 5", 0
            elif qs_rank > 150:
                return "Tier 4", 0.25
            elif qs_rank > 100:
                return "Tier 3", 0.50
            elif qs_rank > 50:
                return "Tier 2", 0.75
            return "Tier 1", 1 

        except ValueError as e:
            raise CustomException(e)
        except Exception as e:
            raise CustomException(f"An unexpected error occurred: {e}")        

    def evaluate_university_values(self, university_ranks):
        """
        Evaluate and determine the tier and value for each university's rank.

        Parameters:
            university_ranks (list of int): A list of university ranks.

        Returns:
            list of dict: A list of dictionaries with university discrete values.
        """
        try:
            university_value_list = []
            # logging.info(f" makldhasjdlhakjsdbaksjdhk University ranks: {university_ranks}")
            for rank in university_ranks:
                # logging.info(f" best rank of the university candidate: {rank}")
                tier, value = self.get_tier_and_value(rank)
                university_dict = {"university_discrete": value}
                university_value_list.append(university_dict)
            
            return university_value_list
        
        except Exception as e:
            raise CustomException(e, sys)
    





