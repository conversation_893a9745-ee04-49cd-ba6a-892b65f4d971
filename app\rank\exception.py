import sys
from app.logger import logging
from typing import *
import re
import os
import subprocess
from typing import Any

def error_message_detail(error: Exception, error_detail: Any) -> str:
    _, _, exc_tb = error_detail.exc_info()
    if exc_tb is not None:
        file_name = exc_tb.tb_frame.f_code.co_filename
        line_no = exc_tb.tb_lineno
    else:
        file_name = "Unknown"
        line_no = "Unknown"
    error_message = "Error in python script name [{0}] line number [{1}] error message [{2}]".format(
        file_name, line_no, str(error)
    )
    return error_message
    
class CustomException(Exception):
    def __init__(self, error_message, error_detail: Any):
        super().__init__(error_message)
        self.error_message=error_message_detail(error_message,error_detail=error_detail)

    def __str__(self) -> str:
        return self.error_message   