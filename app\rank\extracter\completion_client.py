import json

class CompletionClient:
    def __init__(self, client, model: str, example_json: dict) -> None:
        self.model, self.client, self.example_json = model, client, example_json
       

    def completion(self, prompt: str, temp: float = 0.0, top_p: float = 1.0) -> dict:
        """
        Get completion from the OpenAI API.

        Args:
            prompt (str): The user's query for generating the prompt.
            temp (float, optional): The temperature parameter for the OpenAI API. Defaults to 0.0.
            top_p (float, optional): The top_p parameter for the OpenAI API. Defaults to 1.0.

        Returns:
            dict: The completion generated by the OpenAI API.
        """
        try:
            completion = self.client.chat.completions.create(
                model=self.model,
                response_format={"type": "json_object"},
                messages=[
                    {"role": "system", "content": "Provide output in valid JSON. Please be consistent with the dictionary keys. The data schema should be like this:" + json.dumps(self.example_json)},
                    {"role": "user", "content": prompt}
                ],
                temperature=temp,
                top_p=top_p,
            )

            data = completion.choices[0].message.content
            ski = json.loads(data)

            return ski

        except Exception as oops:
            print('ERROR in completion function:', oops)
            return None

