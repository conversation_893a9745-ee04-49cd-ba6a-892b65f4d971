from typing import *
from extracter.prompt_generator import PromptGenerator
from extracter.completion_client import Completion<PERSON><PERSON>
from openai import OpenAI
from exception import CustomEx<PERSON>
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from logger import logging

class ResumeProcessor:
    def __init__(self, client: OpenAI, model: str, example_json: dict) -> None:
        self.prompt_generator = PromptGenerator(example_json)
        self.completion_client = CompletionClient(client, model, example_json)
        self.example_json = example_json

    def formatl(self, user_query):
        """
        Generate a prompt based on the user's query.

        Args:
            user_query (str): The user's query for generating the prompt.

        Returns:
            str: The generated prompt.
        """    
        prompt = self.prompt_generator.generate_prompt(user_query)
        return prompt        


    def process_text_list(self, text_list: List[str], file_list: List[str]) -> List[Union[Dict[str, Any], None]]:
        """
        Process a list of text inputs and extract information from them.

        Args:
            text_list (List[str]): A list of text inputs to process.
            file_list (List[str]): A list of file names corresponding to the text inputs.

        Returns:
            List[Union[Dict[str, Any], None]]: A list of extracted information
                from the text inputs. If extraction fails for a given input, an
                empty dictionary is returned.
        """

        def process_text(text):
            prompt_example = self.formatl(user_query=text)
            retry_count = 0
            while retry_count < 3:
                result = self.completion_client.completion(prompt_example)
                if result:
                    return result
                retry_count += 1
            return {}

        try:
            information_extracted = []
            with ThreadPoolExecutor() as executor:
                futures = {executor.submit(process_text, text): (text, file) for text, file in zip(text_list, file_list)}
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        text, file_name = futures[future]
                        result['FileName'] = file_name  # Add the file name to the extracted information
                        information_extracted.append(result)
                    except Exception as e:
                        information_extracted.append({})
                        logging.error(f"Failed to extract information for text: {futures[future][0]}. Error: {e}")

            return information_extracted

        except Exception as e:
            raise CustomException(e, sys)    