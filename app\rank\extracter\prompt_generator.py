import json
from app.logger import logging

class PromptGenerator:
    def __init__(self, example_json: dict) -> None:
        """
        Initialize the PromptGenerator.

        Args:
            example_json (dict): An example JSON object to use as a template.

        Returns:
            None
        """
        # self.example_json = json.dumps(example_json)
        self.example_json = example_json

    def generate_prompt(self, user_query: str) -> str:
            return f"""
Given the extracted text from a resume which you can find inside '''{user_query}''', create a JSON object containing the following fields:
Name: Extract the candidate's name from the text.
Contact: Extract contact information such as phone number and email address.
Universities: Extract information about universities attended, including:
Name of the university.
Location (associated with each university).
Major (such as computer science, mathematics, electrical engineering etc).
Degree obtained (such as MS, BS, PhD, BA, MA, etc).
Tenure (associated with each university).
Experiences: Extract information about work experiences, including:
Company name.
Location (associated with each company).
Tenure (start and end dates).
Job title or position held.
Skills: Extract a list of skills possessed by the candidate.

Make sure the output is only a JSON object and no additional comments such as "Sure, blah blah". Ensure that the output JSON string is well-formed, adhering to the JSON syntax rules, and contains all the necessary fields.
Do not explain how, but only give the result in a json format. Recheck that the output is a Valid JSON.
            """
    
class JobPromptGenerator:
    def __init__(self, job_title_str: str) -> None:

        """
        Initialize the PromptGenerator.

        Args:
            example_json (dict): An example JSON object to use as a template.

        Returns:
            None
        """

        # self.job_description_str=job_description_str   
        self.job_title_str=job_title_str 
 
    def generate_job_title_prompt(self, job_title_str):
        logging.info(f"Job Description Title: {self.job_title_str}")
        logging.info(f"Candidate Job Title: {job_title_str}")
        
        return f"""
You are a robot that only outputs JSON. Do not generate new lines and spaces after the JSON output.
You reply in JSON format with the field 'job_title_score'.
Example answer format: {{"job_title_score": "1"}} 
Match the job title of the candidate given here {job_title_str} against a job title for a job given here {self.job_title_str}. Return the result in single quote. Use the following criteria:
2 if the candidate's any job title has a perfect match with the required job title,
1 if the candidate's any job title has a somewhat relevant match in the same field,
0 if the candidate's job title is not relevant to the job title at all (e.g., Senior Software Engineer for an HR manager position).
"""



class DatePrompt:
    def __init__(self, datelist, date_format, job_description):
        """
        Initialize the DatePrompt.

        Args:
            datelist (str): List of date ranges as a string.
            date_format (str): Example format of the output JSON.
            job_description (str): Job description string.

        Returns:
            None
        """
        self.datelist = datelist
        self.date_format = date_format
        self.job_description = job_description

    def __call__(self, user_query, input_format):
        return f"""
        You are a robot that only outputs JSON.
        You reply in JSON format with the field 'tenure'.
        Example Input: 'February 2014 - August 2018 (4 years 7 months),September 2019 - February 2022 (2 years 6 months),March 2022 - Present (2 years 2 months),'
        Example answer format: {{'tenure': "1.2"}} 

        Given the number of years of experience of a candidate in different companies mentioned here ```{user_query}``` and the output format style as in here ```{input_format}```. Calculate the total years of experience in decimal correct to 2 decimal places. Return the results in JSON format. Do not create additional space and new lines.
        """
    

class DegreeMajorPromptGenerator:
    def __init__(self, degree, major, job_description):
        self.degree = degree
        self.major = major
        self.job_description = job_description

    def generate_degree_major_prompt(self):
        return (
            f"Job Description:\n{self.job_description}\n\n"
            f"Candidate's Education:\n"
            f"Degree(s): {self.degree}\n"
            f"Major(s): {self.major}\n\n"
            f"Task: Based on the candidate's degrees and majors, determine how well their education matches the job description.\n"
            f"Use the following scoring system:\n"
            f"1 - Perfect Match: Candidate's degree and major exactly match the job requirements.\n"
            f"0.5 - Good Match: Candidate's degree and major are relevant but not an exact match.\n"
            f"0 - Not Relevant: Candidate's degree and major do not match the job requirements.\n\n"
            f"Return your answer as a JSON object in the format:\n"
            f'{{"degree_major_Score": "<1 or 0.5 or 0>"}}\n\n'
            f"Only return the JSON object and nothing else."
        )



class JobRequirementPromptGenerator:

    def __init__(self, job_description_str: str, job_title_str: str) -> None:
        """
        Initialize the PromptGenerator.

        Args:
            job_description_str (str): A string representation of the job description.

        Returns:
            None
        """
        self.job_description_str = job_description_str   
        self.job_title_str = job_title_str 

    def generate_jobdesc_requirement_prompt(self):
        return f"""
        You are a robot that only outputs JSON. Do not generate new lines or spaces after the JSON output.
        You must strictly follow this JSON format:

        {{
            "Role": Mentioned job title role or most suitable role suggestion,
            "Company": Hiring Company,
            "Location": Hiring Location (City, state, Remote, Multiple Locations or similar etc),
            "Requirements": {{
                "Skills": [1gram or bigram in skills],
                "Experience": For example 2+ years of related experience,
                "Minimum Qualification": For example MS in Computer Science,
                "Preferred Qualification": For example Phd in Computer Science
            }},
            "Responsibilities": [Maximum three key responsibilites],
            "Benefits": [Maximum three key benefits],
            "Suggested_Job_Titles": []
        }}

        A job description is given here: ```{self.job_description_str}``` and the user input job title is given here: ```{self.job_title_str}``. Please read the job description and job_title and fill in the values in the JSON format. 
        Provide up to 4 standard job titles that match the job description and job_title in the "Suggested_Job_Titles" array. 
        Examples of standard job titles include "Software Engineer", "Full Stack Developer", "Data Scientist", etc which are used in the job market. 
        For all other keys, if any value is missing in the job description, return null or an empty list ([]) for that key.
        """
