import re
import urllib.request
import spacy
from extracter.TextCleaner import Text_Cleaner
from collections import Counter

# Load the English model
nlp = spacy.load("en_core_web_sm")

RESUME_SECTIONS = [
    "Contact Information",
    "Objective",
    "Summary",
    "Education",
    "Experience",
    "Skills",
    "Projects",
    "Certifications",
    "Licenses",
    "Awards",
    "Honors",
    "Publications",
    "References",
    "Technical Skills",
    "Computer Skills",
    "Programming Languages",
    "Software Skills",
    "Soft Skills",
    "Language Skills",
    "Professional Skills",
    "Transferable Skills",
    "Work Experience",
    "Professional Experience",
    "Employment History",
    "Internship Experience",
    "Volunteer Experience",
    "Leadership Experience",
    "Research Experience",
    "Teaching Experience",
]

class AuthenticityChecker:
    """
    A class for extracting various types of data from text.
    """

    def __init__(self, raw_text: str):
        """
        Initialize the DataExtractor object.

        Args:
            raw_text (str): The raw input text.
        """
        self.text = raw_text
        cleaner = Text_Cleaner(self.text)  # Create an instance of Text_Cleaner
        self.clean_text = cleaner.clean_text()  # Call clean_text on the instance
        self.doc = nlp(self.clean_text)
        
    def extract_links(self):
        """
        Find links of any type in a given string and count specific types of links.

        Args:
            text (str): The string to search for links.

        Returns:
            tuple: A tuple containing a list of all found links, a Counter object with the counts of each type of link, and an authentication score.
        """
        link_pattern = r"\b(?:https?://|http?://|ftp://|www\.|mailto:)\S+\b"
        links = re.findall(link_pattern, self.text)
        
        # Count occurrences of each type of domain link

        if links is not None:
            domain_counts = Counter()
            for link in links:
                if link.startswith("http://"):
                    domain_counts["http"] += 1
                elif link.startswith("https://"):
                    domain_counts["https"] += 1
                elif link.startswith("ftp://"):
                    domain_counts["ftp"] += 1
                elif link.startswith("mailto:"):
                    domain_counts["email"] += 1
                elif "www.linkedin.com" in link:
                    domain_counts["linkedin"] += 1
                elif "github.com/" in link:
                    domain_counts["github"] += 1
                elif "twitter.com" in link:
                    domain_counts["twitter"] += 1

            # Define scoring weights
            scoring_weights = {
                "linkedin": 3,
                "github": 3,
                "twitter": 2,
                "email": 2,
                "https": 1,
                "http": 0.5
            }

            # Calculate the raw authentication score
            raw_score = sum(domain_counts[link_type] * weight for link_type, weight in scoring_weights.items())

            # Calculate the maximum possible score for normalization
            max_score = sum(scoring_weights.values())

            # Normalize the score to a 0-100 scale
            if max_score > 0:
                auth_score = min((raw_score / max_score) * 100, 100)
            else:
                auth_score = 0

            return links, domain_counts, round(auth_score, 2)
        

        else:
            return 0, Counter(), 0

    def get_authenticity_level(self, score):
        """
        Determine the authenticity level based on the score.

        Args:
            score (float): The authentication score.

        Returns:
            str: A string indicating the authenticity level.
        """
        if score >= 75:
            return "Authentic"
        elif score >= 50:
            return "Legit Enough"
        elif score >= 25:
            return "Okay"
        else:
            return "Suspicious"

    def extract_links_extended(self):
        """
        Extract links of all kinds (HTTP, HTTPS, FTP, email, www.linkedin.com,
          and github.com/user_name) from a webpage.

        Args:
            url (str): The URL of the webpage.

        Returns:
            list: A list containing all the extracted links.
        """
        links = []
        try:
            response = urllib.request.urlopen(self.text)
            html_content = response.read().decode("utf-8")
            pattern = r'href=[\'"]?([^\'" >]+)'
            raw_links = re.findall(pattern, html_content)
            for link in raw_links:
                if link.startswith(
                    (
                        "http://",
                        "https://",
                        "ftp://",
                        "mailto:",
                        "www.linkedin.com",
                        "github.com/",
                        "twitter.com",
                    )
                ):
                    links.append(link)
        except Exception as e:
            print(f"Error extracting links: {str(e)}")
        return links


    def extract_names(self):
        """Extracts and returns a list of names from the given
        text using spaCy's named entity recognition.

        Args:
            text (str): The text to extract names from.

        Returns:
            list: A list of strings representing the names extracted from the text.
        """
        names = [ent.text for ent in self.doc.ents if ent.label_ == "PERSON"]
        return names

    def extract_emails(self):
        """
        Extract email addresses from a given string.

        Args:
            text (str): The string from which to extract email addresses.

        Returns:
            list: A list containing all the extracted email addresses.
        """
        email_pattern = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}\b"
        emails = re.findall(email_pattern, self.text)
        return emails


    def extract_phone_numbers(self):
        """
        Extract phone numbers from a given string.

        Args:
            text (str): The string from which to extract phone numbers.

        Returns:
            list: A list containing all the extracted phone numbers.
        """
        phone_number_pattern = re.compile(r'\b\d{3,4}[-.\s]?\d{3,4}[-.\s]?\d{4,6}\b')
        # Find all phone numbers in the text
        phone_numbers = phone_number_pattern.findall(self.text)
        return phone_numbers



