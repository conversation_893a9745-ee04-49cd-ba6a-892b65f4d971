[{"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/austin-glugla-18750910a", "Location": "Portland, Oregon, United States"}, "Universities": [{"Name": "Columbia University in the City of New York", "Location": "", "Major": "", "Degree": "", "Tenure": "0 - 0"}], "Experiences": [{"Company": "Turing Completed", "Location": "Portland, Oregon Area", "Tenure": "12/2017 - 0/0", "Position": "AI Engineer"}, {"Company": "Stareable.com", "Location": "Brooklyn NY", "Tenure": "3/2017 - 2/2018", "Position": "Full Stack Developer"}, {"Company": "Portable Hats Security", "Location": "New York, NY", "Tenure": "6/2015 - 2/2017", "Position": "Backend Engineer"}], "Skills": ["TensorFlow", "Reinforcement Learning", "Deep Learning", "Cryptography", "Data Science", "Java", "JavaScript", "Python", "Linux", "Swift", "PKI", "Psychology", "Penetration Testing", "Hardware Architecture", "Machine Learning", "Artificial Intelligence"], "FileName": "ACoAABt72bcBWRrPk9y-qEhV0ICHyky_qDP8rOU"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/austin-yu-85126219b", "Location": "New York City Metropolitan Area"}, "Universities": [{"Name": "The Johns Hopkins University", "Location": "", "Major": "Computer Science - Natural Language Processing", "Degree": "Master's degree", "Tenure": "2023 - 2024"}, {"Name": "The Johns Hopkins University", "Location": "", "Major": "Computer Science and Applied Mathematics and Statistics", "Degree": "Bachelor of Science - BS", "Tenure": "2019 - 2023"}], "Experiences": [{"Company": "Strategy", "Location": "Tysons Corner, Virginia, United States", "Tenure": "8/2024 - 0/0", "Position": "AI Engineer 1 at Strategy"}, {"Company": "The Johns Hopkins University", "Location": "", "Tenure": "5/2023 - 8/2024", "Position": "Research Assistant"}, {"Company": "The Johns Hopkins University", "Location": "Baltimore, Maryland, United States", "Tenure": "1/2022 - 5/2023", "Position": "Undergraduate Research Assistant"}, {"Company": "The Johns Hopkins University", "Location": "Baltimore, Maryland, United States", "Tenure": "6/2020 - 5/2021", "Position": "Research Project Lead"}], "Skills": ["Large Language Models (LLM)", "Natural Language Processing (NLP)", "Machine Translation", "Computer Vision", "Parallel Programming", "Applied Mathematics", "Software Development", "Mobile Application Development", "Web Development", "Machine Learning", "Deep Learning", "Data Structures", "Algorithms", "Computer Science", "Java", "Git", "MySQL", "HTML", "C++", "React.js", "LaTeX", "Microsoft Office", "Python (Programming Language)", "Mandarin", "German", "OpenCV", "mongoD", "MongoDB", "MATLAB"], "FileName": "ACoAAC7YgD0BhvKIyUqZUaAsP1Bi7kXlP9aoFC0"}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/arunk<PERSON><PERSON>eni", "Location": "Austin, Texas, United States"}, "Universities": [{"Name": "The University of Texas at Austin", "Location": "", "Major": "Computer Science", "Degree": "", "Tenure": "0 - 0"}, {"Name": "Galvanize Inc", "Location": "", "Major": "Data Science", "Degree": "Data Science", "Tenure": "0 - 0"}, {"Name": "The University of Texas at Austin", "Location": "", "Major": "", "Degree": "Chemical Engineering", "Tenure": "0 - 0"}, {"Name": "Georgetown University", "Location": "", "Major": "", "Degree": "Master of Business Administration - MBA", "Tenure": "2017 - 2020"}, {"Name": "Dept Of Energy Bettis Reactor Engineering School", "Location": "", "Major": "Nuclear/Mechanical Engineering", "Degree": "Graduate Studies", "Tenure": "0 - 0"}], "Experiences": [{"Company": "Deloitte Innovation and Tech AI/ML", "Location": "Austin, Texas, United States", "Tenure": "3/2021 - 0/0", "Position": "AI/ML Product Lead & Lead Artificial Intelligence Engineer"}, {"Company": "Galvanize Inc", "Location": "Austin, Tx", "Tenure": "10/2020 - 2/2021", "Position": "Machine Learning Capstone Project"}, {"Company": "Naval Reactors (DOE - US Navy)", "Location": "Washington D.C. Metro Area", "Tenure": "3/2015 - 9/2020", "Position": "Naval Reactors Engineer"}, {"Company": "Solar Semiconductor Inc.", "Location": "San Francisco Bay Area", "Tenure": "7/2008 - 1/2012", "Position": "Principal Technical Product Manager - Tech Entrepreneur"}, {"Company": "Intel Corporation", "Location": "", "Tenure": "1/2007 - 7/2008", "Position": "Embedded Systems Software Engineer"}], "Skills": ["Large Language Models (LLM)", "Large Language Model Operations (LLMOps)", "AI Quality", "MLOPS", "NVIDIA DGX A100", "Ray Distributed Computing", "AI/ML Technical Product Management", "Kubeflow", "AWS Full Stack ML Engineering", "PyTorch", "Kubernetes", "Machine Learning", "Strategy", "Chemical Engineering", "Program Management", "Research", "Data Analysis", "Python", "C/C++", "JavaScript", "Embedded Linux", "Matlab", "SQL", "Leadership", "Public Speaking", "Management", "Customer Service", "Graph Databases", "Amazon Web Services (AWS)", "Databases", "Artificial Intelligence (AI)", "Algorithms", "PySpark", "Pandas (Software)", "<PERSON><PERSON><PERSON>", "Data Science", "Data Engineering", "Deep Learning", "Linux", "C (Programming Language)", "Programming", "Data Visualization", "Apache Spark", "Natural Language Processing (NLP)", "MapReduce", "Elasticsearch", "Distributed Systems", "Real-Time Operating Systems (RTOS)", "NumPy", "Business Intelligence (BI)"], "FileName": "ACoAAAhIWHQBT61EmD544OghA_x6ZbmmNXCeVv4"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/leonardo-a-falcon", "Location": "Austin, Texas, United States"}, "Universities": [{"Name": "The University of Texas at San Antonio", "Location": "", "Major": "Electrical Engineering", "Degree": "Bachelor's degree", "Tenure": "2018 - 2021"}], "Experiences": [{"Company": "General Motors", "Location": "Austin, Texas, United States", "Tenure": "1/2022 - 9/2024", "Position": "AI Engineer"}, {"Company": "The University of Texas at San Antonio", "Location": "San Antonio, Texas, United States", "Tenure": "1/2021 - 8/2021", "Position": "Undergraduate AI Researcher"}, {"Company": "The University of Texas at San Antonio", "Location": "San Antonio, Texas, United States", "Tenure": "1/2021 - 5/2021", "Position": "Robotics Lab Assistant"}], "Skills": ["Python (Programming Language)", "Computer Science", "PyTorch", "Computer Vision", "Robot Programming", "Robotic vision", "Research Skills", "Technical Writing", "Project Management", "Software Development", "Machine Learning", "Artificial Intelligence (AI)"], "FileName": "ACoAAEZYnn0BSEmqKY8G0ZUFsypy1QY9N8z6OMU"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/beatricelovely", "Location": "United States"}, "Universities": [{"Name": "KTH Royal Institute of Technology", "Location": "", "Major": "Machine Learning", "Degree": "Master of Science - MS", "Tenure": "2020 - 2022"}, {"Name": "University of Illinois Urbana-Champaign", "Location": "", "Major": "Machine Learning", "Degree": "Master of Science - MS", "Tenure": "2021 - 2022"}, {"Name": "KTH Royal Institute of Technology", "Location": "", "Major": "Media technology", "Degree": "Bachelor of Technology - BTech", "Tenure": "2012 - 2015"}, {"Name": "Stockholm University", "Location": "", "Major": "", "Degree": "", "Tenure": "2011 - 2011"}, {"Name": "Kungsholmens Gymnasium", "Location": "", "Major": "General education, Natural Sciences", "Degree": "", "Tenure": "2008 - 2011"}], "Experiences": [{"Company": "General Motors", "Location": "Austin, Texas, United States", "Tenure": "7/2022 - 0/0", "Position": "AI Software Engineer"}, {"Company": "University of Illinois Urbana-Champaign", "Location": "Urbana-Champaign Area", "Tenure": "12/2021 - 6/2022", "Position": "Visiting Scholar"}, {"Company": "Accenture", "Location": "Stockholm, Stockholm County, Sweden", "Tenure": "10/2021 - 1/2022", "Position": "Applied Intelligence Mentorship Programme"}, {"Company": "DigitalTolk", "Location": "Stockholm, Stockholm County, Sweden", "Tenure": "6/2021 - 7/2021", "Position": "NLP R&D Intern"}, {"Company": "Nordea", "Location": "Stockholm, Stockholm County, Sweden", "Tenure": "3/2021 - 5/2021", "Position": "Nordea Women's Finance Insight Programme"}, {"Company": "Stellar", "Location": "Stockholm, Stockholm County, Sweden", "Tenure": "11/2020 - 3/2021", "Position": "Computer Vision Rule Developer/R&D & QA"}, {"Company": "Twinkle Star Kids Parties", "Location": "Boston, New York, United States", "Tenure": "5/2014 - 2/2020", "Position": "Owner, Performer"}, {"Company": "All-Star Media International", "Location": "Boston, Massachusetts, United States", "Tenure": "3/2016 - 9/2018", "Position": "Producer"}, {"Company": "All-Star Media International", "Location": "Boston, Massachusetts, United States", "Tenure": "2/2012 - 3/2016", "Position": "Associate Producer"}], "Skills": ["Apache Airflow", "Artificial Intelligence (AI)", "Research", "Sensors", "Sensory Integration", "Television", "Film Production", "Producing", "Media & Entertainment", "Documentaries", "Machine Learning", "Data Science", "Python (Programming Language)", "PyTorch", "Natural Language Processing (NLP)", "Java", "Data Analysis", "Statistical Data Analysis", "Deep Learning", "Artificial Neural Networks", "Computer Vision", "Research and Development (R&D)", "Project Management", "Analytical Skills", "Communication", "NumPy", "Scikit-Learn"], "FileName": "ACoAAAglSWwB9RdttPMuey1d1qtkfHO939xKEAA"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/tim-yk-wang", "Location": "Austin, Texas, United States"}, "Universities": [{"Name": "University of California, San Diego", "Location": "", "Major": "Chemical Engineering", "Degree": "Master’s Degree", "Tenure": "2014 - 2015"}, {"Name": "WeCloudData", "Location": "", "Major": "Machine Learning Engineer", "Degree": "Certification", "Tenure": "2023 - 2023"}, {"Name": "University of California, San Diego", "Location": "", "Major": "NanoEngineering", "Degree": "Bachelor’s Degree", "Tenure": "2010 - 2014"}], "Experiences": [{"Company": "Overalls", "Location": "", "Tenure": "10/2024 - 4/2025", "Position": "AI Engineer"}, {"Company": "Applied Materials", "Location": "Austin, Texas, United States", "Tenure": "5/2020 - 10/2024", "Position": "Data Scientist"}, {"Company": "Beam <PERSON>", "Location": "", "Tenure": "10/2023 - 3/2024", "Position": "Machine Learning Engineer"}, {"Company": "Samsung Austin Semiconductor", "Location": "Austin, Texas Area", "Tenure": "6/2016 - 5/2020", "Position": "Equipment Engineer"}], "Skills": ["AI Agents", "<PERSON><PERSON>", "Google Cloud Platform (GCP)", "MLOps", "Full-Stack Development", "JMP", "<PERSON>er", "Linux", "Git", "<PERSON><PERSON>", "Large Language Models (LLM)", "Amazon Web Services (AWS)", "Computer Vision", "Artificial Intelligence (AI)", "ChatGPT", "Langchain", "Vector Database", "OpenCV", "Streamlit", "Data Analysis", "Critical Thinking", "Python (Programming Language)", "Machine Learning", "Data Science", "Data Visualization"], "FileName": "ACoAAA_r-08Bk_RHYm1pawY06yTWpyS9_7Wy5fY"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/dale-markowitz", "Location": "Austin, Texas, United States"}, "Universities": [{"Name": "Princeton University", "Location": "", "Major": "Computer Science", "Degree": "Bachelor’s Degree", "Tenure": "2011 - 2015"}], "Experiences": [{"Company": "", "Location": "", "Tenure": "9/2016 - 0/0", "Position": "Freelance Writer"}, {"Company": "Google", "Location": "", "Tenure": "6/2022 - 6/2024", "Position": "Staff Generative AI Developer Advocate"}, {"Company": "Google", "Location": "Austin", "Tenure": "5/2019 - 6/2022", "Position": "Sr Applied AI Engineer, Google Cloud"}, {"Company": "Google", "Location": "", "Tenure": "5/2019 - 6/2022", "Position": "Sr Developer Advocate, Google Cloud"}, {"Company": "Google", "Location": "New York, New York", "Tenure": "5/2018 - 5/2019", "Position": "Software Engineer, Google AI"}, {"Company": "OkCupid", "Location": "Greater New York City Area", "Tenure": "9/2015 - 7/2017", "Position": "Software Engineer (Data Science/Backend)"}, {"Company": "OkCupid", "Location": "Greater New York City Area", "Tenure": "9/2015 - 7/2017", "Position": "Data Scientist"}, {"Company": "Princeton Neuroscience Institute and Princeton Computer Science", "Location": "Princeton University", "Tenure": "9/2014 - 6/2015", "Position": "Student Researcher"}, {"Company": "Princeton University", "Location": "Princeton University", "Tenure": "6/2014 - 8/2014", "Position": "Computer Science Research Assistant"}, {"Company": "Floored", "Location": "NYC", "Tenure": "6/2013 - 9/2013", "Position": "Engineering Intern"}, {"Company": "ER Accelerator", "Location": "", "Tenure": "6/2012 - 8/2012", "Position": "Intern"}], "Skills": ["Java", "C", "Linux", "Python", "JavaScript", "Node.js", "C++", "Statistics", "Machine Learning", "Python (Programming Language)", "Google Cloud Platform (GCP)", "Data Science", "TensorFlow"], "FileName": "ACoAAAsubkoB1ZWGdMgWoFaR2fNeD0PKyyUWnmM"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/austin-king-957b65148", "Location": "Los Angeles, California, United States"}, "Universities": [{"Name": "University of California, Santa Cruz", "Location": "", "Major": "Natural Language Processing", "Degree": "Master of Science - MS", "Tenure": "0 - 0"}, {"Name": "University of California, Santa Cruz", "Location": "", "Major": "Computer Science", "Degree": "Bachelor of Science - BS", "Tenure": "0 - 0"}, {"Name": "University of California, Santa Cruz", "Location": "", "Major": "Computational Mathematics", "Degree": "Bachelor of Arts - BA", "Tenure": "0 - 0"}, {"Name": "Foothill College", "Location": "", "Major": "", "Degree": "", "Tenure": "0 - 0"}], "Experiences": [{"Company": "Knitit.ai", "Location": "Palo Alto, California, United States", "Tenure": "6/2024 - 0/0", "Position": "AI Engineer"}, {"Company": "Megagon Labs", "Location": "Mountain View, California, United States", "Tenure": "12/2022 - 6/2024", "Position": "Research Engineer"}, {"Company": "Megagon Labs", "Location": "Mountain View, California, United States", "Tenure": "9/2021 - 12/2022", "Position": "Software Engineer"}, {"Company": "Got It AI", "Location": "", "Tenure": "6/2021 - 9/2021", "Position": "Research Engineer"}], "Skills": ["Annotation", "MLOps", "Research Skills", "Generative AI", "Applied Machine Learning", "ChatGPT", "Data Science", "Continuous Delivery (CD)", "Continuous Integration (CI)", "<PERSON><PERSON>", "Large Language Models (LLM)", "Huggingface", "Transformers", "Engineering", "Text Classification", "Data Structures", "Git", "Artificial Intelligence (AI)", "Python (Programming Language)", "Natural Language Processing (NLP)", "Amazon Web Services (AWS)", "Docker Products", "Continuous Integration and Continuous Delivery (CI/CD)", "GitHub", "Representational State Transfer (REST)", "REST APIs", "Test Automation", "Problem Solving", "Numerical Analysis", "Advanced Algorithms", "Linear Algebra", "Interpersonal Relations", "Machine Learning"], "FileName": "ACoAACPEURMBhUx3j5pQqGddA2fWgiJb-384A5s"}, {"Name": "<PERSON><PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/evelynn-walker", "Location": "Austin, Texas, United States"}, "Universities": [{"Name": "The University of Texas at Austin", "Location": "", "Major": "Electrical and Electronics Engineering", "Degree": "Student", "Tenure": "2011 - 2016"}], "Experiences": [{"Company": "EnCharge AI", "Location": "", "Tenure": "3/2025 - 0/0", "Position": "Principle Engineer"}, {"Company": "Meta", "Location": "Austin, Texas, United States", "Tenure": "10/2024 - 2/2025", "Position": "Software Engineer"}, {"Company": "EnCharge AI", "Location": "United States", "Tenure": "4/2023 - 10/2024", "Position": "Senior Software Engineer"}, {"Company": "Cerebras Systems", "Location": "Austin, Texas, United States", "Tenure": "12/2021 - 1/2023", "Position": "Software Engineer"}, {"Company": "Centaur Technology", "Location": "Austin, Texas Area", "Tenure": "7/2016 - 12/2021", "Position": "Software Engineer"}, {"Company": "Qualcomm", "Location": "La Jolla", "Tenure": "5/2015 - 7/2015", "Position": "Software Engineer <PERSON><PERSON>"}, {"Company": "Microsoft", "Location": "Bellevue, WA", "Tenure": "5/2014 - 7/2014", "Position": "Program Manager <PERSON>n"}, {"Company": "Qualcomm", "Location": "La Jolla", "Tenure": "5/2013 - 7/2013", "Position": "Software Engineer <PERSON><PERSON>"}, {"Company": "Thomson Reuters", "Location": "", "Tenure": "6/2010 - 8/2010", "Position": "Software Tester"}], "Skills": ["C++", "Kernel Programming", "Machine Learning", "Computer Language C and Assembly", "C (Programming Language)", "Python (Programming Language)", "Object-Oriented Programming (OOP)", "C++ Programming", "<PERSON>", "Firmware", "Software Development", "Computer Hardware", "Assembly Language", "x86 Assembly", "Computer Hardware Assembly", "Convolutional Neural Networks (CNN)"], "FileName": "ACoAAAu4AJkBxy09cdYpsO_cbDHX0xftF_PTykk"}, {"Name": "<PERSON>", "Contact": {"LinkedIn": "https://www.linkedin.com/in/tanya-tiwari-tt", "Location": "Dallas, Texas, United States"}, "Universities": [{"Name": "The University of Texas at Dallas", "Location": "", "Major": "Management information system ", "Degree": "Master's degree", "Tenure": "2023 - 0"}, {"Name": "Galgotias College of Engineering and Technology", "Location": "", "Major": "Computer Science", "Degree": "Bachelor of Technology - BTech", "Tenure": "2017 - 2021"}, {"Name": "Somerville School - India", "Location": "", "Major": "", "Degree": "", "Tenure": "0 - 0"}, {"Name": "<PERSON><PERSON><PERSON> School of Management, UT Dallas", "Location": "", "Major": "Management Information Systems, General", "Degree": "Master's degree", "Tenure": "2023 - 0"}], "Experiences": [{"Company": "Austin Artificial Intelligence, Inc.", "Location": "Texas, United States", "Tenure": "10/2024 - 0/0", "Position": "Generative AI Engineer"}, {"Company": "Cognizant", "Location": "", "Tenure": "11/2021 - 8/2023", "Position": "Data and Product Analyst"}, {"Company": "<PERSON><PERSON>", "Location": "Gurugram, Haryana, India", "Tenure": "4/2021 - 10/2021", "Position": "Software Engineer"}, {"Company": "TSP - The Silicon Partners Inc", "Location": "India", "Tenure": "8/2020 - 11/2020", "Position": "Data Science Intern"}], "Skills": ["Teamwork", "Team Leadership", "Leadership", "Interpersonal Skills", "innovative", "Convolutional Neural Networks (CNN)", "<PERSON><PERSON>", "TensorFlow", "Facial Expression Recognition", "Data Augmentation", "Batch Normalization", "OpenCV", "real-time face detection", "<PERSON><PERSON>", "FER-2013 Dataset", "Scikit-Learn", "Model Training", "Data Visualization", "Tweepy", "Natural Language Processing (NLP)", "Natural Language Toolkit (NLTK)", "Apache Airflow", "AWS S3", "Apache Spark", "Microsoft SQL Server", "SEABON", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Data Collection", "Data Cleaning", "Data Preprocessing", "ETL Process", "Distributed Computing", "Distributed Systems", "Real-Time Analysis", "Feature Engineering", "Model Evaluation", "Scalable Data Processing", "Logistic Regression", "<PERSON><PERSON>", "Sentiment Polarity", "Data Analysis", "Data Engineering", "Digital Marketing", "Customer Relationship Management (CRM)", "Graduate Level", "Sales Management", "Administration", "Salesforce Lightning", "Web Components", "Account Management"], "FileName": "ACoAADC8wF0BkLQs1cy8DShayoMzIn5npG4cs7k"}]