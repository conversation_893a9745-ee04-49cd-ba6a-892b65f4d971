from app.rank.attributes.company import CompanyExtractor, CompanyRankEvaluator
from app.rank.attributes.university import UniversityExtractor, UniversityRankEvaluator
from app.rank.attributes.job_title import JobTitleExtractor
from app.rank.attributes.degree_major import DegreeMajorExtractor
from app.rank.attributes.skills import SkillExtractor_candidate, KeywordExtractor, SimilarityCalculator
from app.rank.attributes.tenure import DateExtractor, ExperienceCalculator
from app.rank.relevant_tenure import format_profiles
from app.rank.utils import JSONy
from app.rank.exception import CustomException
from app.rank.sellect_agent import *
from app.logger import logging
from typing import List, Dict

import numpy as np
from skillNer.general_params import SKILL_DB
import json
from datetime import datetime
from openai import OpenAI
import pandas as pd
import time
import subprocess
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import concurrent.futures
from sentence_transformers import SentenceTransformer, util
import sys
import os
from rapidfuzz import fuzz
import torch

model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')

def read_file(file_path):
    try:
        with open(file_path, 'r') as file:
            return file.read()
        
    except Exception as e:
        raise CustomException(e, sys)
    
def clean_information_extracted(information_extracted):
    try:
        cleaned_data = []
        for entry in information_extracted:
            if entry is not None and isinstance(entry, dict):
                cleaned_data.append(entry)
            else:
                cleaned_data.append({})
        return cleaned_data
    
    except Exception as e:
        raise CustomException(e, sys)
    
    
    

def extract_information(information_extracted, job_description):
    try:
        cleaned_data = clean_information_extracted(information_extracted)
        df_all_data_raw = pd.DataFrame(cleaned_data)

        skill_extractor = SkillExtractor_candidate(information_extracted)
        skill_extractor.extract_skills()
        skills = skill_extractor.get_skills()
        logging.info(f"Skills: {skills}")

        formatted_experiences = format_profiles(information_extracted)
        logging.info(f"Formatted Experiences: {formatted_experiences}")

        company_extractor = CompanyExtractor(information_extracted)
        company_extractor.extract_company_name()
        companies = company_extractor.get_companies()
        logging.info(f"Companies extracted using extarctV2 endpoint for all candidates: {companies}")

        job_title_extractor = JobTitleExtractor(information_extracted, job_description)
        job_title_extractor.extract_job_titles()
        job_titles = job_title_extractor.get_job_titles()
        logging.info(f"Job Titles: {job_titles}")

        university_extractor = UniversityExtractor(information_extracted)
        university_extractor.extract_universities()
        universities = university_extractor.get_universities()
        logging.info(f"Universities: {universities}")

        return df_all_data_raw, skills, formatted_experiences, companies, job_titles, universities
    
    except Exception as e:
        raise CustomException(e, sys)    


class SimilarityWithLabelCalculator:
    def __init__(self):
        """
        Optimized Similarity Calculator for CPU-based EC2 instances.
        Uses:
        - Sentence Transformer with `device='cpu'`
        - `fp16` for lower memory usage
        - `torch.no_grad()` for inference optimization
        - ThreadPoolExecutor for parallel execution (instead of ProcessPoolExecutor)
        """
        self.device = 'cpu'  # Explicitly set CPU usage
        self.model = SentenceTransformer('all-MiniLM-L6-v2', device=self.device)
        self.embedding_cache = {}

    def get_embedding(self, text):
        """Fetches embeddings with caching and reduced precision for CPU speedup."""
        if not text.strip():
            return np.zeros((384,), dtype=np.float16)
        if text in self.embedding_cache:
            return self.embedding_cache[text]

        with torch.no_grad():  # Disable gradient calculations for inference
            embedding = self.model.encode(text, convert_to_numpy=True, dtype=np.float16)
        
        self.embedding_cache[text] = embedding
        return embedding

    def get_embedding_batch(self, texts):
        """Efficient batch embedding computation with caching."""
        texts_to_compute = [t for t in texts if t not in self.embedding_cache]
        if texts_to_compute:
            with torch.no_grad():  # Disable autograd for efficiency
                embeddings = self.model.encode(texts_to_compute, convert_to_numpy=True, dtype=np.float16)
                for text, emb in zip(texts_to_compute, embeddings):
                    self.embedding_cache[text] = emb
        return [self.embedding_cache[t] for t in texts]

    def calculate_similarity(self, text1, text2):
        """Computes optimized similarity using cosine similarity and fuzzy matching."""
        emb1, emb2 = self.get_embedding(text1), self.get_embedding(text2)

        # Optimized cosine similarity with precomputed norms
        norm1, norm2 = np.linalg.norm(emb1), np.linalg.norm(emb2)
        cosine_sim = np.dot(emb1, emb2) / (norm1 * norm2 + 1e-8)

        # Use rapidfuzz (faster than fuzzywuzzy)
        fuzzy_sim = fuzz.token_sort_ratio(text1, text2) / 100.0 if cosine_sim < 0.8 else 0.0

        # Weighted score calculation
        return (0.8 * cosine_sim) + (0.2 * fuzzy_sim)


# Instantiate globally to avoid reloading the model
similarity_calculator = SimilarityWithLabelCalculator()


def process_candidate(idx, candidate_skills, recruiter_skills, recruiter_labels, recruiter_embeddings):
    """Processes a single candidate's skills in parallel using threading."""
    candidate_embeddings = similarity_calculator.get_embedding_batch(candidate_skills)

    total_score = 0
    for candidate_emb, candidate_skill in zip(candidate_embeddings, candidate_skills):
        weighted_scores = [
            similarity_calculator.calculate_similarity(candidate_skill, recruiter_skill) * (1.5 if label == 'R' else 1.1)
            for recruiter_skill, label in zip(recruiter_skills, recruiter_labels)
        ]
        total_score += max(weighted_scores, default=0.0)

    aggregate_score = total_score / len(candidate_skills) if candidate_skills else 0.0
    return {'skill_discrete': aggregate_score}


def evaluate_candidate_skills(recruiter_skills, candidate_skills_array, labels):
    """
    Evaluate candidate skills against recruiter requirements using optimized similarity computation.

    Optimizations:
    - Uses `ThreadPoolExecutor` instead of ProcessPoolExecutor to avoid process overhead
    - Precomputes embeddings for recruiters
    - Avoids redundant numpy computations
    """
    # profiler = cProfile.Profile()
    # profiler.enable()

    try:
        all_candidates_scores = []

        # Precompute recruiter embeddings
        recruiter_embeddings = similarity_calculator.get_embedding_batch(recruiter_skills)

        # Use threading for parallel execution
        with ThreadPoolExecutor() as executor:
            results = executor.map(
                lambda args: process_candidate(*args),
                [(idx, candidate_skills, recruiter_skills, labels, recruiter_embeddings)
                 for idx, candidate_skills in enumerate(candidate_skills_array)]
            )

        all_candidates_scores = list(results)

        return all_candidates_scores

    except Exception as e:
        logging.error(f"Error evaluating candidate skills: {e}")
        return []

def extract_skills_and_labels_from_skills_preference(job_description):
    """
    Extract skills and their corresponding labels (R or P) from the SkillsPreference key in the job description.

    Parameters:
        job_description (dict): The job description containing SkillsPreference.

    Returns:
        tuple: A tuple containing two lists - skills and labels.
    """
    try:
        skills_preference = job_description.get("SkillsPreference", [])
        skills = [item["name"] for item in skills_preference if "name" in item]
        labels = [item["status"] for item in skills_preference if "status" in item]
        return skills, labels
    except Exception as e:
        logging.error(f"Error extracting skills and labels: {e}")
        return [], []



# v2
def company_preference(companies, evaluator, company_preferences):
    logging.info(f"Companies: {companies} and company preferences: {company_preferences}")

    # Convert company_preferences keys to lowercase
    company_preferences = {k.lower(): v for k, v in company_preferences.items()}

    company_values = []
    
    for company_list in companies:
        if company_list:  # Ensure the list is not empty
            total_score = 0
            
            # Check if `DNH` with `blackListCE` exists
            dnh_blacklist = any(
                evaluator.adjust_score_preference(company.lower(), company_preferences) == -2 
                and isinstance(company_preferences.get(company.lower(), {}), dict) 
                and company_preferences[company.lower()].get("hire_label") == "DNH" 
                and company_preferences[company.lower()].get("blackListCE", False)
                for company in company_list
            )

            # If `DNH` with `blackListCE` is True, only consider the most recent company
            if dnh_blacklist:
                most_recent_company = company_list[0]  # Assuming the list is sorted by recency
                score = evaluator.adjust_score_preference(most_recent_company.lower(), company_preferences)
                if score is not None:
                    total_score += score  # Use the score of the most recent company only
            else:
                # Process all companies as usual
                for company_name in company_list:
                    normalized_company = company_name.lower()
                    score = evaluator.adjust_score_preference(normalized_company, company_preferences)
                    if score is not None:
                        total_score += score  # Aggregate the scores for all companies
            
            # Append the cumulative score for all companies in the list
            company_values.append({'Company_discrete': total_score})
    
    return company_values if company_values else None  # Explicit None return





def evaluate_company_rankings(companies: List[List[str]], csv_file: str) -> List[Dict[str, int]]:
    """
    Evaluates company rankings based on existing rank data and job-specific preferences.

    Args:
        companies (List[List[str]]): List of companies associated with candidates.
        csv_file (str): CSV file containing company ranking data.
        JobID (str): Job ID to fetch preferences.

    Returns:
        List[Dict[str, int]]: Final ranking values, incorporating discrete scores and preferences.
    """
    try:
        evaluator = CompanyRankEvaluator(csv_file)
        company_ranks = evaluator.evaluate_company_rank(companies)
        # company_preferences = evaluator.query_company_preferences(JobID)

        # Fetch preference values if preferences exist
        # company_preference_values = (
        #     company_preference(companies, evaluator, company_preferences) if company_preferences else []
        # )

        company_values = evaluator.evaluate_company_values(company_ranks)

        # If preferences are missing, return company values directly
        # if not company_preference_values:
        #     return company_values

        # Combine scores using `zip()`
        # combined_values = [
        #     {'Company_discrete': cv['Company_discrete'] + cpv['Company_discrete']}
        #     for cv, cpv in zip(company_values, company_preference_values)
        # ]

        return company_values

    except Exception as e:
        logging.error(f"Error in evaluate_company_rankings: {e}", exc_info=True)
        raise CustomException(e, sys)



def evaluate_university_rankings(universities, csv_file):
    try:
        evaluator = UniversityRankEvaluator(csv_file)
        university_ranks = evaluator.evaluate_university_rank(universities)
        university_values = evaluator.evaluate_university_values(university_ranks)
        return university_values

    except Exception as e:
        raise CustomException(e, sys)
    



# v2
def extract_job_titles_and_scores(recruiter_job_titles, job_titles):
    """
    Extract the highest job title similarity scores for candidates based on recruiter job titles.
    """
    try:
        logging.info("Starting job title extraction process.")
        role_list = []

        def process_job_title(job_titles_subset):
            """
            Process a subset of candidate job titles to compute similarity with recruiter titles.
            """
            logging.info(f"Processing job titles: {job_titles_subset}")
            try:
                # Encode recruiter and candidate job titles
                recruiter_embeddings = model.encode(recruiter_job_titles)
                candidate_embeddings = model.encode(job_titles_subset)

                if recruiter_embeddings is None or candidate_embeddings is None:
                    raise ValueError("Embeddings generation failed.")

                # Compute cosine similarity between all recruiter and candidate embeddings
                cosine_scores = util.pytorch_cos_sim(recruiter_embeddings, candidate_embeddings)

                logging.info(f"Cosine Scores Shape: {cosine_scores.shape}")

                # Find the highest score for all permutations
                max_score = torch.max(cosine_scores).item()
                logging.info(f"Max similarity score: {max_score}")
                return {'job_title_score': float(max_score)}

            except Exception as e:
                logging.error(f"Error in process_job_title: {e}")
                return {'job_title_score': 0}

        # Process each candidate's job titles
        for candidate_jobs in job_titles:
            # Skip invalid or empty candidate job lists
            if not candidate_jobs or not isinstance(candidate_jobs, str):
                logging.warning(f"Invalid candidate_jobs: {candidate_jobs}")
                continue

            # Split and clean job titles
            job_title_list = candidate_jobs.split(',')
            recent_job_titles = [title.strip() for title in job_title_list if title.strip()]

            if not recent_job_titles:
                logging.warning(f"No valid job titles found in: {candidate_jobs}")
                continue

            try:
                # Use ThreadPoolExecutor for parallel processing
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(process_job_title, recent_job_titles)
                    result = future.result()
                    role_list.append(result)
            except Exception as e:
                logging.error(f"Error in thread execution: {e}")
                continue

        logging.info(f"Final job title similarity list: {role_list}")
        return role_list

    except Exception as e:
        logging.error(f"Error in extract_job_titles: {e}")
        raise




def extract_job_titles_old_using_LLM(client, job_titles, job_description, file_save_path):
    try:
        info_file = f"{file_save_path}/information_extracted.json"
        information_extracted = JSONy.load_information_extracted(info_file)
        job_title_extracted = []

        # Initialize JobTitleExtractor
        job_client = JobTitleExtractor(information_extracted, job_description)

        # Function to process each job title extraction
        def process_job_title(user_query):
            prompt_example = job_client.job_format(user_query)
            result = job_client.job_title_completion(client, prompt_example, "gpt-3.5-turbo-1106")
            return JSONy.print_json_info(result[0])

        # Use ThreadPoolExecutor for concurrent processing
        with concurrent.futures.ThreadPoolExecutor() as executor:
            # List to store futures in order
            future_to_query = {executor.submit(process_job_title, user_query): user_query for user_query in job_titles}
            
            # Iterate through completed futures in the order they were started
            for future in concurrent.futures.as_completed(future_to_query):
                user_query = future_to_query[future]
                try:
                    job_title_extracted.append(future.result())
                except Exception as e:
                    logging.error(f"Error extracting job title for query '{user_query}': {e}")
        logging.info(f"sumit testing for job_title_extracted {job_title_extracted}")            
        return job_title_extracted
    
    except Exception as e:
        raise CustomException(e, sys)



def calculate_tenure(client, dates_list, job_description):
    try:
        format_string = "{'tenure': '9.25'}"
        calculator = ExperienceCalculator(model="gpt-3.5-turbo-1106", datelist=dates_list, date_format=format_string, job_description=job_description, client=client)
        tenure_value_list = []

        # Function to process each tenure calculation
        def process_tenure(tenure):
            try:
                processed_date = calculator.process_dates([tenure], format_string)[0]
                print("tenure: ", processed_date)
                level = calculator.experience_level(processed_date)
                return {"Tenure_discrete": level}
            except Exception as e:
                logging.error(f"Error calculating tenure for '{tenure}': {e}")
                return None  
        
        # Use ThreadPoolExecutor for concurrent processing
        with concurrent.futures.ThreadPoolExecutor() as executor:
            # List to store futures in order
            futures = [executor.submit(process_tenure, tenure) for tenure in dates_list]
            
            # Iterate through futures in the order they were submitted
            for future in futures:
                try:
                    result = future.result()
                    if result is not None:
                        tenure_value_list.append(result)
                except Exception as e:
                    logging.error(f"Error retrieving result: {e}")

        # logging.info(f"Tenure value list: {tenure_value_list}")
        return tenure_value_list
    
    except Exception as e:
        raise CustomException(e, sys)
    
def extract_degree_major(client, major_list, degree_list, job_description):

    degree_major_extractor = DegreeMajorExtractor(model="gpt-3.5-turbo-1106", client=client)
    degree_major_extracted = degree_major_extractor.extract_degree_major(major_list, degree_list, job_description)
    logging.info(f"Degree major extracted: {degree_major_extracted}")
    return degree_major_extracted


def extract_linkedin_urls_from_pdfs(directory):
    linkedin_urls = []

    # Regular expression to match LinkedIn URLs
    linkedin_pattern = re.compile(r'https://www\.linkedin\.com/in/[^\s]+')

    for filename in os.listdir(directory):
        if filename.endswith('.pdf'):
            file_path = os.path.join(directory, filename)

            try:
                # Extract printable strings from the PDF using the `strings` command
                result = subprocess.run(['strings', file_path], capture_output=True, text=True)
                text = result.stdout

                urls = linkedin_pattern.findall(text)
                for url in urls:
                    # Extract LinkedIn username and format it
                    username = url.split('/')[-1]
                    output_filename = f"{username}_text.txt"
                    linkedin_urls.append(output_filename)

            except subprocess.CalledProcessError as e:
                print(f"Error processing {file_path}: {e}")

    return linkedin_urls


