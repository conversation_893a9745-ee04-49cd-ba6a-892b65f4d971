import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parents[1]))

import nltk
# nltk.download('punkt_tab')
# nltk.download('punkt')
# nltk.download('wordnet')
# nltk.download('omw-1.4')
from nltk.corpus import stopwords

# from nltk.tokenize import word_tokenize
# from nltk.stem import PorterStemmer
# import pdfplumber
import re

import ast
import uuid
import shutil
import pytz
import pickle
from urllib.parse import urlparse, parse_qs
from openai import OpenAI
import numpy as np
import pandas as pd
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.database import SessionLocal
from app.db.db_models import JobDescription, RankingResult

from app.logger import logging
import json
import time
from app.rank.pain import read_file, extract_information, evaluate_company_rankings, evaluate_university_rankings, calculate_tenure, extract_degree_major, extract_job_titles_and_scores, extract_skills_and_labels_from_skills_preference, evaluate_candidate_skills
from app.rank.relevant_tenure import calculate_tenure_hi
from app.rank.attributes.degree_major import process_information
from app.rank.sellect_agent import get_agent_info, solve_sellect_agent
from app.rank.utils import solve_select_agent_new, min_max_scaling
from sklearn.preprocessing import MinMaxScaler



BASE_DIR = Path(__file__).parent
CompanyMapping = str(BASE_DIR / "data/mapping/companies.csv")
UniversityMapping = str(BASE_DIR / "data/mapping/2023 QS World University Rankings.csv")
api_key="********************************************************************************************************************************************************************"
client = OpenAI(api_key=api_key)

# def preprocess_text(text):
#     # Convert text to lowercase and remove non-alphanumeric characters
#     text = ' '.join(word for word in text.split() if word.isalnum()).lower()
#     stop_words = set(stopwords.words('english'))
#     word_tokens = word_tokenize(text)
#     filtered_text = [word for word in word_tokens if word not in stop_words]
#     ps = PorterStemmer()
#     stemmed_text = [ps.stem(word) for word in filtered_text]
#     preprocessed_text = ' '.join(stemmed_text)
#     return preprocessed_text

def preprocess_text(text):
    # Convert to lowercase and remove non-alphanumeric characters (except spaces)
    text = text.lower()
    text = re.sub(r'[^a-z0-9\s]', '', text)

    # Split into tokens (words)
    tokens = text.split()

    # Remove stopwords
    stop_words = set(stopwords.words('english'))
    filtered_tokens = [word for word in tokens if word not in stop_words]

    # Join the cleaned tokens
    preprocessed_text = ' '.join(filtered_tokens)
    return preprocessed_text

def convert_numpy_to_json(obj):
    """
    Convert numpy types to JSON-serializable types.
    """
    if isinstance(obj, np.integer) or isinstance(obj, np.int64) or isinstance(obj, np.int32):
        return int(obj)
    elif isinstance(obj, np.floating) or isinstance(obj, np.float32) or isinstance(obj, np.float64):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_to_json(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_to_json(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_to_json(item) for item in obj)
    elif isinstance(obj, pd.DataFrame):
        return obj.to_dict('records')
    elif isinstance(obj, pd.Series):
        return obj.tolist()
    else:
        return obj

class JSONy:
    def save_information_extracted(information_extracted, file_path):
        with open(file_path, 'w') as file:
            json.dump(information_extracted, file)

    def load_information_extracted(file_path):
        with open(file_path, 'r') as file:
            return json.load(file)

def merge_resume_with_ranking(ranking_df, resume_df):
    # Merge the dataframes on the 'FileName' and 'Just_file_name' columns
    merged_df = pd.merge(ranking_df, resume_df, left_on='FileName', right_on='Just_file_name', how='left')

    # Drop unnecessary columns from resume_df that are already in ranking_df
    merged_df = merged_df.drop(columns=['index', 'Just_file_name', 'name'])  # Keep only relevant columns

    return merged_df

def safe_convert_to_float(value, default=0.0):
    """Safely convert a value to float, handling strings like 'N/A'."""
    if value is None or value == '' or value == 'N/A':
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_convert_to_int(value, default=0):
    """Safely convert a value to int, handling strings like 'N/A'."""
    if value is None or value == '' or value == 'N/A':
        return default
    try:
        return int(float(value))  # Convert to float first to handle decimal strings
    except (ValueError, TypeError):
        return default

def save_ranking_results_to_new_table(job_id: str, merged_df: pd.DataFrame, processing_time: float):
    """
    Save ranking results to the new table structure with individual columns.
    Adds a new_ranked_index column based on descending Scores.
    Skips insertion if data for the job_id already exists.
    """
    try:
        db = SessionLocal()
        # Check if data already exists for this job_id
        existing_count = db.execute(
            text("SELECT COUNT(*) FROM ranking_results WHERE job_id = :job_id"),
            {'job_id': job_id}
        ).scalar()
        if existing_count > 0:
            print(f"[INFO] Ranking results already exist for job_id {job_id}. Skipping insertion.")
            return False
        # Prepare LinkedIn URL mapping
        linkedin_urls = {}
        for _, row in merged_df.iterrows():
            file_name = row.get('FileName', '')
            linkedin_url = row.get('Extracted_LinkedIn_URLs', 'Not available')
            linkedin_urls[file_name] = linkedin_url
        
        # --- Add new_ranked_index based on descending Scores ---
        merged_df = merged_df.copy()
        merged_df['new_ranked_index'] = merged_df['Scores'].rank(method='first', ascending=False).astype(int) - 1
        
        # --- Score-to-candidate mapping check ---
        ranked_indices = merged_df['New_ranked_Index'].tolist()
        if len(set(ranked_indices)) != len(ranked_indices):
            print("[WARNING] Duplicate ranked indices found in merged_df! This may indicate a bug in ranking logic.")
        print("\n[DEBUG] Candidate ranking order:")
        for idx, row in merged_df.iterrows():
            print(f"Rank {row.get('New_ranked_Index')} (new_ranked_index {row.get('new_ranked_index')}): {row.get('Name')} (File: {row.get('FileName')}) - Score: {row.get('Scores')}")
        
        # Insert each candidate's ranking data
        for _, row in merged_df.iterrows():
            file_name = row.get('FileName', '')
            name = row.get('Name', '')
            scores = safe_convert_to_float(row.get('Scores', 0.0))
            ranked_index = safe_convert_to_int(row.get('New_ranked_Index', 0))
            new_ranked_index = safe_convert_to_int(row.get('new_ranked_index', 0))
            tenure_discrete = str(row.get('Tenure_discrete', ''))  # Keep as string
            company_discrete = safe_convert_to_int(row.get('Company_discrete', 0))
            degree_major_discrete = safe_convert_to_float(row.get('degree_major_score', 0.0))
            job_title_discrete = safe_convert_to_float(row.get('Job_Title_discrete', 0.0))
            skills_discrete = safe_convert_to_float(row.get('Skills_discrete', 0.0))
            university_discrete = safe_convert_to_float(row.get('university_discrete', 0.0))
            linkedin_url = linkedin_urls.get(file_name, 'Not available')
            
            # Debug print for problematic values
            print(f"[DEBUG] Processing {name}: degree_major_score={row.get('degree_major_score')} -> {degree_major_discrete}")
            
            insert_query = text("""
                INSERT INTO ranking_results (
                    job_id, linkedin_url, name, file_name, scores, ranked_index,
                    tenure_discrete, company_discrete, degree_major_discrete,
                    job_title_discrete, skills_discrete, university_discrete, new_ranked_index
                ) VALUES (
                    :job_id, :linkedin_url, :name, :file_name, :scores, :ranked_index,
                    :tenure_discrete, :company_discrete, :degree_major_discrete,
                    :job_title_discrete, :skills_discrete, :university_discrete, :new_ranked_index
                )
            """)
            db.execute(insert_query, {
                'job_id': job_id,
                'linkedin_url': linkedin_url,
                'name': name,
                'file_name': file_name,
                'scores': scores,
                'ranked_index': ranked_index,
                'tenure_discrete': tenure_discrete,
                'company_discrete': company_discrete,
                'degree_major_discrete': degree_major_discrete,
                'job_title_discrete': job_title_discrete,
                'skills_discrete': skills_discrete,
                'university_discrete': university_discrete,
                'new_ranked_index': new_ranked_index
            })
        db.commit()
        logging.info(f"Ranking results saved to new table for job_id: {job_id}")
        return True
    except Exception as e:
        logging.error(f"Error saving ranking results to new table: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def save_ranking_results_to_db(job_id: str, result_data: dict, processing_time: float):
    """
    Save ranking results to the database.
    
    Args:
        job_id (str): UUID of the job description
        result_data (dict): Results from extract_info function
        processing_time (float): Time taken to process in seconds
    """
    try:
        db = SessionLocal()
        
        # Prepare candidate scores breakdown
        candidate_scores = {
            'skills': convert_numpy_to_json(result_data['skill_list']),
            'job_titles': convert_numpy_to_json(result_data['job_title_extracted']),
            'companies': convert_numpy_to_json(result_data['company_values']),
            'tenure': convert_numpy_to_json(result_data['tenure_value_list']),
            'universities': convert_numpy_to_json(result_data['university_values']),
            'degree_major': convert_numpy_to_json(result_data['degree_major_extracted']),
            'overall_scores': convert_numpy_to_json(result_data['scores']),
            'scaled_scores': convert_numpy_to_json(result_data['scaled_scores'])
        }
        
        # Prepare intermediate results (optional, for debugging/analysis)
        intermediate_results = {
            'skills_raw': convert_numpy_to_json(result_data['skills']),
            'formatted_experiences': convert_numpy_to_json(result_data['formatted_experiences']),
            'companies_raw': convert_numpy_to_json(result_data['companies']),
            'job_titles_raw': convert_numpy_to_json(result_data['job_titles']),
            'universities_raw': convert_numpy_to_json(result_data['universities']),
            'all_data_raw': convert_numpy_to_json(result_data['all_data_raw_df']),
            'discrete_candidates': convert_numpy_to_json(result_data['discrete_candidates_df']),
            'sorted_candidates': convert_numpy_to_json(result_data['sorted_candidates'])
        }
        
        # Convert all data to JSON-serializable format
        ranking_result = RankingResult(
            job_id=job_id,
            final_rankings_json=convert_numpy_to_json(result_data['merged_df']),
            candidate_scores_json=candidate_scores,
            intermediate_results_json=intermediate_results,
            total_candidates=len(result_data['merged_df']) if result_data['merged_df'] is not None else 0,
            processing_time_seconds=processing_time
        )
        
        db.add(ranking_result)
        db.commit()
        db.refresh(ranking_result)
        
        logging.info(f"Ranking results saved to database with ID: {ranking_result.id}")
        return ranking_result.id
        
    except Exception as e:
        logging.error(f"Error saving ranking results to database: {e}")
        db.rollback()
        raise
    finally:
        db.close()

def pad_list_to_length(lst, target_len, default):
    """Pad lst to target_len with default value if needed."""
    if len(lst) < target_len:
        return lst + [default] * (target_len - len(lst))
    return lst

def extract_info(job_id: str, save_to_db: bool = True):
    """
    Modified extract_info function to work with database instead of files.
    
    Args:
        job_id (str): UUID of the job description to process
        save_to_db (bool): Whether to save results to database (default: True)
        
    Returns:
        dict: Contains the final merged dataframe and intermediate results
    """
    start_time = time.time()
    
    try:
        # Get database session
        db = SessionLocal()
        
        # Fetch job description from database
        job_description_record = db.query(JobDescription).filter(JobDescription.id == job_id).first()
        if not job_description_record:
            raise ValueError(f"Job description with id {job_id} not found")
        
        # Extract data from database
        JD = job_description_record.jd_text
        information_extracted = job_description_record.information_extracted_json
        
        if not information_extracted:
            raise ValueError(f"No information_extracted_json found for job_id {job_id}")
        
        # Create temporary directory for intermediate files (if needed)
        base_dir = Path(__file__).parent / "output_profiles"
        base_dir.mkdir(parents=True, exist_ok=True)
        meta_folder_path = base_dir / 'meta'
        meta_folder_path.mkdir(parents=True, exist_ok=True)

        job_description_text = json.dumps(JD)
        job_description = preprocess_text(job_description_text)

        # ✅ Filter out bad entries (non-dictionaries)
        information_extracted = [entry for entry in information_extracted if isinstance(entry, dict)]

        df_all_data_raw, skills, formatted_experiences, companies, job_titles, universities = extract_information(
            information_extracted, job_description
        )

        candidate_filename = np.array([entry.get("FileName", "") for entry in information_extracted])

        try:
            recruiter_skills = [skill.get('name') for skill in JD.get('SkillsPreference', []) if skill.get('name')]
            if not recruiter_skills:
                raise ValueError("SkillsPreference is empty or missing.")
        except (KeyError, TypeError, ValueError) as e:
            logging.warning(f"Failed to extract skills from SkillsPreference: {e}")
            recruiter_skills = JD.get('Requirements', {}).get('Skills', [])
            logging.info("Fallback to skills from Requirements.")

        logging.info(f"Skills required by job description: {recruiter_skills}")

        recruiter_skills, labels = extract_skills_and_labels_from_skills_preference(JD)
        logging.info(f"labels for the skills: {labels}")
        skill_list = evaluate_candidate_skills(recruiter_skills, skills, labels)
        logging.info(f"Skill list: {skill_list}")

        all_recruiter_job_titles = []

        # ✅ Also check JD['Role']
        role_in_jd = JD.get('Role')
        if isinstance(role_in_jd, str) and role_in_jd.strip():
            all_recruiter_job_titles.append(role_in_jd.strip())

        # ✅ Suggested titles
        suggested_titles = JD.get('Suggested_Job_Titles', [])
        if isinstance(suggested_titles, str):
            all_recruiter_job_titles.extend([title.strip() for title in suggested_titles.split(',')])
        elif isinstance(suggested_titles, list):
            all_recruiter_job_titles.extend([title.strip() for title in suggested_titles if isinstance(title, str)])
        else:
            logging.warning(f"Unexpected type for Suggested_Job_Titles: {type(suggested_titles)}")

        all_recruiter_job_titles = list(set(all_recruiter_job_titles))
        logging.info(f"all_recruiter_job_titles: {all_recruiter_job_titles}")

        job_title_extracted = extract_job_titles_and_scores(all_recruiter_job_titles, job_titles)
        logging.info(f"Job title extracted: {job_title_extracted}")

        company_values = evaluate_company_rankings(companies, CompanyMapping)
        logging.info(f"Company values: {company_values}")

        tenure_value_list = calculate_tenure_hi(formatted_experiences, all_recruiter_job_titles)
        logging.info(f"Tenure value list: {tenure_value_list}")

        university_values = evaluate_university_rankings(universities, UniversityMapping)
        logging.info(f"University values: {university_values}")

        major_list, degree_list = process_information(information_extracted)
        logging.info(f"Major list: {major_list}")
        logging.info(f"Degree list: {degree_list}")

        min_qualification = JD.get('Requirements', {}).get('Minimum Qualification', '')
        preferred_qualification = JD.get('Requirements', {}).get('Preferred Qualification', '')

        if min_qualification and preferred_qualification:
            recruiter_degree_major = f"{min_qualification} or {preferred_qualification}"
        elif min_qualification:
            recruiter_degree_major = min_qualification
        elif preferred_qualification:
            recruiter_degree_major = preferred_qualification
        else:
            recruiter_degree_major = "No specific qualifications listed"
        logging.info(f"Recruiter degree major: {recruiter_degree_major}")

        degree_major_extracted = extract_degree_major(client, major_list, degree_list, recruiter_degree_major)

        # Debug logging for degree_major_extracted
        logging.info(f"Degree major extracted sample: {degree_major_extracted[:3] if degree_major_extracted else 'None'}")
        for i, dm in enumerate(degree_major_extracted[:5]):  # Log first 5 entries
            if dm and isinstance(dm, dict):
                logging.info(f"Entry {i}: {dm}")
            else:
                logging.info(f"Entry {i}: {dm} (type: {type(dm)})")

        names = [item.get('Name') if isinstance(item, dict) else None for item in information_extracted]

        max_len = max(
            len(names), len(candidate_filename), len(university_values),
            len(company_values), len(tenure_value_list),
            len(job_title_extracted), len(skill_list), len(degree_major_extracted)
        )

        names = pad_list_to_length(names, max_len, None)
        candidate_filename = pad_list_to_length(candidate_filename, max_len, "")
        university_values = pad_list_to_length(university_values, max_len, {'university_discrete': 0})
        company_values = pad_list_to_length(company_values, max_len, {'Company_discrete': 0})
        tenure_value_list = pad_list_to_length(tenure_value_list, max_len, {'Tenure_discrete': 0})
        job_title_extracted = pad_list_to_length(job_title_extracted, max_len, {'job_title_score': 0})
        skill_list = pad_list_to_length(skill_list, max_len, {'skill_discrete': 0})
        degree_major_extracted = pad_list_to_length(degree_major_extracted, max_len, {'degree_major_Score': 0})

        expected_length = len(names)
        print(f"Expected length: {expected_length}")

        min_len = min(
            len(names), len(candidate_filename), len(university_values),
            len(company_values), len(tenure_value_list),
            len(job_title_extracted), len(skill_list), len(degree_major_extracted)
        )

        # Truncate all to min_len
        names = names[:min_len]
        candidate_filename = candidate_filename[:min_len]
        university_values = university_values[:min_len]
        company_values = company_values[:min_len]
        tenure_value_list = tenure_value_list[:min_len]
        job_title_extracted = job_title_extracted[:min_len]
        skill_list = skill_list[:min_len]
        degree_major_extracted = degree_major_extracted[:min_len]

        # Ensure candidate_filename is a list
        if not isinstance(candidate_filename, list):
            candidate_filename = list(candidate_filename)

        # Debug print for all lengths
        print(f"Lengths after truncation:")
        print(f"names: {len(names)}")
        print(f"candidate_filename: {len(candidate_filename)}")
        print(f"university_values: {len(university_values)}")
        print(f"company_values: {len(company_values)}")
        print(f"tenure_value_list: {len(tenure_value_list)}")
        print(f"job_title_extracted: {len(job_title_extracted)}")
        print(f"skill_list: {len(skill_list)}")
        print(f"degree_major_extracted: {len(degree_major_extracted)}")

        lengths = [
            len(names), len(candidate_filename), len(university_values),
            len(company_values), len(tenure_value_list),
            len(job_title_extracted), len(skill_list), len(degree_major_extracted)
        ]
        if len(set(lengths)) != 1:
            raise ValueError(f"Mismatch in lengths after truncation: {lengths}")

        resume_df_full = pd.DataFrame.from_records(
            list(zip(
                names, candidate_filename,
                [u.get('university_discrete', 0) for u in university_values],
                [c.get('Company_discrete', 0) for c in company_values],
                [d.get('Tenure_discrete', 0) for d in tenure_value_list],
                [j.get('job_title_score', 0) for j in job_title_extracted],
                [s.get('skill_discrete', 0) for s in skill_list],
                [safe_convert_to_float(dm.get('degree_major_Score', 0) if dm and isinstance(dm, dict) else 0) for dm in degree_major_extracted]
            )),
            columns=[
                'name', 'Just_file_name', 'university_discrete', 'Company_discrete',
                'Tenure_discrete', 'Job_Title_discrete',
                'Skills_discrete', 'degree_major_score'
            ]
        )

        resume_df_full['index'] = resume_df_full.index
        resume_df_full = resume_df_full[
            ['index', 'Just_file_name', 'name', 'university_discrete', 'Company_discrete',
             'Tenure_discrete', 'Job_Title_discrete', 'Skills_discrete', 'degree_major_score']
        ]

        # Save discrete candidates to CSV (for compatibility with existing code)
        csv_file_path = str(meta_folder_path / 'discrete_candidates.csv')
        resume_df_full.drop(columns=['name']).to_csv(csv_file_path, index=False, encoding='utf-8')

        state = get_agent_info(csv_file_path)
        learned_weights = np.array([10, 6, 16, 43, 20, 5])
        print(f"Learned weights: {learned_weights}")

        optimal_action = solve_sellect_agent(learned_weights, state)
        sorted_candidates, states_with_candidates, scores = solve_select_agent_new(learned_weights, state)

        indices, score_values = zip(*sorted_candidates)
        rearranged_scores = np.zeros(len(scores), dtype=float)
        rearranged_scores[np.array(indices, dtype=int)] = np.array(score_values)

        scaler = MinMaxScaler(feature_range=(0, 1))
        scaled_agrregated_scores = scaler.fit_transform(np.array(scores).reshape(-1, 1)).flatten()

        df_states_with_candidates = pd.DataFrame(states_with_candidates)
        original_indices = df_states_with_candidates['original_index'].astype(int)

        result_df = resume_df_full.iloc[original_indices][['name']].reset_index()
        result_df.rename(columns={'index': 'original_index'}, inplace=True)

        df = df_all_data_raw.copy()
        for index, row in result_df.iterrows():
            df.at[row['original_index'], 'original_index'] = index

        rows_with_zero = df[df['original_index'] == 0]
        sorted_df = df[df['original_index'] != 0].sort_values(by='original_index')
        sorted_df = pd.concat([rows_with_zero, sorted_df])
        sorted_df['Scores'] = scaled_agrregated_scores
        sorted_df['aggregate_weighted_score'] = scores

        nan_threshold = 8
        nan_counts = df.isnull().sum(axis=1)
        rows_to_move = df[nan_counts > nan_threshold]
        sorted_df.drop(rows_to_move.index, inplace=True)
        df_new = pd.concat([sorted_df, rows_to_move])

        sorted_indices = [int(candidate[0]) for candidate in sorted_candidates]
        if len(sorted_indices) == len(df_new):
            df_new['New_ranked_Index'] = sorted_indices
        else:
            raise ValueError("Length of sorted indices does not match number of rows in DataFrame")

        # ✅ Safe LinkedIn URL mapping
        linkedin_urls = {
            entry.get('FileName'): entry.get('Contact', {}).get('LinkedIn', 'Not available')
            for entry in information_extracted
            if isinstance(entry, dict) and isinstance(entry.get('Contact', {}), dict)
        }

        df_new['Extracted_LinkedIn_URLs'] = df_new['FileName'].map(linkedin_urls)
        merged_df = merge_resume_with_ranking(df_new, resume_df_full)

        # Save final results to CSV (for compatibility)
        csv_file_path = str(base_dir / 'resume_df_with_score.csv')
        merged_df.to_csv(csv_file_path, index=False, encoding='utf-8')
        
        print(merged_df)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Prepare return data with all intermediate results
        result_data = {
            'merged_df': merged_df,
            'discrete_candidates_df': resume_df_full,
            'all_data_raw_df': df_all_data_raw,
            'skills': skills,
            'formatted_experiences': formatted_experiences,
            'companies': companies,
            'job_titles': job_titles,
            'universities': universities,
            'skill_list': skill_list,
            'job_title_extracted': job_title_extracted,
            'company_values': company_values,
            'tenure_value_list': tenure_value_list,
            'university_values': university_values,
            'degree_major_extracted': degree_major_extracted,
            'scores': scores,
            'scaled_scores': scaled_agrregated_scores,
            'sorted_candidates': sorted_candidates
        }
        
        # Save to database if requested
        if save_to_db:
            save_ranking_results_to_new_table(job_id, merged_df, processing_time)
        return result_data

    except Exception as e:
        logging.error(f"Exception in extract_info: {e}")
        raise
    finally:
        db.close()

def test_table_insertion(job_id: str):
    """
    Test function to verify table insertion works correctly.
    
    Args:
        job_id (str): UUID of the job description to test
    """
    try:
        # Get database session
        db = SessionLocal()
        
        # Check if job description exists
        job_description_record = db.query(JobDescription).filter(JobDescription.id == job_id).first()
        if not job_description_record:
            print(f"Job description with id {job_id} not found")
            return False
        
        print(f"Found job description: {str(job_description_record.jd_text)[:100]}...")
        
        # Check if information_extracted_json exists
        if not job_description_record.information_extracted_json:
            print("No information_extracted_json found")
            return False
        
        print(f"Found {len(job_description_record.information_extracted_json)} candidate records")
        
        # Run the extract_info function
        results = extract_info(job_id, save_to_db=True)
        
        # Verify the merged_df structure
        merged_df = results['merged_df']
        print(f"\nMerged DataFrame columns: {list(merged_df.columns)}")
        print(f"Number of candidates: {len(merged_df)}")
        
        # Show sample data
        if len(merged_df) > 0:
            print("\nSample candidate data:")
            sample_row = merged_df.iloc[0]
            print(f"Name: {sample_row.get('Name', 'N/A')}")
            print(f"FileName: {sample_row.get('FileName', 'N/A')}")
            print(f"Scores: {sample_row.get('Scores', 'N/A')}")
            print(f"Ranked Index: {sample_row.get('New_ranked_Index', 'N/A')}")
            print(f"Tenure Discrete: {sample_row.get('Tenure_discrete', 'N/A')}")
            print(f"Company Discrete: {sample_row.get('Company_discrete', 'N/A')}")
            print(f"Degree Major Score: {sample_row.get('degree_major_score', 'N/A')}")
            print(f"Job Title Discrete: {sample_row.get('Job_Title_discrete', 'N/A')}")
            print(f"Skills Discrete: {sample_row.get('Skills_discrete', 'N/A')}")
            print(f"University Discrete: {sample_row.get('university_discrete', 'N/A')}")
        
        # Check if data was inserted into the new table
        result = db.execute(text("SELECT COUNT(*) FROM ranking_results WHERE job_id = :job_id"), 
                          {'job_id': job_id})
        count = result.scalar()
        print(f"\nRecords inserted into ranking_results table: {count}")
        
        if count > 0:
            # Show sample records from the table
            result = db.execute(text("""
                SELECT name, file_name, scores, ranked_index, 
                       tenure_discrete, company_discrete, degree_major_discrete,
                       job_title_discrete, skills_discrete, university_discrete
                FROM ranking_results 
                WHERE job_id = :job_id 
                ORDER BY ranked_index 
                LIMIT 3
            """), {'job_id': job_id})
            
            records = result.fetchall()
            print("\nSample records from ranking_results table:")
            for record in records:
                print(f"Name: {record[0]}, File: {record[1]}, Score: {record[2]}, Rank: {record[3]}")
                print(f"  Tenure: {record[4]}, Company: {record[5]}, Degree: {record[6]}")
                print(f"  Job Title: {record[7]}, Skills: {record[8]}, University: {record[9]}")
        
        return True
        
    except Exception as e:
        print(f"Error in test_table_insertion: {e}")
        logging.error(f"Error in test_table_insertion: {e}")
        return False
    finally:
        db.close()


def backup_old_extract_info(job_id: str, save_to_db: bool = True):
    """
    Modified extract_info function to work with database instead of files.
    
    Args:
        job_id (str): UUID of the job description to process
        save_to_db (bool): Whether to save results to database (default: True)
        
    Returns:
        dict: Contains the final merged dataframe and intermediate results
    """
    start_time = time.time()
    
    try:
        # Get database session
        db = SessionLocal()
        
        # Fetch job description from database
        job_description_record = db.query(JobDescription).filter(JobDescription.id == job_id).first()
        if not job_description_record:
            raise ValueError(f"Job description with id {job_id} not found")
        
        # Extract data from database
        JD = job_description_record.jd_text
        information_extracted = job_description_record.information_extracted_json
        
        if not information_extracted:
            raise ValueError(f"No information_extracted_json found for job_id {job_id}")
        
        # Create temporary directory for intermediate files (if needed)
        base_dir = Path(__file__).parent / "output_profiles"
        base_dir.mkdir(parents=True, exist_ok=True)
        meta_folder_path = base_dir / 'meta'
        meta_folder_path.mkdir(parents=True, exist_ok=True)

        job_description_text = json.dumps(JD)
        job_description = preprocess_text(job_description_text)

        # ✅ Filter out bad entries (non-dictionaries)
        information_extracted = [entry for entry in information_extracted if isinstance(entry, dict)]

        df_all_data_raw, skills, formatted_experiences, companies, job_titles, universities = extract_information(
            information_extracted, job_description
        )

        candidate_filename = [entry.get("FileName", "") for entry in information_extracted]

        try:
            recruiter_skills = [skill.get('name') for skill in JD.get('SkillsPreference', []) if skill.get('name')]
            if not recruiter_skills:
                raise ValueError("SkillsPreference is empty or missing.")
        except (KeyError, TypeError, ValueError) as e:
            logging.warning(f"Failed to extract skills from SkillsPreference: {e}")
            recruiter_skills = JD.get('Requirements', {}).get('Skills', [])
            logging.info("Fallback to skills from Requirements.")

        logging.info(f"Skills required by job description: {recruiter_skills}")

        recruiter_skills, labels = extract_skills_and_labels_from_skills_preference(JD)
        logging.info(f"labels for the skills: {labels}")
        skill_list = evaluate_candidate_skills(recruiter_skills, skills, labels)
        logging.info(f"Skill list: {skill_list}")

        all_recruiter_job_titles = []

        # ✅ Also check JD['Role']
        role_in_jd = JD.get('Role')
        if isinstance(role_in_jd, str) and role_in_jd.strip():
            all_recruiter_job_titles.append(role_in_jd.strip())

        # ✅ Suggested titles
        suggested_titles = JD.get('Suggested_Job_Titles', [])
        if isinstance(suggested_titles, str):
            all_recruiter_job_titles.extend([title.strip() for title in suggested_titles.split(',')])
        elif isinstance(suggested_titles, list):
            all_recruiter_job_titles.extend([title.strip() for title in suggested_titles if isinstance(title, str)])
        else:
            logging.warning(f"Unexpected type for Suggested_Job_Titles: {type(suggested_titles)}")

        all_recruiter_job_titles = list(set(all_recruiter_job_titles))
        logging.info(f"all_recruiter_job_titles: {all_recruiter_job_titles}")

        job_title_extracted = extract_job_titles_and_scores(all_recruiter_job_titles, job_titles)
        logging.info(f"Job title extracted: {job_title_extracted}")

        company_values = evaluate_company_rankings(companies, CompanyMapping)
        logging.info(f"Company values: {company_values}")

        tenure_value_list = calculate_tenure_hi(formatted_experiences, all_recruiter_job_titles)
        logging.info(f"Tenure value list: {tenure_value_list}")

        university_values = evaluate_university_rankings(universities, UniversityMapping)
        logging.info(f"University values: {university_values}")

        major_list, degree_list = process_information(information_extracted)
        logging.info(f"Major list: {major_list}")
        logging.info(f"Degree list: {degree_list}")

        min_qualification = JD.get('Requirements', {}).get('Minimum Qualification', '')
        preferred_qualification = JD.get('Requirements', {}).get('Preferred Qualification', '')

        if min_qualification and preferred_qualification:
            recruiter_degree_major = f"{min_qualification} or {preferred_qualification}"
        elif min_qualification:
            recruiter_degree_major = min_qualification
        elif preferred_qualification:
            recruiter_degree_major = preferred_qualification
        else:
            recruiter_degree_major = "No specific qualifications listed"
        logging.info(f"Recruiter degree major: {recruiter_degree_major}")

        degree_major_extracted = extract_degree_major(client, major_list, degree_list, recruiter_degree_major)

        names = [item.get('Name') if isinstance(item, dict) else None for item in information_extracted]

        expected_length = len(names)
        print(f"Expected length: {expected_length}")

        # Debug prints for all lists
        print("names:", type(names), len(names))
        print("candidate_filename:", type(candidate_filename), len(candidate_filename))
        print("university_values:", type(university_values), len(university_values))
        print("company_values:", type(company_values), len(company_values))
        print("tenure_value_list:", type(tenure_value_list), len(tenure_value_list))
        print("job_title_extracted:", type(job_title_extracted), len(job_title_extracted))
        print("skill_list:", type(skill_list), len(skill_list))
        print("degree_major_extracted:", type(degree_major_extracted), len(degree_major_extracted))

        min_len = min(
            len(names), len(candidate_filename), len(university_values),
            len(company_values), len(tenure_value_list),
            len(job_title_extracted), len(skill_list), len(degree_major_extracted)
        )

        # Truncate all to min_len
        names = names[:min_len]
        candidate_filename = candidate_filename[:min_len]
        university_values = university_values[:min_len]
        company_values = company_values[:min_len]
        tenure_value_list = tenure_value_list[:min_len]
        job_title_extracted = job_title_extracted[:min_len]
        skill_list = skill_list[:min_len]
        degree_major_extracted = degree_major_extracted[:min_len]

        print("After truncation:")
        print("names:", len(names))
        print("candidate_filename:", len(candidate_filename))
        print("university_values:", len(university_values))
        print("company_values:", len(company_values))
        print("tenure_value_list:", len(tenure_value_list))
        print("job_title_extracted:", len(job_title_extracted))
        print("skill_list:", len(skill_list))
        print("degree_major_extracted:", len(degree_major_extracted))

        resume_df_full = pd.DataFrame.from_records(
            list(zip(
                names, candidate_filename,
                [u['university_discrete'] for u in university_values],
                [c['Company_discrete'] for c in company_values],
                [d['Tenure_discrete'] for d in tenure_value_list],
                [j['job_title_score'] for j in job_title_extracted],
                [s['skill_discrete'] for s in skill_list],
                [dm['degree_major_Score'] for dm in degree_major_extracted]
            )),
            columns=[
                'name', 'Just_file_name', 'university_discrete', 'Company_discrete',
                'Tenure_discrete', 'Job_Title_discrete',
                'Skills_discrete', 'degree_major_score'
            ]
        )

        resume_df_full['index'] = resume_df_full.index
        resume_df_full = resume_df_full[
            ['index', 'Just_file_name', 'name', 'university_discrete', 'Company_discrete',
             'Tenure_discrete', 'Job_Title_discrete', 'Skills_discrete', 'degree_major_score']
        ]

        # Save discrete candidates to CSV (for compatibility with existing code)
        csv_file_path = str(meta_folder_path / 'discrete_candidates.csv')
        resume_df_full.drop(columns=['name']).to_csv(csv_file_path, index=False, encoding='utf-8')

        state = get_agent_info(csv_file_path)
        learned_weights = np.array([10, 6, 16, 43, 20, 5])
        print(f"Learned weights: {learned_weights}")

        optimal_action = solve_sellect_agent(learned_weights, state)
        sorted_candidates, states_with_candidates, scores = solve_select_agent_new(learned_weights, state)

        indices, score_values = zip(*sorted_candidates)
        rearranged_scores = np.zeros(len(scores), dtype=float)
        rearranged_scores[np.array(indices, dtype=int)] = np.array(score_values)

        scaler = MinMaxScaler(feature_range=(0, 1))
        scaled_agrregated_scores = scaler.fit_transform(np.array(scores).reshape(-1, 1)).flatten()

        df_states_with_candidates = pd.DataFrame(states_with_candidates)
        original_indices = df_states_with_candidates['original_index'].astype(int)

        result_df = resume_df_full.iloc[original_indices][['name']].reset_index()
        result_df.rename(columns={'index': 'original_index'}, inplace=True)

        df = df_all_data_raw.copy()
        for index, row in result_df.iterrows():
            df.at[row['original_index'], 'original_index'] = index

        rows_with_zero = df[df['original_index'] == 0]
        sorted_df = df[df['original_index'] != 0].sort_values(by='original_index')
        sorted_df = pd.concat([rows_with_zero, sorted_df])
        sorted_df['Scores'] = scaled_agrregated_scores
        sorted_df['aggregate_weighted_score'] = scores

        nan_threshold = 8
        nan_counts = df.isnull().sum(axis=1)
        rows_to_move = df[nan_counts > nan_threshold]
        sorted_df.drop(rows_to_move.index, inplace=True)
        df_new = pd.concat([sorted_df, rows_to_move])

        sorted_indices = [int(candidate[0]) for candidate in sorted_candidates]
        if len(sorted_indices) == len(df_new):
            df_new['New_ranked_Index'] = sorted_indices
        else:
            raise ValueError("Length of sorted indices does not match number of rows in DataFrame")

        # ✅ Safe LinkedIn URL mapping
        linkedin_urls = {
            entry.get('FileName'): entry.get('Contact', {}).get('LinkedIn', 'Not available')
            for entry in information_extracted
            if isinstance(entry, dict) and isinstance(entry.get('Contact', {}), dict)
        }

        df_new['Extracted_LinkedIn_URLs'] = df_new['FileName'].map(linkedin_urls)
        merged_df = merge_resume_with_ranking(df_new, resume_df_full)

        # Save final results to CSV (for compatibility)
        csv_file_path = str(base_dir / 'resume_df_with_score.csv')
        merged_df.to_csv(csv_file_path, index=False, encoding='utf-8')
        
        print(merged_df)
        
        # Calculate processing time
        processing_time = time.time() - start_time
        
        # Prepare return data with all intermediate results
        result_data = {
            'merged_df': merged_df,
            'discrete_candidates_df': resume_df_full,
            'all_data_raw_df': df_all_data_raw,
            'skills': skills,
            'formatted_experiences': formatted_experiences,
            'companies': companies,
            'job_titles': job_titles,
            'universities': universities,
            'skill_list': skill_list,
            'job_title_extracted': job_title_extracted,
            'company_values': company_values,
            'tenure_value_list': tenure_value_list,
            'university_values': university_values,
            'degree_major_extracted': degree_major_extracted,
            'scores': scores,
            'scaled_scores': scaled_agrregated_scores,
            'sorted_candidates': sorted_candidates
        }
        
        # Save to database if requested
        if save_to_db:
            save_ranking_results_to_new_table(job_id, merged_df, processing_time)
        return result_data

    except Exception as e:
        logging.error(f"Exception in extract_info: {e}")
        raise
    finally:
        db.close()


# if __name__=="__main__":
#     # Example usage of the modified extract_info function
#     # Replace with an actual job_id from your database
#     test_job_id = "e7a0e529-52f1-4658-b3d3-1c67fb7a356e"
    
#     try:
#         print("=== Testing Table Insertion ===")
#         # Test the new table insertion
#         success = test_table_insertion(test_job_id)
        
#         if success:
#             print("\n=== Table Insertion Test Successful ===")
#         else:
#             print("\n=== Table Insertion Test Failed ===")
        
#         # Also run the original ranking analysis
#         print("\n=== Running Full Ranking Analysis ===")
#         results = extract_info(test_job_id, save_to_db=True)
        
#         print("=== Ranking Analysis Complete ===")
#         print(f"Total candidates processed: {len(results['merged_df'])}")
#         print(f"Processing time: {results.get('processing_time_seconds', 'N/A')} seconds")
#         print(f"Ranking result ID: {results.get('ranking_result_id', 'N/A')}")
        
#         # Display top 5 candidates
#         if results['merged_df'] is not None and len(results['merged_df']) > 0:
#             print("\n=== Top 5 Candidates ===")
#             top_candidates = results['merged_df'].head(5)
#             for idx, row in top_candidates.iterrows():
#                 print(f"{idx+1}. {row.get('Name', 'N/A')} - Score: {row.get('Scores', 'N/A'):.3f}")
        
#     except Exception as e:
#         print(f"Error running ranking analysis: {e}")
#         logging.error(f"Error in main: {e}")    