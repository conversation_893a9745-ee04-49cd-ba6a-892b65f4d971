from datetime import datetime
import re
from app.logger import logging
from sentence_transformers import SentenceTransformer, util
from concurrent.futures import ThreadPoolExecutor, as_completed
import concurrent.futures
import time
import torch



model = SentenceTransformer('sentence-transformers/all-MiniLM-L6-v2')


@staticmethod
def calculate_duration(start, end):
    """Calculate the duration between start and end dates in years."""
    try:
        date_formats = ["%b %Y", "%B %Y", "%b%Y", "%B%Y", "%m/%Y", "%b.%Y"]
        start_date = None

        for fmt in date_formats:
            try:
                start_date = datetime.strptime(start.strip(), fmt)
                break
            except ValueError:
                continue

        if not start_date:
            logging.warning(f"Invalid start date format: {start}")
            return None

        if end.lower() in ["present", "current", "continue"]:
            end_date = datetime.now()
        else:
            end_date = None
            for fmt in date_formats:
                try:
                    end_date = datetime.strptime(end.strip(), fmt)
                    break
                except ValueError:
                    continue
            if not end_date:
                logging.warning(f"Invalid end date format: {end}")
                return None

        delta = end_date - start_date
        years = delta.days / 365  # considering leap years
        return round(years, 2)
    except Exception as e:
        logging.error(f"Error calculating duration: {e}")
        return None


# @staticmethod
# def format_profiles(profiles):
#     title_keys = ["Position", "Job Title", "JobTitle", "Title"]
#     all_formatted_experiences = []

#     def calculate_duration(start_date, end_date):
#         try:
#             if start_date == "Unknown" or end_date == "Unknown":
#                 return 0.0
#             start_dt = datetime.strptime(start_date, "%m/%Y")
#             end_dt = datetime.strptime(end_date, "%m/%Y")
#             duration_months = (end_dt.year - start_dt.year) * 12 + (end_dt.month - start_dt.month)
#             return round(duration_months / 12, 2)
#         except Exception as e:
#             return 0.0

#     def process_date(date_str, is_end_date=False):
#         try:
#             # Handle "Present" or invalid dates like "0/0"
#             if date_str.strip().lower() in ["present", "0/0"]:
#                 return datetime.now().strftime("%m/%Y") if is_end_date else "01/1900"

#             # Handle invalid months like "0/<year>"
#             parts = date_str.strip().split("/")
#             if len(parts) == 2:
#                 month = int(parts[0]) if parts[0].isdigit() and 1 <= int(parts[0]) <= 12 else 1
#                 year = int(parts[1]) if parts[1].isdigit() else None
#                 if year:
#                     return f"{month:02}/{year}"
#             return "Unknown"
#         except Exception:
#             return "Unknown"

#     for profile in profiles:
#         formatted_experience = {
#             "Name": profile.get("Name", "Unknown"),
#             "Experiences": []
#         }

#         for exp in profile.get("Experiences", []):
#             job_title = None
#             for key in title_keys:
#                 job_title = exp.get(key)
#                 if job_title:
#                     break
#             job_title = job_title if job_title else "No Position"

#             tenure = exp.get("Tenure", "")
#             match = re.match(r'^(.*?)(?:[-\u2013]\s*(.*?))?(?:\s*\(.*?\))?$', tenure)

#             if match:
#                 start_date_raw = match.group(1).strip() if match.group(1) else ""
#                 end_date_raw = match.group(2).strip() if match.group(2) else "Present"

#                 start_date = process_date(start_date_raw)
#                 end_date = process_date(end_date_raw, is_end_date=True)

#                 duration = calculate_duration(start_date, end_date)

#                 if duration is not None:
#                     formatted_experience["Experiences"].append({
#                         "Title": job_title,
#                         "Duration": duration,
#                         "Start Date": start_date,
#                         "End Date": end_date
#                     })

#         all_formatted_experiences.append(formatted_experience)

#     return all_formatted_experiences




@staticmethod
def format_profiles(profiles):
    title_keys = ["Position", "Job Title", "JobTitle", "Title"]
    all_formatted_experiences = []

    month_map = {
        "january": 1, "february": 2, "march": 3, "april": 4, "may": 5, "june": 6,
        "july": 7, "august": 8, "september": 9, "october": 10, "november": 11, "december": 12
    }

    def process_date(date_str, is_end_date=False):
        try:
            if not date_str or date_str.strip().lower() in ["present", "ongoing", "current"]:
                return datetime.now().strftime("%m/%Y")

            date_str = date_str.strip()
            
            # Handle cases where only the year is given
            if re.match(r"^\d{4}$", date_str):
                return f"01/{date_str}"

            # Handle cases like "August 2020" or "Aug 2020"
            match = re.match(r"([A-Za-z]+)\s+(\d{4})", date_str)
            if match:
                month_name, year = match.groups()
                month = month_map.get(month_name.lower(), 1)  # Default to January if unknown month
                return f"{month:02}/{year}"

            # Handle cases like "8/2020" or "08/2020"
            parts = date_str.split("/")
            if len(parts) == 2 and parts[0].isdigit() and parts[1].isdigit():
                month = int(parts[0])
                year = int(parts[1])
                if 1 <= month <= 12:
                    return f"{month:02}/{year}"

            return "01/1900"  # Default unknown values to a fallback date
        except Exception:
            return "01/1900"

    def calculate_duration(start_date, end_date):
        try:
            if start_date == "01/1900" or end_date == "01/1900":
                return 0.0
            start_dt = datetime.strptime(start_date, "%m/%Y")
            end_dt = datetime.strptime(end_date, "%m/%Y")
            duration_months = (end_dt.year - start_dt.year) * 12 + (end_dt.month - start_dt.month)
            return round(duration_months / 12, 2)
        except Exception as e:
            logging.error(f"Error calculating duration: {e}")
            return 0.0

    for profile in profiles:
        formatted_experience = {
            "Name": profile.get("Name", "Unknown"),
            "Experiences": []
        }

        for exp in profile.get("Experiences", []):
            job_title = None
            for key in title_keys:
                job_title = exp.get(key)
                if job_title:
                    break
            job_title = job_title if job_title else "No Position"

            tenure = exp.get("Tenure", "")
            match = re.match(r'^(.*?)(?:[-\u2013]\s*(.*?))?(?:\s*\(.*?\))?$', tenure)

            if match:
                start_date_raw = match.group(1).strip() if match.group(1) else ""
                end_date_raw = match.group(2).strip() if match.group(2) else "Present"

                start_date = process_date(start_date_raw)
                end_date = process_date(end_date_raw, is_end_date=True)

                duration = calculate_duration(start_date, end_date)

                if duration is not None:
                    formatted_experience["Experiences"].append({
                        "Title": job_title,
                        "Duration": duration,
                        "Start Date": start_date,
                        "End Date": end_date
                    })

        all_formatted_experiences.append(formatted_experience)

    return all_formatted_experiences


# v1
@staticmethod
def calculate_tenure_hi(candidate_titles, recruiter_titles, similarity_threshold=0.55):
    """
    Calculate the total tenure for candidates by matching their job titles
    against multiple recruiter job titles using cosine similarity.
    """
    start_time = time.time()

    # Encode all recruiter titles once to save computation
    recruiter_embeddings = model.encode(recruiter_titles)

    tenure_list = []

    for candidate_idx, candidate_data in enumerate(candidate_titles):
        total_tenure = 0
        candidate_name = candidate_data.get("Name", f"Candidate {candidate_idx + 1}")
        logging.info(f"\n======================================== {candidate_name} ========================================")

        for exp in candidate_data.get("Experiences", []):
            candidate_title = exp["Title"]
            experience_duration = exp["Duration"]

            # Encode the candidate's title
            candidate_embedding = model.encode([candidate_title])

            # Compute cosine similarity for all recruiter titles
            similarity_scores = util.pytorch_cos_sim(recruiter_embeddings, candidate_embedding).squeeze()
            # Handle scalar and tensor/list outputs robustly
            if isinstance(similarity_scores, float):
                max_similarity = similarity_scores
            elif hasattr(similarity_scores, 'tolist'):
                similarity_scores = similarity_scores.tolist()
                if isinstance(similarity_scores, list):
                    max_similarity = max(similarity_scores)
                else:
                    max_similarity = similarity_scores
            else:
                max_similarity = float(similarity_scores)

            logging.info(
                f"Candidate Title: '{candidate_title}', Recruiter Titles: '{recruiter_titles}', "
                f"Max Cosine Similarity: {max_similarity:.2f}, Experience: {experience_duration}"
            )

            # Add duration if max similarity exceeds the threshold
            if max_similarity >= similarity_threshold:
                total_tenure += experience_duration

        total_tenure = round(total_tenure, 2)
        logging.info(f"Total Tenure for {candidate_name}: {total_tenure} years")

        tenure_list.append({
            'Candidate Name': candidate_name,
            'Tenure_discrete': str(total_tenure)
        })

    logging.info(f"Total processing time: {time.time() - start_time:.2f} seconds")
    return [{'Tenure_discrete': str(candidate['Tenure_discrete'])} for candidate in tenure_list]

# v2
# @staticmethod
# def calculate_tenure_hi(candidate_titles, recruiter_titles, min_YOE, similarity_threshold=0.50):
#     """
#     Calculate the total tenure for candidates by matching their job titles
#     against multiple recruiter job titles using cosine similarity.
#     """
#     start_time = time.time()
    
#     min_YOE = max(min_YOE, 1)
#     # Encode all recruiter titles once to save computation
#     recruiter_embeddings = model.encode(recruiter_titles)

#     tenure_list = []

#     for candidate_idx, candidate_data in enumerate(candidate_titles):
#         total_tenure = 0
#         candidate_name = candidate_data.get("Name", f"Candidate {candidate_idx + 1}")
#         logging.info(f"\n======================================== {candidate_name} ========================================")

#         for exp in candidate_data.get("Experiences", []):
#             candidate_title = exp["Title"]
#             experience_duration = exp["Duration"]

#             try:
#                 # Encode the candidate's title
#                 candidate_embedding = model.encode([candidate_title])

#                 # Compute cosine similarity for all recruiter titles
#                 similarity_scores = util.pytorch_cos_sim(recruiter_embeddings, candidate_embedding).squeeze()

#                 # Handle scalar and tensor outputs
#                 if isinstance(similarity_scores, torch.Tensor):
#                     if similarity_scores.ndim == 0:  # Scalar output
#                         similarity_scores = [similarity_scores.item()]
#                     else:
#                         similarity_scores = similarity_scores.tolist()

#                 # Compute the max similarity
#                 if isinstance(similarity_scores, list):
#                     max_similarity = max(similarity_scores)
#                 else:
#                     raise ValueError("Unexpected similarity_scores format: not a list")

#                 logging.info(
#                     f"Candidate Title: '{candidate_title}', Recruiter Titles: '{recruiter_titles}', "
#                     f"Max Cosine Similarity: {max_similarity:.2f}, Experience: {experience_duration}"
#                 )

#                 # Add duration if max similarity exceeds the threshold
#                 if max_similarity >= similarity_threshold:
#                     total_tenure += experience_duration

#             except Exception as e:
#                 logging.error(f"Error processing candidate title '{candidate_title}': {e}")
#                 continue

#         actual_tenure_years = round(total_tenure, 2)
#         total_tenure = round(total_tenure / min_YOE, 2) if min_YOE else 0
#         logging.info(f"Total Tenure for {candidate_name}: {actual_tenure_years} years (Discrete: {total_tenure})")

#         tenure_list.append({
#             'Candidate Name': candidate_name,
#             'Tenure_discrete': str(total_tenure),
#             'Actual_Tenure_Years': str(actual_tenure_years)
#         })

#     logging.info(f"Total processing time: {time.time() - start_time:.2f} seconds")
#     tenure_discrete_list = [{'Tenure_discrete': str(candidate['Tenure_discrete'])} for candidate in tenure_list]
#     actual_tenure_list = [{'Actual_Tenure_Years': str(candidate['Actual_Tenure_Years'])} for candidate in tenure_list]

#     return tenure_discrete_list, actual_tenure_list

    # return [{'Tenure_discrete': str(candidate['Tenure_discrete'])} for candidate in tenure_list]

