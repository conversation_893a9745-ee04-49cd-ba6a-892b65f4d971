# -*- coding: utf-8 -*-
import pandas as pd
from ortools.linear_solver import pywraplp

def get_agent_info(agent_info_file):
    agent_info_df = pd.read_csv(agent_info_file)
    # print("hi:",agent_info_df)
    return agent_info_df


def solve_sellect_agent(weights, agent_info_df):
    Ni = len(agent_info_df)
    solver = pywraplp.Solver.CreateSolver('CBC')

    # Define decision variables
    x = []
    for i in range(Ni):
        x.append(solver.IntVar(0, 1, 'x_%i' % (i)))

    # Set Constraints
    solver.Add(sum(x) == 1)

    # Set Objective
    solver.Maximize(weights[0] * sum([agent_info_df['university_discrete'][i] * x[i] for i in range(Ni)]) +
                    weights[1] * sum([agent_info_df['Company_discrete'][i] * x[i] for i in range(Ni)]) +
                    weights[2] * sum([agent_info_df['Tenure_discrete'][i] * x[i] for i in range(Ni)]) +
                    weights[3] * sum([agent_info_df['Job_Title_discrete'][i] * x[i] for i in range(Ni)]) +
                    weights[4] * sum([agent_info_df['Skills_discrete'][i] * x[i] for i in range(Ni)]) +
                    weights[5] * sum([agent_info_df['degree_major_score'][i] * x[i] for i in range(Ni)]))

    # Solve
    status = solver.Solve()
    if status == pywraplp.Solver.OPTIMAL:
        objective_value = solver.Objective().Value()
        return [x[i].solution_value() for i in range(Ni)], objective_value
    else:
        print('The problem does not have an optimal solution.')
        return [0] * Ni, 0

