import json
from app.rank.sellect_agent import *
import numpy as np
from app.rank.exception import CustomException
import sys
from app.logger import logging
import os
import re
import PyPDF2
import pdfplumber
import fitz

# from app.rank.sellect_agent import solve_sellect_agent


# import nltk
# def preprocess_text(text):
#     return ' '.join(word for word in text.split() if word.isalnum()).lower()  
# from nltk.corpus import stopwords
# from nltk.tokenize import word_tokenize
# from nltk.stem import PorterStemmer

# nltk.download('stopwords')
# nltk.download('punkt')

# def preprocess_text(text):
#     # Convert text to lowercase and remove non-alphanumeric characters
#     text = ' '.join(word for word in text.split() if word.isalnum()).lower()
#     # Remove stopwords
#     stop_words = set(stopwords.words('english'))
#     word_tokens = word_tokenize(text)
#     filtered_text = [word for word in word_tokens if word not in stop_words]
#     # Stemming
#     ps = PorterStemmer()
#     stemmed_text = [ps.stem(word) for word in filtered_text]
#     # Join the tokens back into a single string
#     preprocessed_text = ' '.join(stemmed_text)
#     return preprocessed_text









class JSONy:
    def save_information_extracted(information_extracted, file_path):
        with open(file_path, 'w') as file:
            json.dump(information_extracted, file)

    def load_information_extracted(file_path):
        with open(file_path, 'r') as file:
            return json.load(file)
        
    def print_json_info(json_string):
        try:
            json_object = json.loads(json_string)
            return json_object
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")   

    def process_resumes(output_directory):
        try:
            text_list = []
            file_list = []
            for subdir, _, files in os.walk(output_directory):
                for file_name in files:
                    if file_name.endswith(".txt"):
                        file_path = os.path.join(subdir, file_name)
                        with open(file_path, "r") as file:
                            text_content = file.read()
                            # text_content=preprocess_text(text_content)
                            text_list.append(text_content)
                            file_list.append(file_name)
            logging.info(f"Total number of resumes are {len(text_list)} and their file names in the order of extraction: {file_list}")
            return text_list, file_list
        
        except Exception as e:
            raise CustomException(e, sys) 

                       

# def min_max_scaling(values):
#     """
#     Apply min-max scaling to a list of values to scale them to a range of 0 to 100.

#     Parameters:
#     - values: A list of numerical values.

#     Returns:
#     - scaled_values: A list of values scaled to the range [0, 100].
#     """
#     min_value = 0
#     max_value = max(values)

#     scaled_values = [(100 * (value - min_value) / (max_value - min_value)) for value in values]
    
#     return scaled_values   

def min_max_scaling(values):
    """
    Apply min-max scaling to a list of values to scale them to a range of 0 to 100.
    """
    min_value = 0
    max_value = max(values) if values else 1  # Avoid division by zero

    if max_value == min_value:
        return [0 for _ in values]  # Avoids divide-by-zero error in a single-value case

    scaled_values = [(100 * (value - min_value) / (max_value - min_value)) for value in values]
    
    return scaled_values          


def solve_select_agent_new(learned_weights, copied_state):
    """
    Perform a selection process based on learned weights and a state.

    Parameters:
    - learned_weights: A list of learned weights for the selection process.
    - copied_state: The state from which the selection process will be conducted.

    Returns:
    - sorted_candidates: A list of candidates sorted based on their scores.
    - states_with_candidates: A list of states associated with the selected candidates.
    - scores_list: A list of scores of the selected candidates.
    """
    try:
        candidates = []
        states_with_candidates = []
        scores_list = []  # List to store scores of each top candidate

        # Store original indices in a separate column
        copied_state['original_index'] = copied_state.index

        while not copied_state.empty:
            try:
                action, objective_value = solve_sellect_agent(learned_weights, copied_state)
                best_candidate_index = np.argmax(action)
                best_candidate_score = objective_value  # Use the objective value as the score
                original_index = copied_state.iloc[best_candidate_index]['original_index']  # Get original index
                candidates.append((original_index, best_candidate_score))  # Append with original index
                scores_list.append(best_candidate_score)  # Append the score to the list

                # Append the best candidate state
                state_dict = {
                    'original_index': original_index,
                    'university_discrete': copied_state.iloc[best_candidate_index]['university_discrete'],
                    'Company_discrete': copied_state.iloc[best_candidate_index]['Company_discrete'],
                    'Tenure_discrete': copied_state.iloc[best_candidate_index]['Tenure_discrete'],
                    'Job_Title_discrete': copied_state.iloc[best_candidate_index]['Job_Title_discrete'],
                    'Skills_discrete': copied_state.iloc[best_candidate_index]['Skills_discrete'],
                    'degree_major_score': copied_state.iloc[best_candidate_index]['degree_major_score']
                }
                states_with_candidates.append(state_dict)

                # Remove the best candidate row from the state
                copied_state = copied_state.drop(copied_state.index[best_candidate_index]).reset_index(drop=True)
            except ValueError:
                break

        # Sort candidates based on scores from best to worst
        sorted_candidates = sorted(candidates, key=lambda x: x[1], reverse=True)

        return sorted_candidates, states_with_candidates, scores_list  # Return the scores_list along with other results

    except Exception as e:
        raise CustomException(e, sys)


class EmailExtarct:
    def extract_emails_from_pdf(file_path):
        emails = []
        email_pattern = re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}')

        file_path_str = str(file_path)

        if file_path_str.endswith('.pdf'):
            try:
                with pdfplumber.open(file_path) as pdf:
                    for page in pdf.pages:
                        text = page.extract_text() or ""
                        found_emails = email_pattern.findall(text)
                        for email in found_emails:
                            if email not in emails:
                                emails.append(email)
                        
                        if page.annots:
                            for annotation in page.annots:
                                uri = annotation.get('uri')
                                if uri and email_pattern.match(uri):
                                    email = uri.strip()
                                    if email not in emails:
                                        emails.append(email)
                    
                    # Ensure length match
                    if len(emails) == 0:
                        emails.append("Not found")
                        
            except Exception as e:
                logging.info(f"Error processing {file_path}: {e}")
                emails.append("Not found")
        else:
            emails.append("Not found")
        
        return emails

    def extract_email_from_hyperlinks(pdf_path):
        email = None
        with fitz.open(pdf_path) as doc:
            for page in doc:
                links = page.get_links()
                for link in links:
                    uri = link.get('uri', '')
                    if re.match(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', uri):
                        email = uri.strip()
                        break
                if email:
                    break
        return email


    def find_longest_email(emails):
        if not emails or emails == ["Not found"]:
            return "Not found"
        return max(emails, key=len)   



# import json
# from sellect_agent import *
# import numpy as np
# from exception import CustomException
# import sys
# from logger import logging
# import os
# import re
# import PyPDF2
# import pdfplumber
# import fitz



# import nltk
# # def preprocess_text(text):
# #     return ' '.join(word for word in text.split() if word.isalnum()).lower()  
# from nltk.corpus import stopwords
# from nltk.tokenize import word_tokenize
# from nltk.stem import PorterStemmer

# nltk.download('stopwords')
# nltk.download('punkt')


# import redis

# redis_client = redis.StrictRedis(host='localhost', port=6379, db=0, decode_responses=True)



# class JSONy:
#     def save_information_extracted(information_extracted, file_path):
#         with open(file_path, 'w') as file:
#             json.dump(information_extracted, file)

#     # def load_information_extracted(file_path):
#     #     with open(file_path, 'r') as file:
#     #         return json.load(file)
#     def load_information_extracted(file_path):
#         """Load JSON from Redis cache or file system if missing."""
#         cache_key = f"info_json:{file_path}"

#         # ✅ Check Redis cache first
#         cached_data = redis_client.get(cache_key)
#         if cached_data:
#             return json.loads(cached_data)  # 🚀 Fastest (<1ms)

#         # ❌ If not cached, load from file
#         with open(file_path, 'r') as file:
#             data = json.load(file)

#         # ✅ Store in Redis (cache for 1 hour)
#         redis_client.setex(cache_key, 3600, json.dumps(data))

#         return data  # 🚀 Fast after first read
        
#     def print_json_info(json_string):
#         try:
#             json_object = json.loads(json_string)
#             return json_object
#         except json.JSONDecodeError as e:
#             print(f"Error decoding JSON: {e}")   

#     def process_resumes(output_directory):
#         try:
#             text_list = []
#             file_list = []
#             for subdir, _, files in os.walk(output_directory):
#                 for file_name in files:
#                     if file_name.endswith(".txt"):
#                         file_path = os.path.join(subdir, file_name)
#                         with open(file_path, "r") as file:
#                             text_content = file.read()
#                             # text_content=preprocess_text(text_content)
#                             text_list.append(text_content)
#                             file_list.append(file_name)
#             logging.info(f"Total number of resumes are {len(text_list)} and their file names in the order of extraction: {file_list}")
#             return text_list, file_list
        
#         except Exception as e:
#             raise CustomException(e, sys) 

                    

# # def min_max_scaling(values):
# #     """
# #     Apply min-max scaling to a list of values to scale them to a range of 0 to 100.
# #     """
# #     min_value = 0
# #     max_value = max(values) if values else 1  # Avoid division by zero

# #     if max_value == min_value:
# #         return [0 for _ in values]  # Avoids divide-by-zero error in a single-value case

# #     scaled_values = [(100 * (value - min_value) / (max_value - min_value)) for value in values]
    
# #     return scaled_values          


# def min_max_scaling(values):
#     """
#     Apply min-max scaling to a list of values, scaling them to a range of 0 to 100.

#     Uses NumPy for optimized vectorized operations.
#     """
#     if not values:  # ✅ Handle empty list edge case
#         return []

#     values = np.array(values, dtype=np.float64)  # ✅ Convert to NumPy array for faster computation
#     min_value = np.min(values)
#     max_value = np.max(values)

#     if min_value == max_value:  # ✅ Avoid divide-by-zero case
#         return np.zeros_like(values).tolist()

#     return (100 * (values - min_value) / (max_value - min_value)).tolist()



# # def solve_select_agent_new(learned_weights, copied_state):
# #     """
# #     Perform a selection process based on learned weights and a state.

# #     Parameters:
# #     - learned_weights: A list of learned weights for the selection process.
# #     - copied_state: The state from which the selection process will be conducted.

# #     Returns:
# #     - sorted_candidates: A list of candidates sorted based on their scores.
# #     - states_with_candidates: A list of states associated with the selected candidates.
# #     - scores_list: A list of scores of the selected candidates.
# #     """
# #     try:
# #         candidates = []
# #         states_with_candidates = []
# #         scores_list = []  # List to store scores of each top candidate

# #         # Store original indices in a separate column
# #         copied_state['original_index'] = copied_state.index

# #         while not copied_state.empty:
# #             try:
# #                 action, objective_value = solve_sellect_agent(learned_weights, copied_state)
# #                 best_candidate_index = np.argmax(action)
# #                 best_candidate_score = objective_value  # Use the objective value as the score
# #                 original_index = copied_state.iloc[best_candidate_index]['original_index']  # Get original index
# #                 candidates.append((original_index, best_candidate_score))  # Append with original index
# #                 scores_list.append(best_candidate_score)  # Append the score to the list

# #                 # Append the best candidate state
# #                 state_dict = {
# #                     'original_index': original_index,
# #                     'university_discrete': copied_state.iloc[best_candidate_index]['university_discrete'],
# #                     'Company_discrete': copied_state.iloc[best_candidate_index]['Company_discrete'],
# #                     'Tenure_discrete': copied_state.iloc[best_candidate_index]['Tenure_discrete'],
# #                     'Job_Title_discrete': copied_state.iloc[best_candidate_index]['Job_Title_discrete'],
# #                     'Skills_discrete': copied_state.iloc[best_candidate_index]['Skills_discrete'],
# #                     'degree_major_score': copied_state.iloc[best_candidate_index]['degree_major_score']
# #                 }
# #                 states_with_candidates.append(state_dict)

# #                 # Remove the best candidate row from the state
# #                 copied_state = copied_state.drop(copied_state.index[best_candidate_index]).reset_index(drop=True)
# #             except ValueError:
# #                 break

# #         # Sort candidates based on scores from best to worst
# #         sorted_candidates = sorted(candidates, key=lambda x: x[1], reverse=True)

# #         return sorted_candidates, states_with_candidates, scores_list  # Return the scores_list along with other results

# #     except Exception as e:
# #         raise CustomException(e, sys)

# def solve_select_agent_new(learned_weights, copied_state):
#     """
#     Perform a selection process based on learned weights and a state.
#     Optimized:
#     - Uses NumPy vectorized operations to reduce `max()` calls.
#     - Replaces sorting with `np.argsort()`.
#     """

#     try:
#         candidates = []
#         states_with_candidates = []
#         scores_list = []  # Store scores of selected candidates

#         copied_state['original_index'] = copied_state.index

#         while not copied_state.empty:
#             try:
#                 action, objective_value = solve_sellect_agent(learned_weights, copied_state)

#                 # ✅ Optimized: Use NumPy's `argmax()` once and extract all required data efficiently
#                 best_candidate_index = np.argmax(action)
#                 best_candidate_score = objective_value
#                 original_index = copied_state.iloc[best_candidate_index]['original_index']

#                 candidates.append((original_index, best_candidate_score))
#                 scores_list.append(best_candidate_score)

#                 # ✅ Store best candidate’s attributes in NumPy arrays for efficiency
#                 best_candidate_state = copied_state.iloc[best_candidate_index][[
#                     'original_index', 'university_discrete', 'Company_discrete',
#                     'Tenure_discrete', 'Job_Title_discrete', 'Skills_discrete',
#                     'degree_major_score'
#                 ]].to_dict()
                
#                 states_with_candidates.append(best_candidate_state)

#                 # ✅ Efficiently drop the best candidate
#                 copied_state = copied_state.drop(copied_state.index[best_candidate_index]).reset_index(drop=True)
#             except ValueError:
#                 break

#         # ✅ Optimized Sorting: Use `np.argsort()` instead of `sorted()`
#         sorted_indices = np.argsort(-np.array([score for _, score in candidates]))  # Negative for descending order
#         sorted_candidates = [candidates[i] for i in sorted_indices]

#         return sorted_candidates, states_with_candidates, scores_list

#     except Exception as e:
#         raise CustomException(e, sys)



# class EmailExtarct:
#     def extract_emails_from_pdf(file_path):
#         emails = []
#         email_pattern = re.compile(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}')

#         file_path_str = str(file_path)

#         if file_path_str.endswith('.pdf'):
#             try:
#                 with pdfplumber.open(file_path) as pdf:
#                     for page in pdf.pages:
#                         text = page.extract_text() or ""
#                         found_emails = email_pattern.findall(text)
#                         for email in found_emails:
#                             if email not in emails:
#                                 emails.append(email)
                        
#                         if page.annots:
#                             for annotation in page.annots:
#                                 uri = annotation.get('uri')
#                                 if uri and email_pattern.match(uri):
#                                     email = uri.strip()
#                                     if email not in emails:
#                                         emails.append(email)
                    
#                     # Ensure length match
#                     if len(emails) == 0:
#                         emails.append("Not found")
                        
#             except Exception as e:
#                 logging.info(f"Error processing {file_path}: {e}")
#                 emails.append("Not found")
#         else:
#             emails.append("Not found")
        
#         return emails

#     def extract_email_from_hyperlinks(pdf_path):
#         email = None
#         with fitz.open(pdf_path) as doc:
#             for page in doc:
#                 links = page.get_links()
#                 for link in links:
#                     uri = link.get('uri', '')
#                     if re.match(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', uri):
#                         email = uri.strip()
#                         break
#                 if email:
#                     break
#         return email


#     def find_longest_email(emails):
#         if not emails or emails == ["Not found"]:
#             return "Not found"
#         return max(emails, key=len)   