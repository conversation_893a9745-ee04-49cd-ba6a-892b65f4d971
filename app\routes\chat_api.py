"""
DashChat /chatAPI Orchestration Endpoint
Implements LLM-powered workflow orchestration for job/candidate processing.
"""
from fastapi import APIRouter, Depends, HTTPException, Request, Body, Query
from fastapi.responses import JSONResponse
from fastapi import UploadFile, File, Form
from starlette.requests import Request as StarletteRequest
from starlette.datastructures import MutableHeaders
from starlette.types import Scope, Receive, Send
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.auth.security import get_current_user
from app.db.db_models import JobWorkflowState
from app.services.jd_service import JobDescriptionService
from app.services.scraping_service import ScrapingService
from app.services.note_service import NoteService
from app.services.serpapi_service import SerpAPIService
from app.services.rapidapi_batch_service import RapidAPIBatchService
from app.services.ranking_service import RankingService
from app.models.note_schemas import NoteCreate
from app.models.ranking import RankingRequest
import uuid
import asyncio
import logging
import re
import os
import pandas as pd
from app.rank.ranking import extract_info
import re
import tenacity
import fitz
import json


__all__ = ["router"]

router = APIRouter()

# --- Global OpenAI client and key ---
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in environment.")
from openai import OpenAI
openai_client = OpenAI(api_key=OPENAI_API_KEY)

# --- Helper: Extract intent/entities (placeholder for LLM or spaCy) ---
def extract_intent_entities(query: str) -> dict:
    # TODO: Replace with OpenAI or spaCy-based extraction
    # For now, use simple keyword matching
    query_lower = query.lower()
    if "parse" in query_lower and "url" in query_lower:
        return {"intent": "parse_jd_url"}
    elif "parse" in query_lower:
        return {"intent": "parse_jd"}
    elif "note" in query_lower:
        return {"intent": "add_note"}
    elif "search" in query_lower:
        return {"intent": "search_candidates"}
    elif "batch" in query_lower:
        return {"intent": "batch_candidates"}
    elif "rank" in query_lower:
        return {"intent": "rank_candidates"}
    else:
        # Fallback: Use OpenAI to extract intent and entities
        try:
            prompt = f"""
Given the following user query, extract the following fields as JSON:
- intent (one of: parse_jd, parse_jd_url, add_note, search_candidates, batch_candidates, rank_candidates)
- job_title (string, if relevant)
- skills (list of strings, if any)
- location (string, if relevant)
- num_candidates (integer, if specified, else 10)
- url (string, if present)
- note_text (string, if present)

Query: {query}

Return ONLY the JSON object, no markdown or extra text."""
            logging.info(f"[intent_extraction] Using OpenAI fallback for intent extraction.")
            response = openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert at extracting structured intent and entities from user queries."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )
            content = response.choices[0].message.content.strip()
            import json
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            params = json.loads(content)
            logging.info(f"[intent_extraction] OpenAI extracted: {params}")
            return params
        except Exception as e:
            logging.error(f"OpenAI intent extraction failed: {e}")
            return {"intent": "unknown"}
        

                # Convert any other numpy types in the result
# def convert_numpy_types(obj):
#     try:
#         if hasattr(obj, 'tolist'):  # numpy array - check this first
#             return obj.tolist()
#         elif hasattr(obj, 'item'):  # numpy scalar
#             return obj.item()
#         elif isinstance(obj, dict):
#             return {k: convert_numpy_types(v) for k, v in obj.items()}
#         elif isinstance(obj, list):
#             return [convert_numpy_types(item) for item in obj]
#         else:
#             return obj
#     except Exception as e:
#         # If conversion fails, return the original object as string
#         logging.warning(f"Failed to convert numpy type: {type(obj)}, error: {e}")
#         return str(obj) if obj is not None else None        

def convert_numpy_types(obj):
    try:
        if isinstance(obj, pd.DataFrame):
            return obj.to_dict(orient="records")
        elif isinstance(obj, pd.Series):
            return obj.tolist()
        elif hasattr(obj, 'tolist'):  # numpy array
            return obj.tolist()
        elif hasattr(obj, 'item'):  # numpy scalar
            return obj.item()
        elif isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(i) for i in obj]
        elif isinstance(obj, tuple):
            return tuple(convert_numpy_types(i) for i in obj)
        else:
            return obj
    except Exception as e:
        logging.warning(f"Failed to convert value during JSON serialization: {e}")
        return str(obj) if obj is not None else None

# --- Main Orchestration Endpoint ---
@router.post("/chatAPI")
async def chat_api(
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    Orchestrate multi-step candidate processing workflow based on user query.
    Tracks workflow state, routes to correct backend function, and returns unified response.
    """
    data = await request.json()
    user_id = current_user.id
    query = data.get("query", "")
    job_id = data.get("job_id")  # May be None for first step
    extra = data.get("extra", {})

    # 1. Extract intent/entities
    intent_info = extract_intent_entities(query)
    intent = extra.get("intent") or intent_info["intent"]

    result = None
    error = None
    next_step = None
    workflow = None

    try:
        # Import re at the top of the function for use in multiple blocks
        
        
        # --- FIRST STEP: Parse JD (raw or URL), no job_id yet ---
        if not job_id and intent in ("parse_jd", "parse_jd_url"):
            if intent == "parse_jd":
                jd_text = data.get("jd_text")
                if not jd_text:
                    # Try to extract job description from the query
                    # Look for text after "parse this job description:" or similar patterns
                    patterns = [
                        r'parse this job description:\s*(.+)',
                        r'parse job description:\s*(.+)',
                        r'parse the job description:\s*(.+)',
                        r'parse job description\s*(.+)'
                    ]
                    
                    for pattern in patterns:
                        match = re.search(pattern, query, re.IGNORECASE | re.DOTALL)
                        if match:
                            jd_text = match.group(1).strip()
                            break
                    
                    # If still no match, use the entire query (excluding the parse command)
                    if not jd_text:
                        # Remove common parse commands from the beginning
                        cleaned_query = re.sub(r'^parse\s+(this\s+)?(the\s+)?job\s+description:?\s*', '', query, flags=re.IGNORECASE)
                        if cleaned_query.strip():
                            jd_text = cleaned_query.strip()
                    
                if not jd_text:
                    raise HTTPException(status_code=400, detail="Missing job description text.")
                
                service = JobDescriptionService()
                result = await service.parse_and_save_job_description(db, jd_text, user_id, parse_method="raw")
                # Get the new job_id from the DB (most recent for this user)
                from app.db.db_models import JobDescription
                new_jd = db.query(JobDescription).filter_by(user_id=user_id).order_by(JobDescription.created_at.desc()).first()
                job_id = str(new_jd.id)
            elif intent == "parse_jd_url":
                url = data.get("url")
                if not url:
                    # Try to extract URL from the query string
                    match = re.search(r'https?://[^\s]+', query)
                    if match:
                        url = match.group(0)
                if not url:
                    raise HTTPException(status_code=400, detail="Missing job description URL.")
                scraping_service = ScrapingService()
                # 1. Scrape the job description from the URL
                scraped = await scraping_service.scrape_job_description(url)
                # Log the actual scraped output before any formatting/mapping
                logging.info(f"Raw scraped job description output: {scraped}")
                # 2. Extract the main description text (try 'description', fallback to 'full_text' or '')
                if scraped is None:
                    logging.error(f"Scraping failed or returned nothing for URL: {url}")
                    raise HTTPException(status_code=400, detail="Failed to scrape job description.")
                data = scraped.get("data", {})
                job_info = data.get("job_info", {})

                # Get the main job description from various fallback sources
                job_description = (
                    job_info.get("description")
                    or data.get("full_text")
                    or scraped.get("full_text")
                    or scraped.get("description")
                    or ""
                )

                # Select relevant metadata fields to append
                metadata_keys = [
                    "location", "employment_status", "is_remote_allowed", "listed_at",
                    "expire_at", "job_url", "experience_level", "workplace_types",
                    "job_state", "country_code", "job_posting_id", "is_reposted",
                    "job_application_limit_reached", "eligible_for_referrals"
                ]
                metadata = {k: job_info.get(k) for k in metadata_keys if job_info.get(k) is not None}

                # Convert metadata to JSON string for better LLM parsing (can switch to plain text if needed)
                import json
                extra_text = "\n\n[EXTRA INFO]\n" + json.dumps(metadata, ensure_ascii=False, indent=2)

                # Combine main description with extra metadata
                jd_text = job_description.strip() + extra_text

                if not jd_text:
                    # Log the scraped result for debugging
                    logging.error(f"Scraped result missing description: {scraped}")
                    # Insert a failsafe normalized job description
                    normalized = {
                        "Role": "N/A",
                        "Company": "N/A",
                        "Benefits": [],
                        "Location": "N/A",
                        "Requirements": {
                            "Skills": [],
                            "Experience": "N/A",
                            "Minimum Qualification": "N/A",
                            "Preferred Qualification": "N/A"
                        },
                        "Responsibilities": [],
                        "SkillsPreference": [
                            {"name": "", "status": "R"}
                        ],
                        "Suggested_Job_Titles": []
                    }
                    # Save to job_descriptions table manually
                    from app.db.db_models import JobDescription
                    new_jd = JobDescription(
                        user_id=user_id,
                        job_title="N/A",
                        location="N/A",
                        jd_text=normalized,
                        parse_method="url"
                    )
                    db.add(new_jd)
                    db.commit()
                    db.refresh(new_jd)
                    result = normalized
                    job_id = str(new_jd.id)
                else:
                    # 3. Pass it through the LLM parser to normalize
                    parser = JobDescriptionService()
                    normalized = await parser.parse_and_save_job_description(db, jd_text, user_id, parse_method="url")
                    result = normalized
                    # 4. Get the new job_id from the DB (most recent for this user)
                    from app.db.db_models import JobDescription
                    new_jd = db.query(JobDescription).filter_by(user_id=user_id).order_by(JobDescription.created_at.desc()).first()
                    job_id = str(new_jd.id)
            # Now create workflow state
            workflow = JobWorkflowState(user_id=user_id, job_id=job_id, current_step="parsed_jd", status="in_progress")
            db.add(workflow)
            db.commit()
            db.refresh(workflow)
            next_step = "add_note"
        else:
            # --- SUBSEQUENT STEPS: job_id is provided ---
            workflow = db.query(JobWorkflowState).filter_by(user_id=user_id, job_id=job_id).first()
            if not workflow:
                raise HTTPException(status_code=404, detail="Workflow state not found for this job_id.")
            if intent == "add_note":
                note_text = data.get("note_text")
                if not note_text:
                    # Try to extract note text from the query
                    # Look for text after "add a note:" or similar patterns
                    patterns = [
                        r'add a note:\s*(.+)',
                        r'add note:\s*(.+)',
                        r'add note\s*(.+)',
                        r'note:\s*(.+)'
                    ]
                    
                    for pattern in patterns:
                        match = re.search(pattern, query, re.IGNORECASE | re.DOTALL)
                        if match:
                            note_text = match.group(1).strip()
                            break
                    
                    # If still no match, use the entire query (excluding the add note command)
                    if not note_text:
                        # Remove common add note commands from the beginning
                        cleaned_query = re.sub(r'^add\s+(a\s+)?note:?\s*', '', query, flags=re.IGNORECASE)
                        if cleaned_query.strip():
                            note_text = cleaned_query.strip()
                
                if not note_text or not job_id:
                    raise HTTPException(status_code=400, detail="Missing note text or job_id.")
                
                note_service = NoteService()
                note = NoteCreate(job_id=job_id, note_text=note_text)
                note_obj = note_service.create_note(db, user_id, note)
                # Convert Note ORM object to JSON-serializable dict (minimal, non-redundant)
                result = {
                    "id": str(note_obj.id),
                    "note_text": note_obj.note_text,
                    "created_at": str(note_obj.created_at)
                }
                workflow.current_step = "added_note"
                next_step = "search_candidates"
            elif intent == "search_candidates":
                search_params = data.get("search_params")
                if not search_params:
                    # Use OpenAI LLM to extract job_title and location
                    prompt = f"""
Given the following user query, extract the job title and location as JSON.

Query: {query}

Return ONLY this JSON structure:
{{
  "job_title": "...",
  "location": "..."
}}
"""
                    logging.info(f"[search_candidates] Using OpenAI for job_title/location extraction.")
                    response = openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "You are an expert at extracting job search parameters from user queries."},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.1,
                        max_tokens=100
                    )
                    content = response.choices[0].message.content.strip()
                    import json
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    params = json.loads(content)
                    job_title = params.get("job_title", "").strip()
                    location = params.get("location", "").strip()
                    if not job_title:
                        raise HTTPException(status_code=400, detail="Could not extract job title from query.")
                    search_params = {
                        "query": job_title,
                        "location": location,
                        "max_pages": 1,
                        "job_id": job_id
                    }
                # Always call the search API with the mapped parameters
                service = SerpAPIService()
                result = await service.search_profiles(search_params)
                # Store results in serpapi_results table
                if result and 'linkedin_urls' in result:
                    service.batch_insert_results(db, job_id, result['linkedin_urls'], result['query'])
                workflow.current_step = "searched_candidates"
                next_step = "batch_candidates"
            elif intent == "batch_candidates":
                if not job_id:
                    raise HTTPException(status_code=400, detail="Missing job_id for batch processing.")
                batch_service = RapidAPIBatchService()
                batch_service.fetch_and_store_candidates(db, job_id, user_id)
                # Do not include the full result in the response for this intent
                result = {"message": "Batch processing started or completed. See logs or database for details."}
                workflow.current_step = "batched_candidates"
                next_step = "rank_candidates"
            elif intent == "rank_candidates":
                if not job_id:
                    raise HTTPException(status_code=400, detail="Missing job_id for ranking.")
                ranking_result = extract_info(job_id, save_to_db=True)
                
                # Extract only top 5 candidates with key information
                if ranking_result and 'merged_df' in ranking_result:
                    merged_df = ranking_result['merged_df']
                    if hasattr(merged_df, 'to_dict'):
                        df_dict = merged_df.to_dict('records')
                        
                        # Sort by New_ranked_Index to get top candidates
                        sorted_candidates = sorted(df_dict, key=lambda x: x.get('New_ranked_Index', 999))
                        
                        # Extract only top 5 candidates with key information
                        top_5_candidates = []
                        valid_candidates = []
                        
                        # First, filter out invalid candidates
                        for candidate in df_dict:
                            name = candidate.get('Name', '').strip()
                            linkedin_url = candidate.get('Extracted_LinkedIn_URLs', '')
                            score = candidate.get('Scores', 0)
                            
                            # Skip candidates with empty names, invalid URLs, or zero scores
                            if (name and 
                                name != " " and 
                                linkedin_url and 
                                "linkedin.com/pub/dir" not in linkedin_url and
                                score > 0):
                                valid_candidates.append(candidate)
                        
                        # Sort by score (highest first)
                        valid_candidates.sort(key=lambda x: x.get('Scores', 0), reverse=True)
                        
                        # Take top 5 valid candidates
                        for i, candidate in enumerate(valid_candidates[:5]):
                            # Get most recent experience
                            experiences = candidate.get('Experiences', [])
                            most_recent_position = "None"
                            most_recent_company = "None"
                            total_experience = "N/A"
                            if experiences:
                                most_recent_exp = experiences[0]  # First experience is most recent
                                most_recent_position = most_recent_exp.get('Position', 'None')
                                most_recent_company = most_recent_exp.get('Company', 'None')
                                # Calculate total experience if possible
                                try:
                                    from dateutil import parser as date_parser
                                    from datetime import datetime
                                    total_months = 0
                                    for exp in experiences:
                                        start = exp.get('Tenure', '').split('-')[0].strip()
                                        end = exp.get('Tenure', '').split('-')[-1].strip()
                                        if start and end:
                                            try:
                                                start_year = int(start.split('/')[-1])
                                                end_year = int(end.split('/')[-1])
                                                total_months += (end_year - start_year) * 12
                                            except Exception:
                                                continue
                                    if total_months > 0:
                                        total_experience = f"{total_months // 12} years"
                                except Exception:
                                    pass
                            # Get most recent university
                            universities = candidate.get('Universities', [])
                            most_recent_education = "None"
                            if universities:
                                most_recent_uni = universities[0]  # First university is most recent
                                uni_name = most_recent_uni.get('Name', 'None')
                                uni_degree = most_recent_uni.get('Degree', '')
                                uni_major = most_recent_uni.get('Major', '')
                                if uni_degree and uni_major:
                                    most_recent_education = f"{uni_name} ({uni_degree}, {uni_major})"
                                elif uni_degree:
                                    most_recent_education = f"{uni_name} ({uni_degree})"
                                else:
                                    most_recent_education = uni_name
                            # Skills
                            skills = candidate.get('Skills', [])
                            if isinstance(skills, list):
                                skills_str = ', '.join(skills)
                            else:
                                skills_str = str(skills)
                            # Background summary
                            background = f"{most_recent_position} at {most_recent_company} with {total_experience} experience"
                            # Create enriched candidate object
                            enriched_candidate = {
                                "rank": i + 1,  # Start from 1, not 0
                                "name": candidate.get('Name', 'Unknown').strip(),
                                "position": most_recent_position,
                                "company": most_recent_company,
                                "background": background,
                                "education": most_recent_education,
                                "skills": skills if isinstance(skills, list) else [skills_str],
                                "total_experience": total_experience,
                                "score": round(candidate.get('Scores', 0), 2),
                                "linkedin_url": candidate.get('Extracted_LinkedIn_URLs', '')
                            }
                            top_5_candidates.append(enriched_candidate)
                        
                        # Create simplified response
                        simplified_result = {
                            "top_5_candidates": top_5_candidates,
                            "total_candidates_processed": len(df_dict),
                            "ranking_completed": True
                        }
                        
                        return JSONResponse({
                            "status": "success",
                            "message": "Candidates ranked successfully",
                            "data": simplified_result
                        })
                    else:
                        return JSONResponse({
                            "status": "error",
                            "message": "No ranking data available",
                            "data": {"top_5_candidates": []}
                        })
                else:
                    return JSONResponse({
                        "status": "error",
                        "message": "No candidates found for ranking",
                        "data": {"top_5_candidates": []}
                    })
            else:
                error = "Could not determine intent. Please clarify your request."
                next_step = workflow.current_step
        # Update workflow state
        if workflow:
            workflow.status = "error" if error else "in_progress"
            from sqlalchemy import func
            workflow.last_updated = db.execute(func.now()).scalar()
            db.commit()
            db.refresh(workflow)
    except Exception as e:
        logging.exception("Error in /chatAPI workflow step")
        
        # If it's a tenacity.RetryError, extract the real error message
        if isinstance(e, tenacity.RetryError) and hasattr(e, 'last_attempt'):
            real_exc = e.last_attempt.exception()
            if real_exc and "Parsing was not successful" in str(real_exc):
                error = "Parsing was not successful. Please provide a more detailed or valid job description."
            else:
                error = str(real_exc) if real_exc else str(e)
        elif "Parsing was not successful" in str(e):
            error = "Parsing was not successful. Please provide a more detailed or valid job description."
        else:
            error = str(e)

    # Unified response (always return job_id for frontend)
    return JSONResponse({
        "workflow_state": {
            "job_id": str(job_id) if job_id else None,
            "user_id": str(workflow.user_id) if workflow and workflow.user_id else str(user_id),
            "current_step": workflow.current_step if workflow else None,
            "status": workflow.status if workflow else ("error" if error else "in_progress"),
            "last_updated": str(workflow.last_updated) if workflow else None,
        },
        "result": result,
        "error": error,
        "next_step": next_step,
        "guidance": f"Next, you should: {next_step}" if next_step else "Workflow complete or awaiting clarification."
    }) 

@router.post("/api/v1/job/full-process")
async def full_process(
    job_id: str = Body(...),
    job_title: str = Body(None),
    location: str = Body(None),
    max_pages: int = Body(1),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    Orchestrate the full candidate ranking workflow: search, extract, rank.
    Returns top 5 candidates and step logs for debugging.
    """
    step_logs = []
    try:
        # CREDIT CHECK
        from app.db.db_models import User
        user_obj = db.query(User).filter(User.id == current_user.id).first()
        if not user_obj:
            return {"status": "error", "message": "User not found.", "step_logs": step_logs}
        if getattr(user_obj, 'credit', 1) <= 0:
            return {"status": "error", "message": "Your one trial credit has been used.", "step_logs": step_logs}
        # Step 1: Check job_descriptions table
        from app.db.db_models import JobDescription
        jd = db.query(JobDescription).filter(
            JobDescription.id == job_id,
            JobDescription.user_id == current_user.id
        ).first()
        if not jd:
            step_logs.append(f"Job description not found for job_id {job_id} or access denied.")
            return {"status": "error", "message": "Job description not found or access denied.", "step_logs": step_logs}
        db_job_title = getattr(jd, 'job_title', None)
        db_location = getattr(jd, 'location', None)
        # Use provided values to override DB if present
        final_job_title = job_title or db_job_title
        final_location = location or db_location
        # Check for missing fields
        missing_fields = []
        if not final_job_title or final_job_title in ("N/A", ""):
            missing_fields.append("job_title")
        if not final_location or final_location in ("N/A", ""):
            missing_fields.append("location")
        if missing_fields:
            step_logs.append(f"Missing required field(s): {', '.join(missing_fields)}. Please provide them.")
            return {
                "status": "error",
                "message": f"Missing required field(s): {', '.join(missing_fields)}. Please provide them.",
                "step_logs": step_logs
            }
        step_logs.append(f"Checked job description: found (title: {final_job_title}, location: {final_location})")
        # Clean job title and location for search
        def clean_job_title(title):
            if not title:
                return ''
            return title.split(',')[0].strip()
        def clean_location(location):
            if not location:
                return ''
            import re
            location = re.sub(r'\s*\(.*?\)', '', location)
            return location.split(',')[0].strip()
        cleaned_job_title = clean_job_title(final_job_title)
        cleaned_location = clean_location(final_location)
        step_logs.append(f"Cleaned job title: {cleaned_job_title}, Cleaned location: {cleaned_location}")
        # Step 3: Search LinkedIn (SerpAPI)
        search_params = {
            "job_id": job_id,
            "query": cleaned_job_title,
            "location": cleaned_location,
            "max_pages": max_pages
        }
        from app.services.serpapi_service import SerpAPIService
        serpapi_service = SerpAPIService()
        search_result = await serpapi_service.search_profiles(search_params)
        linkedin_urls = search_result.get("linkedin_urls", [])
        step_logs.append(f"Searched LinkedIn: {len(linkedin_urls)} URLs found.")
        if not linkedin_urls:
            return {"status": "error", "message": "No LinkedIn URLs found.", "step_logs": step_logs}
        # PATCH: Insert LinkedIn URLs into the database for this job_id
        serpapi_service.batch_insert_results(db, job_id, linkedin_urls, search_result.get('query', ''))
        step_logs.append(f"Inserted {len(linkedin_urls)} LinkedIn URLs into the database.")
        # Step 4: Batch extract candidates
        from app.services.rapidapi_batch_service import RapidAPIBatchService
        rapidapi_batch_service = RapidAPIBatchService()
        candidates = rapidapi_batch_service.fetch_and_store_candidates(db, job_id, current_user.id)
        step_logs.append(f"Batch extraction: {len(candidates)} candidates extracted.")
        if not candidates:
            return {"status": "error", "message": "No candidates extracted.", "step_logs": step_logs}
        # Step 5: Rank candidates
        from app.rank.ranking import extract_info
        ranking_result = extract_info(job_id, save_to_db=True)
        top_5_candidates = []
        if ranking_result and 'merged_df' in ranking_result:
            merged_df = ranking_result['merged_df']
            if hasattr(merged_df, 'to_dict'):
                df_dict = merged_df.to_dict('records')
                # Sort by New_ranked_Index to get top candidates
                sorted_candidates = sorted(df_dict, key=lambda x: x.get('New_ranked_Index', 999))
                # First, filter out invalid candidates
                valid_candidates = []
                for candidate in df_dict:
                    name = candidate.get('Name', '').strip()
                    linkedin_url = candidate.get('Extracted_LinkedIn_URLs', '')
                    score = candidate.get('Scores', 0)
                    if (name and name != " " and linkedin_url and "linkedin.com/pub/dir" not in linkedin_url and score > 0):
                        valid_candidates.append(candidate)
                # Sort by score (highest first)
                valid_candidates.sort(key=lambda x: x.get('Scores', 0), reverse=True)
                # Take top 5 valid candidates
                for i, candidate in enumerate(valid_candidates[:5]):
                    # Get most recent experience
                    experiences = candidate.get('Experiences', [])
                    most_recent_position = "None"
                    most_recent_company = "None"
                    total_experience = candidate.get('Tenure_discrete', "N/A")
                    if experiences:
                        most_recent_exp = experiences[0]  # First experience is most recent
                        most_recent_position = most_recent_exp.get('Position', 'None')
                        most_recent_company = most_recent_exp.get('Company', 'None')
                    # Get most recent university
                    universities = candidate.get('Universities', [])
                    most_recent_education = "None"
                    if universities:
                        most_recent_uni = universities[0]  # First university is most recent
                        uni_name = most_recent_uni.get('Name', 'None')
                        uni_degree = most_recent_uni.get('Degree', '')
                        uni_major = most_recent_uni.get('Major', '')
                        if uni_degree and uni_major:
                            most_recent_education = f"{uni_name} ({uni_degree}, {uni_major})"
                        elif uni_degree:
                            most_recent_education = f"{uni_name} ({uni_degree})"
                        else:
                            most_recent_education = uni_name
                    # Skills
                    skills = candidate.get('Skills', [])
                    if isinstance(skills, list):
                        skills_str = ', '.join(skills)
                    else:
                        skills_str = str(skills)
                    # Background summary
                    background = f"{most_recent_position} at {most_recent_company} with {total_experience} years experience"
                    # Create enriched candidate object
                    enriched_candidate = {
                        "rank": i + 1,  # Start from 1, not 0
                        "name": candidate.get('Name', 'Unknown').strip(),
                        "position": most_recent_position,
                        "company": most_recent_company,
                        "background": background,
                        "education": most_recent_education,
                        "skills": skills if isinstance(skills, list) else [skills_str],
                        "total_experience": total_experience,
                        "score": round(candidate.get('Scores', 0), 2),
                        "linkedin_url": candidate.get('Extracted_LinkedIn_URLs', '')
                    }
                    top_5_candidates.append(enriched_candidate)
        step_logs.append(f"Ranking: top {len(top_5_candidates)} candidates returned.")
        # PATCH: Decrement credit after successful ranking
        if top_5_candidates:
            if getattr(user_obj, 'credit', 1) > 0:
                user_obj.credit -= 1
                db.commit()
                step_logs.append("User credit decremented by 1 after successful ranking.")
        return {
            "status": "success",
            "message": "Ranking completed",
            "step_logs": step_logs,
            "data": {
                "top_5_candidates": top_5_candidates
            }
        }
    except Exception as e:
        step_logs.append(f"Error: {str(e)}")
        return {"status": "error", "message": str(e), "step_logs": step_logs} 

@router.get("/api/v1/job/top-candidates")
async def get_top_candidates(
    job_id: str = Query(...),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    Return the top 5 ranked candidates for a given job_id, using the ranking_results table.
    Skills and other info are fetched from job_descriptions.information_extracted_json using FileName or LinkedIn URL.
    """
    from app.db.db_models import RankingResult, JobDescription
    import ast
    step_logs = []
    try:
        # Check job_descriptions table for access and get info_extracted_json
        jd = db.query(JobDescription).filter(
            JobDescription.id == job_id,
            JobDescription.user_id == current_user.id
        ).first()
        if not jd:
            return {"status": "error", "message": "Job description not found or access denied.", "step_logs": step_logs}
        info_extracted = jd.information_extracted_json or []
        # Build a lookup by FileName and LinkedIn URL
        file_lookup = {}
        url_lookup = {}
        for entry in info_extracted:
            if not isinstance(entry, dict):
                continue
            fname = entry.get("FileName")
            url = entry.get("Contact", {}).get("LinkedIn")
            if fname:
                file_lookup[fname] = entry
            if url:
                url_lookup[url] = entry
        # Query the ranking_results table for this job_id, order by score descending
        candidates = db.query(RankingResult).filter(
            RankingResult.job_id == job_id
        ).order_by(RankingResult.scores.desc()).limit(5).all()
        if not candidates:
            return {"status": "error", "message": "No ranking data available.", "data": {"top_5_candidates": []}}
        top_5_candidates = []
        for candidate in candidates:
            # Try to get FileName or LinkedIn URL for lookup
            file_name = getattr(candidate, 'file_name', None) or getattr(candidate, 'Just_file_name', None) or getattr(candidate, 'FileName', None)
            linkedin_url = getattr(candidate, 'linkedin_url', None)
            info_entry = None
            if file_name and file_name in file_lookup:
                info_entry = file_lookup[file_name]
            elif linkedin_url and linkedin_url in url_lookup:
                info_entry = url_lookup[linkedin_url]
            # Get skills from info_entry if possible
            skills = []
            position = ''
            company = ''
            if info_entry:
                if isinstance(info_entry.get("Skills"), list):
                    skills = info_entry["Skills"]
                # Get most recent experience for position and company
                experiences = info_entry.get("Experiences", [])
                if experiences and isinstance(experiences, list):
                    most_recent_exp = experiences[0]
                    position = most_recent_exp.get("Position", "")
                    company = most_recent_exp.get("Company", "")
            # Build education string if possible
            education = getattr(candidate, 'education', None) or ""
            if not education and info_entry:
                # Try to build from Universities field
                universities = info_entry.get("Universities", [])
                if universities and isinstance(universities, list):
                    uni = universities[0]
                    uni_name = uni.get("Name", "")
                    uni_degree = uni.get("Degree", "")
                    uni_major = uni.get("Major", "")
                    if uni_degree and uni_major:
                        education = f"{uni_name} ({uni_degree}, {uni_major})"
                    elif uni_degree:
                        education = f"{uni_name} ({uni_degree})"
                    else:
                        education = uni_name
            # Use new_ranked_index for rank (add 1 if you want to start from 1)
            rank = getattr(candidate, 'new_ranked_index', None)
            if rank is not None:
                try:
                    rank = int(rank)
                except Exception:
                    rank = 0
                rank = rank + 1  # If you want rank to start from 1
            else:
                rank = 0
            enriched_candidate = {
                "candidate_id": str(getattr(candidate, 'id', '')),
                "rank": rank,
                "name": getattr(candidate, 'name', None),
                "position": position,
                "company": company,
                "background": f"{position} at {company} with {getattr(candidate, 'tenure_discrete', 'N/A')} years experience",
                "education": education,
                "skills": skills,
                "total_experience": getattr(candidate, 'tenure_discrete', 'N/A'),
                "score": float(getattr(candidate, 'scores', 0)),
                "linkedin_url": getattr(candidate, 'linkedin_url', '')
            }
            top_5_candidates.append(enriched_candidate)
        return {"status": "success", "data": {"top_5_candidates": top_5_candidates}}
    except Exception as e:
        return {"status": "error", "message": str(e), "data": {"top_5_candidates": []}} 

@router.post("/parse-job-pdf")
async def parse_job_pdf_redirect(
    pdf_attachment: UploadFile = File(...),
    query: str = Form(""),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    def _extract_pdf_text(pdf_bytes: bytes) -> str:
        pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
        return "\n".join(page.get_text() for page in pdf_doc)

    contents = await pdf_attachment.read()
    pdf_jd_content = _extract_pdf_text(contents)

    payload_dict = {
        "query": f"{query}\n{pdf_jd_content}",
        "extra": {"intent": "parse_jd"}
    }
    payload_bytes = json.dumps(payload_dict).encode("utf-8")

    scope = {
        "type": "http",
        "method": "POST",
        "path": "/chatAPI",
        "headers": [(b"content-type", b"application/json")],
        "query_string": b"",
        "client": None,
        "server": None,
        "scheme": "http",
        "http_version": "1.1",
    }

    async def receive() -> dict:
        return {"type": "http.request", "body": payload_bytes}

    fake_request = Request(scope, receive=receive)
    fake_request._body = payload_bytes
    fake_request._json = payload_dict
    fake_request.body = lambda: payload_bytes
    async def _json():
        return payload_dict

    fake_request.json = _json

    return await chat_api(fake_request, db, current_user)