"""
DashChat /chatAPI Orchestration Endpoint
Implements LLM-powered workflow orchestration for job/candidate processing.
"""
from fastapi import APIRouter, Depends, HTTPException, Request, Body, Query
from fastapi.responses import JSONResponse
from fastapi import UploadFile, File, Form
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.auth.security import get_current_user
from app.db.db_models import JobWorkflowState
from app.services.jd_service import JobDescriptionService
from app.services.scraping_service import ScrapingService
from app.services.note_service import NoteService
from app.services.serpapi_service import SerpAPIService
from app.services.rapidapi_batch_service import RapidAPIBatchService
from app.models.note_schemas import NoteCreate
from app.utils.intent_utils import detect_parse_jd_intent

import logging
from app.rank.ranking import extract_info
import tenacity
import fitz
import json
from celery.result import AsyncResult
from app.celery_worker import celery_app
from app.services.smart_intent_detector import SmartIntentDetector
from app.services.dashboard_service import dashboard_post_jd


__all__ = ["router"]

router = APIRouter()

# --- Global OpenAI client and key ---
OPENAI_API_KEY = "********************************************************************************************************************************************************************"
if not OPENAI_API_KEY:
    raise RuntimeError("OPENAI_API_KEY not set in environment.")
from openai import OpenAI
openai_client = OpenAI(api_key=OPENAI_API_KEY)

@router.post("/chatAPI")
async def chat_api(
    request: Request,
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    IMPROVED: Orchestrate multi-step candidate processing workflow with smart intent detection.
    Tracks workflow state, routes to correct backend function, and returns unified response.
    """
    data = await request.json()
    user_id = current_user.id
    query = data.get("query", "")
    job_id = data.get("job_id")  # May be None for first step

    # ✅ CHANGE 1: Initialize smart intent detector (assume class exists)
    intent_detector = SmartIntentDetector(openai_client)
    
    # Get current workflow step for context
    current_step = None
    if job_id:
        workflow = db.query(JobWorkflowState).filter_by(user_id=user_id, job_id=job_id).first()
        current_step = workflow.current_step if workflow else None

    # ✅ CHANGE 2: Use smart intent detection instead of simple function
    intent_analysis = intent_detector.detect_intent_and_extract_data(query, current_step)
    intent = intent_analysis["intent"]
    extracted_data = intent_analysis["extracted_data"]
    confidence = intent_analysis["confidence"]
    
    # ✅ CHANGE 3: Log intent detection for debugging
    logging.info(f"Intent Analysis: {intent_analysis}")
    
    # ✅ CHANGE 4: Handle low confidence cases
    if confidence < 0.6:
        return JSONResponse({
            "workflow_state": {
                "job_id": None,
                "user_id": str(user_id),
                "current_step": None,
                "status": "clarification_needed",
                "last_updated": None,
            },
            "intent_analysis": {
                "detected_intent": intent,
                "confidence": confidence,
                "reasoning": intent_analysis.get("reasoning", ""),
                "urls_found": len(intent_analysis.get("urls_analysis", {}).get("all_urls", [])),
                "suggestions": [
                    "Parse a job description from text",
                    "Parse a job description from URL",
                ]
            },
            "result": None,
            "error": f"Low confidence in intent detection. {intent_analysis.get('reasoning', '')}",
            "next_step": None,
            "guidance": "Please clarify your request with more details."
        })

    result = None
    error = None
    next_step = None
    workflow = None

    try:
        # --- FIRST STEP: Parse JD (raw or URL), no job_id yet ---
        if not job_id and intent in ("parse_jd", "parse_jd_url"):
            if intent == "parse_jd":
                # ✅ CHANGE 5: Use extracted JD text from smart detection
                jd_text = extracted_data.get("job_description_text") or data.get("jd_text")
                
                if not jd_text:
                    raise HTTPException(status_code=400, detail="Missing job description text.")
                
                service = JobDescriptionService()
                result = await service.parse_and_save_job_description(db, jd_text, user_id, parse_method="raw")
                
                # Check for missing fields
                missing_fields = []
                if not result.get("Role") or result.get("Role", "N/A").strip().upper() == "N/A":
                    missing_fields.append("job_title")
                if not result.get("Location") or result.get("Location", "N/A").strip().upper() == "N/A":
                    missing_fields.append("location")
                if missing_fields:
                    result["missing_fields"] = missing_fields
                
                # Get the new job_id from the DB
                from app.db.db_models import JobDescription
                new_jd = db.query(JobDescription).filter_by(user_id=user_id).order_by(JobDescription.created_at.desc()).first()
                job_id = str(new_jd.id)
                
            elif intent == "parse_jd_url":
                # ✅ CHANGE 6: Use primary URL from smart detection
                url = extracted_data.get("primary_url") or extracted_data.get("url") or data.get("url")
                
                if not url:
                    raise HTTPException(status_code=400, detail="Missing job description URL.")
                
                # ✅ CHANGE 7: Log URL being processed
                logging.info(f"Processing job URL: {url}")
                
                scraping_service = ScrapingService()
                scraped = await scraping_service.scrape_job_description(url)
                
                logging.info(f"Raw scraped job description output: {scraped}")
                
                if scraped is None:
                    logging.error(f"Scraping failed or returned nothing for URL: {url}")
                    raise HTTPException(status_code=400, detail="Failed to scrape job description.")
                
                data_scraped = scraped.get("data", {})
                job_info = data_scraped.get("job_info", {})

                job_description = (
                    job_info.get("description")
                    or data_scraped.get("full_text")
                    or scraped.get("full_text")
                    or scraped.get("description")
                    or ""
                )

                metadata_keys = [
                    "location", "employment_status", "is_remote_allowed", "listed_at",
                    "expire_at", "job_url", "experience_level", "workplace_types",
                    "job_state", "country_code", "job_posting_id", "is_reposted",
                    "job_application_limit_reached", "eligible_for_referrals"
                ]
                metadata = {k: job_info.get(k) for k in metadata_keys if job_info.get(k) is not None}

                import json
                extra_text = "\n\n[EXTRA INFO]\n" + json.dumps(metadata, ensure_ascii=False, indent=2)
                jd_text = job_description.strip() + extra_text

                if not jd_text:
                    logging.error(f"Scraped result missing description: {scraped}")
                    # Failsafe logic
                    normalized = {
                        "Role": "N/A",
                        "Company": "N/A", 
                        "Benefits": [],
                        "Location": "N/A",
                        "Requirements": {
                            "Skills": [],
                            "Experience": "N/A",
                            "Minimum Qualification": "N/A",
                            "Preferred Qualification": "N/A"
                        },
                        "Responsibilities": [],
                        "SkillsPreference": [{"name": "", "status": "R"}],
                        "Suggested_Job_Titles": []
                    }
                    
                    from app.db.db_models import JobDescription
                    new_jd = JobDescription(
                        user_id=user_id,
                        job_title="N/A",
                        location="N/A", 
                        jd_text=normalized,
                        parse_method="url"
                    )
                    db.add(new_jd)
                    db.commit()
                    db.refresh(new_jd)
                    result = normalized
                    job_id = str(new_jd.id)
                    
                    missing_fields = []
                    if not result.get("Role") or result.get("Role", "N/A").strip().upper() == "N/A":
                        missing_fields.append("job_title")
                    if not result.get("Location") or result.get("Location", "N/A").strip().upper() == "N/A":
                        missing_fields.append("location")
                    if missing_fields:
                        result["missing_fields"] = missing_fields
                else:
                    parser = JobDescriptionService()
                    normalized = await parser.parse_and_save_job_description(db, jd_text, user_id, parse_method="url")
                    result = normalized
                    
                    missing_fields = []
                    if not result.get("Role") or result.get("Role", "N/A").strip().upper() == "N/A":
                        missing_fields.append("job_title")
                    if not result.get("Location") or result.get("Location", "N/A").strip().upper() == "N/A":
                        missing_fields.append("location")
                    if missing_fields:
                        result["missing_fields"] = missing_fields
                    
                    from app.db.db_models import JobDescription
                    new_jd = db.query(JobDescription).filter_by(user_id=user_id).order_by(JobDescription.created_at.desc()).first()
                    job_id = str(new_jd.id)
            
            # Create workflow state
            workflow = JobWorkflowState(user_id=user_id, job_id=job_id, current_step="parsed_jd", status="in_progress")
            db.add(workflow)
            db.commit()
            db.refresh(workflow)
            next_step = "add_note"

            # post JD to Dashboard
            dashboard_post_jd(job_id, user_id, result)
            
        else:
            # --- SUBSEQUENT STEPS: job_id is provided ---
            workflow = db.query(JobWorkflowState).filter_by(user_id=user_id, job_id=job_id).first()
            if not workflow:
                raise HTTPException(status_code=404, detail="Workflow state not found for this job_id.")
                
            if intent == "add_note":
                # ✅ CHANGE 8: Use extracted note text from smart detection
                note_text = extracted_data.get("note_text") or data.get("note_text")
                
                if not note_text or not job_id:
                    raise HTTPException(status_code=400, detail="Missing note text or job_id.")
                
                note_service = NoteService()
                note = NoteCreate(job_id=job_id, note_text=note_text)
                note_obj = note_service.create_note(db, user_id, note)
                
                result = {
                    "id": str(note_obj.id),
                    "note_text": note_obj.note_text,
                    "created_at": str(note_obj.created_at)
                }
                workflow.current_step = "added_note"
                next_step = "search_candidates"
                
            elif intent == "search_candidates":
                # ✅ CHANGE 9: Use extracted search parameters from smart detection
                search_params = data.get("search_params")
                if not search_params:
                    job_title = extracted_data.get("job_title", "").strip()
                    location = extracted_data.get("location", "").strip()
                    
                    if not job_title:
                        raise HTTPException(status_code=400, detail="Could not extract job title from query.")
                    
                    search_params = {
                        "query": job_title,
                        "location": location,
                        "max_pages": 1,
                        "job_id": job_id
                    }
                
                service = SerpAPIService()
                result = await service.search_profiles(search_params)
                
                if result and 'linkedin_urls' in result:
                    service.batch_insert_results(db, job_id, result['linkedin_urls'], result['query'])
                    
                workflow.current_step = "searched_candidates"
                next_step = "batch_candidates"
                
            elif intent == "batch_candidates":
                if not job_id:
                    raise HTTPException(status_code=400, detail="Missing job_id for batch processing.")
                    
                batch_service = RapidAPIBatchService()
                batch_service.fetch_and_store_candidates(db, job_id, user_id)
                result = {"message": "Batch processing started or completed. See logs or database for details."}
                workflow.current_step = "batched_candidates"
                next_step = "rank_candidates"
                
            elif intent == "rank_candidates":
                if not job_id:
                    raise HTTPException(status_code=400, detail="Missing job_id for ranking.")
                    
                ranking_result = extract_info(job_id, save_to_db=True)
                
                # Extract only top 5 candidates with key information
                if ranking_result and 'merged_df' in ranking_result:
                    merged_df = ranking_result['merged_df']
                    if hasattr(merged_df, 'to_dict'):
                        df_dict = merged_df.to_dict('records')
                        
                        # Sort by New_ranked_Index to get top candidates
                        sorted_candidates = sorted(df_dict, key=lambda x: x.get('New_ranked_Index', 999))
                        
                        # Extract only top 5 candidates with key information
                        top_5_candidates = []
                        valid_candidates = []
                        
                        # First, filter out invalid candidates
                        for candidate in df_dict:
                            name = candidate.get('Name', '').strip()
                            linkedin_url = candidate.get('Extracted_LinkedIn_URLs', '')
                            score = candidate.get('Scores', 0)
                            
                            # Skip candidates with empty names, invalid URLs, or zero scores
                            if (name and 
                                name != " " and 
                                linkedin_url and 
                                "linkedin.com/pub/dir" not in linkedin_url and
                                score > 0):
                                valid_candidates.append(candidate)
                        
                        # Sort by score (highest first)
                        valid_candidates.sort(key=lambda x: x.get('Scores', 0), reverse=True)
                        
                        # Take top 5 valid candidates
                        for i, candidate in enumerate(valid_candidates[:5]):
                            # Get most recent experience
                            experiences = candidate.get('Experiences', [])
                            most_recent_position = "None"
                            most_recent_company = "None"
                            total_experience = "N/A"
                            if experiences:
                                most_recent_exp = experiences[0]  # First experience is most recent
                                most_recent_position = most_recent_exp.get('Position', 'None')
                                most_recent_company = most_recent_exp.get('Company', 'None')
                                # Calculate total experience if possible
                                try:
                                    from dateutil import parser as date_parser
                                    from datetime import datetime
                                    total_months = 0
                                    for exp in experiences:
                                        start = exp.get('Tenure', '').split('-')[0].strip()
                                        end = exp.get('Tenure', '').split('-')[-1].strip()
                                        if start and end:
                                            try:
                                                start_year = int(start.split('/')[-1])
                                                end_year = int(end.split('/')[-1])
                                                total_months += (end_year - start_year) * 12
                                            except Exception:
                                                continue
                                    if total_months > 0:
                                        total_experience = f"{total_months // 12} years"
                                except Exception:
                                    pass
                            # Get most recent university
                            universities = candidate.get('Universities', [])
                            most_recent_education = "None"
                            if universities:
                                most_recent_uni = universities[0]  # First university is most recent
                                uni_name = most_recent_uni.get('Name', 'None')
                                uni_degree = most_recent_uni.get('Degree', '')
                                uni_major = most_recent_uni.get('Major', '')
                                if uni_degree and uni_major:
                                    most_recent_education = f"{uni_name} ({uni_degree}, {uni_major})"
                                elif uni_degree:
                                    most_recent_education = f"{uni_name} ({uni_degree})"
                                else:
                                    most_recent_education = uni_name
                            # Skills
                            skills = candidate.get('Skills', [])
                            if isinstance(skills, list):
                                skills_str = ', '.join(skills)
                            else:
                                skills_str = str(skills)
                            # Background summary
                            background = f"{most_recent_position} at {most_recent_company} with {total_experience} experience"
                            # Create enriched candidate object
                            enriched_candidate = {
                                "rank": i + 1,  # Start from 1, not 0
                                "name": candidate.get('Name', 'Unknown').strip(),
                                "position": most_recent_position,
                                "company": most_recent_company,
                                "background": background,
                                "education": most_recent_education,
                                "skills": skills if isinstance(skills, list) else [skills_str],
                                "total_experience": total_experience,
                                "score": round(candidate.get('Scores', 0), 2),
                                "linkedin_url": candidate.get('Extracted_LinkedIn_URLs', '')
                            }
                            top_5_candidates.append(enriched_candidate)
                        
                        # Create simplified response
                        simplified_result = {
                            "top_5_candidates": top_5_candidates,
                            "total_candidates_processed": len(df_dict),
                            "ranking_completed": True
                        }
                        
                        return JSONResponse({
                            "status": "success",
                            "message": "Candidates ranked successfully",
                            "data": simplified_result
                        })
                    else:
                        return JSONResponse({
                            "status": "error",
                            "message": "No ranking data available",
                            "data": {"top_5_candidates": []}
                        })
                else:
                    return JSONResponse({
                        "status": "error",
                        "message": "No candidates found for ranking",
                        "data": {"top_5_candidates": []}
                    })
            else:
                error = "Could not determine intent. Please clarify your request."
                next_step = workflow.current_step
                
        # Update workflow state
        if workflow:
            workflow.status = "error" if error else "in_progress"
            from sqlalchemy import func
            workflow.last_updated = db.execute(func.now()).scalar()
            db.commit()
            db.refresh(workflow)
            
    except Exception as e:
        logging.exception("Error in /chatAPI workflow step")
        
        # If it's a tenacity.RetryError, extract the real error message
        if isinstance(e, tenacity.RetryError) and hasattr(e, 'last_attempt'):
            real_exc = e.last_attempt.exception()
            if real_exc and "Parsing was not successful" in str(real_exc):
                error = "Parsing was not successful. Please provide a more detailed or valid job description."
            else:
                error = str(real_exc) if real_exc else str(e)
        elif "Parsing was not successful" in str(e):
            error = "Parsing was not successful. Please provide a more detailed or valid job description."
        else:
            error = str(e)

    # ✅ CHANGE 10: Enhanced response with intent analysis info
    return JSONResponse({
        "workflow_state": {
            "job_id": str(job_id) if job_id else None,
            "user_id": str(workflow.user_id) if workflow and workflow.user_id else str(user_id),
            "current_step": workflow.current_step if workflow else None,
            "status": workflow.status if workflow else ("error" if error else "in_progress"),
            "last_updated": str(workflow.last_updated) if workflow else None,
        },
        "intent_analysis": {
            "detected_intent": intent,
            "confidence": confidence,
            "reasoning": intent_analysis.get("reasoning", ""),
            "urls_found": len(intent_analysis.get("urls_analysis", {}).get("all_urls", []))
        },
        "result": result,
        "error": error,
        "next_step": next_step,
        "guidance": f"Next, you should: {next_step}" if next_step else "Workflow complete or awaiting clarification."
    })

@router.post("/api/v1/job/full-process")
async def full_process(
    job_id: str = Body(...),
    job_title: str = Body(None),
    location: str = Body(None),
    max_pages: int = Body(1),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    Orchestrate the full candidate ranking workflow: search, extract, rank.
    Now runs as a background Celery job. Returns a job_id for polling.
    """
    # Package all arguments needed for the job
    job_args = {
        "job_id": job_id,
        "job_title": job_title,
        "location": location,
        "max_pages": max_pages,
        "user_id": current_user.id
    }
    # Enqueue the Celery task
    task = celery_app.send_task("app.tasks.full_process_job", args=[job_args])
    return {"worker_id": task.id, "status": "processing"}

@router.get("/api/v1/job/full-process/status/{job_id}")
def get_full_process_status(job_id: str):
    result = AsyncResult(job_id, app=celery_app)
    if result.state == 'PENDING':
        return {"status": "processing"}
    elif result.state == 'SUCCESS':
        return {"status": "done", "result": result.result}
    elif result.state == 'FAILURE':
        return {"status": "failed", "error": str(result.info)}
    else:
        return {"status": result.state}

@router.get("/api/v1/job/top-candidates")
async def get_top_candidates(
    job_id: str = Query(...),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    """
    Return the top 5 ranked candidates for a given job_id, using the ranking_results table.
    Skills and other info are fetched from job_descriptions.information_extracted_json using FileName or LinkedIn URL.
    """
    from app.db.db_models import RankingResult, JobDescription
    import ast
    step_logs = []
    try:
        # Check job_descriptions table for access and get info_extracted_json
        jd = db.query(JobDescription).filter(
            JobDescription.id == job_id,
            JobDescription.user_id == current_user.id
        ).first()
        if not jd:
            return {"status": "error", "message": "Job description not found or access denied.", "step_logs": step_logs}
        info_extracted = jd.information_extracted_json or []
        # Build a lookup by FileName and LinkedIn URL
        file_lookup = {}
        url_lookup = {}
        for entry in info_extracted:
            if not isinstance(entry, dict):
                continue
            fname = entry.get("FileName")
            url = entry.get("Contact", {}).get("LinkedIn")
            if fname:
                file_lookup[fname] = entry
            if url:
                url_lookup[url] = entry
        # Query the ranking_results table for this job_id, order by score descending
        candidates = db.query(RankingResult).filter(
            RankingResult.job_id == job_id
        ).order_by(RankingResult.scores.desc()).limit(5).all()
        if not candidates:
            return {"status": "error", "message": "No ranking data available.", "data": {"top_5_candidates": []}}
        top_5_candidates = []
        for candidate in candidates:
            # Try to get FileName or LinkedIn URL for lookup
            file_name = getattr(candidate, 'file_name', None) or getattr(candidate, 'Just_file_name', None) or getattr(candidate, 'FileName', None)
            linkedin_url = getattr(candidate, 'linkedin_url', None)
            info_entry = None
            if file_name and file_name in file_lookup:
                info_entry = file_lookup[file_name]
            elif linkedin_url and linkedin_url in url_lookup:
                info_entry = url_lookup[linkedin_url]
            # Get skills from info_entry if possible
            skills = []
            position = ''
            company = ''
            if info_entry:
                if isinstance(info_entry.get("Skills"), list):
                    skills = info_entry["Skills"]
                # Get most recent experience for position and company
                experiences = info_entry.get("Experiences", [])
                if experiences and isinstance(experiences, list):
                    most_recent_exp = experiences[0]
                    position = most_recent_exp.get("Position", "")
                    company = most_recent_exp.get("Company", "")
            # Build education string if possible
            education = getattr(candidate, 'education', None) or ""
            if not education and info_entry:
                # Try to build from Universities field
                universities = info_entry.get("Universities", [])
                if universities and isinstance(universities, list):
                    uni = universities[0]
                    uni_name = uni.get("Name", "")
                    uni_degree = uni.get("Degree", "")
                    uni_major = uni.get("Major", "")
                    if uni_degree and uni_major:
                        education = f"{uni_name} ({uni_degree}, {uni_major})"
                    elif uni_degree:
                        education = f"{uni_name} ({uni_degree})"
                    else:
                        education = uni_name
            # Use new_ranked_index for rank (add 1 if you want to start from 1)
            rank = getattr(candidate, 'new_ranked_index', None)
            if rank is not None:
                try:
                    rank = int(rank)
                except Exception:
                    rank = 0
                rank = rank + 1  # If you want rank to start from 1
            else:
                rank = 0
            enriched_candidate = {
                "candidate_id": str(getattr(candidate, 'id', '')),
                "rank": rank,
                "name": getattr(candidate, 'name', None),
                "position": position,
                "company": company,
                "background": f"{position} at {company} with {getattr(candidate, 'tenure_discrete', 'N/A')} years experience",
                "education": education,
                "skills": skills,
                "total_experience": getattr(candidate, 'tenure_discrete', 'N/A'),
                "score": float(getattr(candidate, 'scores', 0)),
                "linkedin_url": getattr(candidate, 'linkedin_url', '')
            }
            top_5_candidates.append(enriched_candidate)
        # Add user's current credit to the response
        from app.db.db_models import User
        user_obj = db.query(User).filter(User.id == current_user.id).first()
        credit = getattr(user_obj, 'credit', None)
        return {"status": "success", "data": {"top_5_candidates": top_5_candidates, "credit": credit}}
    except Exception as e:
        return {"status": "error", "message": str(e), "data": {"top_5_candidates": []}}

async def redirect_to_chat_with_plaintext(query: str, db, current_user, request, source_text: str):
    data = {"query": f"{query}\n{source_text}", "extra": {"intent": "parse_jd"}}
    payload_bytes = json.dumps(data).encode("utf-8")
    scope = {
        "type": "http",
        "method": "POST",
        "path": "/chatAPI",
        "headers": [
            (b"content-type", b"application/json"),
            (b"authorization", request.headers.get("authorization", "").encode("utf-8") if request.headers.get("authorization") else b"")
        ],
        "query_string": b"",
        "client": None,
        "server": None,
        "scheme": "http",
        "http_version": "1.1",
    }
    async def receive() -> dict:
        return {"type": "http.request", "body": payload_bytes}
    fake_request = Request(scope, receive=receive)
    fake_request._body = payload_bytes
    return await chat_api(fake_request, db, current_user)

def extract_pdf_text_from_bytes(pdf_bytes: bytes) -> str:
    pdf_doc = fitz.open(stream=pdf_bytes, filetype="pdf")
    return "\n".join(page.get_text() for page in pdf_doc)

def extract_docx_text_from_bytes(docx_bytes: bytes) -> str:
    from docx import Document
    from io import BytesIO
    document = Document(BytesIO(docx_bytes))
    return "\n".join([para.text for para in document.paragraphs])

@router.post("/parse-job-attachment")
async def parse_job_attachment_redirect(
    request: Request,
    attachment: UploadFile = File(...),
    query: str = Form(""),
    db: Session = Depends(get_db),
    current_user=Depends(get_current_user)
):
    try:
        file_content = await attachment.read()
        if attachment.filename.endswith(".pdf"):
            extracted_text = extract_pdf_text_from_bytes(file_content)
        elif attachment.filename.endswith(".docx"):
            extracted_text = extract_docx_text_from_bytes(file_content)
        else:
            raise HTTPException(status_code=400, detail="Only .pdf and .docx files are supported.")
        if not extracted_text.strip():
            logging.error("[Attachment Parse] Empty content extracted from the file.")
            return JSONResponse({
                "status": "error",
                "parse_attachment": True,
                "error": "The file appears to be empty or the content could not be extracted.",
            })
        logging.info(f"[Attachment Parse] Extracted content preview:\n{extracted_text[:500]}")
        return await redirect_to_chat_with_plaintext(query, db, current_user, request, extracted_text)
    except Exception as e:
        logging.exception(f"Error while parsing attachment: {e}")
        return JSONResponse({
            "status": "error",
            "parse_attachment": True,
            "error": "The file appears to be empty or the content could not be extracted.",
        })
    
@router.post("/get-parse-jd-intent")
async def test_parse_jd_intent(payload: dict):
    query = payload.get("query", "")
    intent = detect_parse_jd_intent(query)
    return {"intent": intent}