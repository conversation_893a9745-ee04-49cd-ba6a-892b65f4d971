from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends, status
from typing import Optional, List
from pydantic import BaseModel, HttpUrl
from app.models.schemas import JobDescription, JobDescriptionResponse, ErrorResponse
from app.services.jd_service import JobDescriptionService
from app.services.scraping_service import ScrapingService
from app.logger import logging
from app.models.job_description import JobDescriptionURL
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.db_models import User
from app.auth.security import get_current_user



router = APIRouter(prefix="/api/v1/jd", tags=["Job Descriptions"])

jd_service = JobDescriptionService()
scraping_service = ScrapingService()

class JobDescriptionRequest(BaseModel):
    description: str

class JobURLRequest(BaseModel):
    url: HttpUrl

@router.post("/parse", responses={400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}})
async def parse_job_description(
    request: JobDescriptionRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Parse a job description and save it to the database."""
    try:
        logging.info(f"User {current_user.id} parsing job description from text")
        result = await jd_service.parse_and_save_job_description(
            db=db,
            job_description_text=request.description,
            user_id=current_user.id,
            parse_method="raw-text"
            )
        return JSONResponse(content=result)
    except Exception as e:
        logging.error(f"Error processing job description: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/parse-url", responses={400: {"model": ErrorResponse}, 500: {"model": ErrorResponse}})
async def parse_job_description_from_url(
    job_url: JobURLRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Parse a job description from a URL and save it to the database."""
    try:
        logging.info(f"User {current_user.id} parsing job URL: {job_url.url}")
        
        scraped_data = await scraping_service.scrape_job_description(job_url.url)
        if not scraped_data or "full_text" not in scraped_data:
            raise HTTPException(status_code=404, detail="Could not scrape job description")
        
        result = await jd_service.parse_and_save_job_description(
            db=db,
            job_description_text=scraped_data["full_text"],
            user_id=current_user.id,
            parse_method="url"
        )
        return JSONResponse(content=result)
    except Exception as e:
        logging.error(f"Error processing job URL: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{job_id}", responses={404: {"model": ErrorResponse}, 500: {"model": ErrorResponse}})
async def get_job_description(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """Get a specific job description by ID, scoped to the current user."""
    try:
        result = await jd_service.get_job_description(db, job_id, current_user.id)
        if not result:
            raise HTTPException(status_code=404, detail="Job description not found")
        return JSONResponse(content=result)
    except Exception as e:
        logging.error(f"Error retrieving job description: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=List[dict])
async def list_job_descriptions(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """List all job descriptions for the current user."""
    try:
        return await jd_service.list_job_descriptions(db, user_id=current_user.id)
    except Exception as e:
        logging.error(f"Error listing job descriptions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 