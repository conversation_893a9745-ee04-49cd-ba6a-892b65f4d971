from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from pydantic import BaseModel, HttpUrl
from app.services.linkedin_service import LinkedInService
from app.services.scraping_service import ScrapingService
from app.logger import logging
from app.models.linkedin import LinkedIn<PERSON><PERSON>, LinkedInJobResponse



router = APIRouter()
linkedin_service = LinkedInService()
scraping_service = ScrapingService()

class SearchRequest(BaseModel):
    query: str
    max_pages: Optional[int] = 1

class SearchResponse(BaseModel):
    profiles: List[str]
    csv_path: str

@router.post("/search", response_model=SearchResponse)
async def search_profiles(request: SearchRequest):
    """
    Search for LinkedIn profiles based on the provided query.
    
    Args:
        request (SearchRequest): The search request containing the query and max_pages
        
    Returns:
        SearchResponse: The search results containing profile URLs and CSV path
    """
    try:
        # Search for profiles
        profiles = await linkedin_service.search_profiles(request.query, request.max_pages)
        
        if not profiles:
            raise HTTPException(status_code=404, detail="No profiles found")
            
        # Save profiles to CSV
        csv_path = await linkedin_service.save_profiles_to_csv(profiles)
        
        return SearchResponse(profiles=profiles, csv_path=csv_path)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/job/{job_url:path}")
async def get_job_details(job_url: str):
    """
    Get job details from a LinkedIn job posting URL.
    
    Args:
        job_url (str): The LinkedIn job posting URL
        
    Returns:
        dict: The job details
    """
    try:
        job_details = await scraping_service.scrape_job_description(job_url)
        
        if not job_details:
            raise HTTPException(status_code=404, detail="Could not fetch job details")
            
        return job_details
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/jobs", response_model=LinkedInJobResponse)
async def create_linkedin_job(job: LinkedInJob):
    """Create a new LinkedIn job posting."""
    try:
        logging.info(f"Creating LinkedIn job posting for title: {job.title}")
        return await linkedin_service.create_job(job)
    except Exception as e:
        logging.error(f"Error creating LinkedIn job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/jobs/{job_id}", response_model=LinkedInJobResponse)
async def get_linkedin_job(job_id: str):
    """Get a LinkedIn job posting by ID."""
    try:
        logging.info(f"Retrieving LinkedIn job with ID: {job_id}")
        job = await linkedin_service.get_job(job_id)
        if not job:
            logging.warning(f"LinkedIn job not found for ID: {job_id}")
            raise HTTPException(status_code=404, detail="Job not found")
        return job
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error retrieving LinkedIn job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/jobs", response_model=List[LinkedInJobResponse])
async def list_linkedin_jobs():
    """List all LinkedIn job postings."""
    try:
        logging.info("Listing all LinkedIn jobs")
        return await linkedin_service.list_jobs()
    except Exception as e:
        logging.error(f"Error listing LinkedIn jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/jobs/{job_id}/apply")
async def apply_to_linkedin_job(job_id: str):
    """Apply to a LinkedIn job posting."""
    try:
        logging.info(f"Applying to LinkedIn job with ID: {job_id}")
        return await linkedin_service.apply_to_job(job_id)
    except Exception as e:
        logging.error(f"Error applying to LinkedIn job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 