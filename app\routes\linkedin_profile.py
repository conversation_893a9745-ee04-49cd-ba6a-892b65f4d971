# from fastapi import APIRouter, HTTPException, BackgroundTasks
# from typing import List
# import time
# from app.services.linkedin_profile_service import LinkedInProfileService
# from app.models.linkedin_profile_schemas import (
#     ProfileDetailsRequest,
#     ProfileDetailsResponse,
#     MultipleProfilesRequest,
#     MultipleProfilesResponse,
#     CacheStatsResponse,
#     ClearCacheResponse,
#     LinkedInProfileData,
#     ExperienceItem
# )
# from app.logger import logging

# router = APIRouter(prefix="/linkedin-profile", tags=["LinkedIn Profile"])
# linkedin_profile_service = LinkedInProfileService()

# @router.post("/multiple", response_model=MultipleProfilesResponse)
# async def get_multiple_profiles(request: MultipleProfilesRequest):
#     """
#     Get profile details for multiple LinkedIn URLs with rate limiting.
    
#     This endpoint processes multiple LinkedIn profiles efficiently with:
#     - Rate limiting to avoid being blocked
#     - Caching for improved performance
#     - Batch processing statistics
#     """
#     try:
#         start_time = time.time()
        
#         # Convert URLs to strings
#         urls = [str(url) for url in request.linkedin_urls]
        
#         profiles_data = await linkedin_profile_service.get_multiple_profiles(
#             urls, 
#             max_concurrent=request.max_concurrent
#         )
        
#         total_duration = time.time() - start_time
        
#         # Process results
#         profiles = []
#         successful_count = 0
#         failed_count = 0
        
#         for profile_data in profiles_data:
#             if "error" in profile_data:
#                 failed_count += 1
#                 profiles.append(ProfileDetailsResponse(
#                     success=False,
#                     error=profile_data["error"],
#                     message="Failed to extract profile data"
#                 ))
#             else:
#                 successful_count += 1
#                 linkedin_profile = LinkedInProfileData(
#                     url=profile_data["url"],
#                     name=profile_data.get("name"),
#                     headline=profile_data.get("headline"),
#                     location=profile_data.get("location"),
#                     company=profile_data.get("company"),
#                     education=profile_data.get("education"),
#                     skills=profile_data.get("skills", []),
#                     experience=[
#                         ExperienceItem(
#                             company=exp.get("company"),
#                             position=exp.get("position"),
#                             duration=exp.get("duration")
#                         ) for exp in profile_data.get("experience", [])
#                     ],
#                     extraction_time=profile_data.get("extraction_time")
#                 )
                
#                 profiles.append(ProfileDetailsResponse(
#                     success=True,
#                     data=linkedin_profile,
#                     message="Profile data extracted successfully"
#                 ))
        
#         logging.info(f"Processed {len(urls)} profiles: {successful_count} successful, {failed_count} failed in {total_duration:.2f}s")
        
#         return MultipleProfilesResponse(
#             success=True,
#             profiles=profiles,
#             total_processed=len(urls),
#             successful_count=successful_count,
#             failed_count=failed_count
#         )
        
#     except Exception as e:
#         logging.error(f"Error in get_multiple_profiles: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"Failed to process multiple profiles: {str(e)}")

# @router.get("/cache/stats", response_model=CacheStatsResponse)
# async def get_cache_stats():
#     """
#     Get statistics about cached LinkedIn profiles.
    
#     Returns information about:
#     - Total number of cached profiles
#     - Cache directory location
#     - Total cache size
#     """
#     try:
#         stats = await linkedin_profile_service.get_cache_stats()
        
#         if "error" in stats:
#             return CacheStatsResponse(
#                 success=False,
#                 total_cached_profiles=0,
#                 cache_directory="",
#                 total_cache_size_bytes=0,
#                 total_cache_size_mb=0.0,
#                 error=stats["error"]
#             )
        
#         return CacheStatsResponse(
#             success=True,
#             total_cached_profiles=stats["total_cached_profiles"],
#             cache_directory=stats["cache_directory"],
#             total_cache_size_bytes=stats["total_cache_size_bytes"],
#             total_cache_size_mb=stats["total_cache_size_mb"]
#         )
        
#     except Exception as e:
#         logging.error(f"Error in get_cache_stats: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"Failed to get cache stats: {str(e)}")

# @router.delete("/cache/clear", response_model=ClearCacheResponse)
# async def clear_profile_cache(background_tasks: BackgroundTasks):
#     """
#     Clear all cached LinkedIn profile data.
    
#     This endpoint removes all cached profile files to free up storage space.
#     The operation is performed in the background to avoid blocking the response.
#     """
#     try:
#         def clear_cache_task():
#             try:
#                 result = linkedin_profile_service.clear_profile_cache()
#                 logging.info(f"Cache cleared: {result['cleared_count']} files")
#             except Exception as e:
#                 logging.error(f"Error in background cache clearing: {str(e)}")
        
#         # Run cache clearing in background
#         background_tasks.add_task(clear_cache_task)
        
#         return ClearCacheResponse(
#             success=True,
#             message="Cache clearing initiated in background",
#             cleared_count=0  # Will be updated in background
#         )
        
#     except Exception as e:
#         logging.error(f"Error in clear_profile_cache: {str(e)}")
#         raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")

# @router.get("/health")
# async def health_check():
#     """
#     Health check endpoint for LinkedIn profile service.
#     """
#     return {
#         "status": "healthy",
#         "service": "linkedin-profile",
#         "cache_directory": linkedin_profile_service.cache_dir
#     } 