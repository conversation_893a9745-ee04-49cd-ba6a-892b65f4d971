from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from app.db.database import get_db
from app.auth.security import get_current_user
from app.db.db_models import User
from app.models.note_schemas import NoteCreate, NoteResponse
from app.services.note_service import NoteService
from uuid import UUID

router = APIRouter(prefix="/api/v1/notes", tags=["Notes"])
note_service = NoteService()

@router.post("/", response_model=NoteResponse, status_code=status.HTTP_201_CREATED)
def create_note(
    note: NoteCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    db_note = note_service.create_note(db, user_id=current_user.id, note=note)
    return db_note

@router.get("/job/{job_id}", response_model=List[NoteResponse])
def list_notes_for_job(
    job_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    notes = note_service.list_notes_for_job(db, user_id=current_user.id, job_id=job_id)
    return notes 