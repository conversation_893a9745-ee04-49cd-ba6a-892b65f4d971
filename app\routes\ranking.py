from app.logger import logging
from typing import List
from fastapi import APIRouter, HTTPException, status, Request, Depends
from app.models.ranking import RankingRequest, RankingResponse
from app.services.ranking_service import RankingService
from pydantic import BaseModel
from app.rank.ranking import extract_info
from app.auth.security import get_current_user
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.db.db_models import RankingResult


router = APIRouter()
ranking_service = RankingService()

class RankingRequest(BaseModel):
    job_id: str

class CandidateEvaluationRequest(BaseModel):
    job_id: str
    candidate_id: str  # ranking_result id
    status: str  # 'approved' or 'rejected'
    candidate_note: str

@router.post("/rank", response_model=RankingResponse)
async def rank_candidates(request: RankingRequest):
    """Rank candidates based on job requirements."""
    try:
        logging.info(f"Ranking candidates for job ID: {request.job_id}")
        return await ranking_service.rank_candidates(request)
    except Exception as e:
        logging.error(f"Error ranking candidates: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/rankings/{job_id}", response_model=RankingResponse)
async def get_rankings(job_id: str):
    """Get rankings for a specific job."""
    try:
        logging.info(f"Retrieving rankings for job ID: {job_id}")
        rankings = await ranking_service.get_rankings(job_id)
        if not rankings:
            logging.warning(f"No rankings found for job ID: {job_id}")
            raise HTTPException(status_code=404, detail="Rankings not found")
        return rankings
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error retrieving rankings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/rankings", response_model=List[RankingResponse])
async def list_rankings():
    """List all rankings."""
    try:
        logging.info("Listing all rankings")
        return await ranking_service.list_rankings()
    except Exception as e:
        logging.error(f"Error listing rankings: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/ranking/run")
def run_ranking(request: RankingRequest, user=Depends(get_current_user)):
    try:
        result = extract_info(request.job_id, save_to_db=True)
        return {"status": "success", "message": f"Ranking completed for job_id {request.job_id}", "details": {"candidates_processed": len(result['merged_df'])}}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/candidate/evaluate")
def evaluate_candidate(
    req: CandidateEvaluationRequest,
    db: Session = Depends(get_db),
    user=Depends(get_current_user)
):
    from app.db.db_models import RankingResult
    try:
        candidate = db.query(RankingResult).filter(
            RankingResult.id == req.candidate_id,
            RankingResult.job_id == req.job_id
        ).first()
        if not candidate:
            return {"data": None, "message": "Candidate not found", "statusCode": 404}
        # Only update status and candidate_note
        candidate.status = req.status
        candidate.candidate_note = req.candidate_note
        db.commit()
        db.refresh(candidate)
        return {
            "data": {
                "candidate_id": str(candidate.id),
                "job_id": str(candidate.job_id),
                "status": candidate.status,
                "candidate_note": candidate.candidate_note
            },
            "message": f"Candidate {req.status} and note saved.",
            "statusCode": 200
        }
    except Exception as e:
        db.rollback()
        return {"data": None, "message": f"Database error: {str(e)}", "statusCode": 500} 