from fastapi import APIRouter, HTTPException, BackgroundTasks, UploadFile, File, Depends
from typing import List
import time
import os
import tempfile
import shutil
from sqlalchemy.orm import Session
from app.services.rapidapi_linkedin_service import RapidAPILinkedInService
from app.models.rapidapi_linkedin_schemas import (
    SingleProfileRequest,
    SingleProfileResponse,
    BatchProfilesRequest,
    BatchProfilesResponse,
    CSVProcessingRequest,
    CSVProcessingResponse,
    CacheStatsResponse,
    ClearCacheResponse,
    LinkedInProfileData,
    ContactInfo,
    University,
    Experience,
    BatchCandidatesRequest,
    BatchCandidatesResponse
)
from typing import Dict, Any
from app.logger import logging
from app.db.database import get_db
from app.auth.security import get_current_user
from app.db.db_models import User, JobDescription
from app.services.rapidapi_batch_service import RapidAPIBatchService

router = APIRouter(prefix="/rapidapi-linkedin", tags=["RapidAPI LinkedIn"])
rapidapi_service = None
rapidapi_batch_service = RapidAPIBatchService()

def get_rapidapi_service():
    """Get or create RapidAPI service instance."""
    global rapidapi_service
    if rapidapi_service is None:
        try:
            rapidapi_service = RapidAPILinkedInService()
        except ValueError as e:
            raise HTTPException(status_code=500, detail=f"RapidAPI service initialization failed: {str(e)}")
    return rapidapi_service

@router.post("/profile", response_model=SingleProfileResponse)
async def get_single_profile(request: SingleProfileRequest):
    """
    Fetch a single LinkedIn profile using RapidAPI.
    
    This endpoint extracts comprehensive profile data including:
    - Name and contact information
    - Location details
    - Education history
    - Work experience
    - Skills and endorsements
    
    The data is cached to improve performance for subsequent requests.
    """
    try:
        start_time = time.time()
        service = get_rapidapi_service()
        
        profile_data = await service.fetch_linkedin_profile(str(request.linkedin_url))
        transformed_data = service.transform_linkedin_data(profile_data)
        
        extraction_duration = time.time() - start_time
        
        if "error" in transformed_data:
            return SingleProfileResponse(
                success=False,
                error=transformed_data["error"],
                message="Failed to extract profile data"
            )
        
        # Convert to Pydantic model
        linkedin_profile = LinkedInProfileData(
            name=transformed_data["name"],
            contact=ContactInfo(
                linkedin=transformed_data["contact"]["linkedin"],
                location=transformed_data["contact"]["location"]
            ),
            universities=[
                University(
                    name=uni["name"],
                    location=uni["location"],
                    major=uni["major"],
                    degree=uni["degree"],
                    tenure=uni["tenure"]
                ) for uni in transformed_data["universities"]
            ],
            experiences=[
                Experience(
                    company=exp["company"],
                    location=exp["location"],
                    tenure=exp["tenure"],
                    position=exp["position"]
                ) for exp in transformed_data["experiences"]
            ],
            skills=transformed_data["skills"],
            file_name=transformed_data["file_name"]
        )
        
        logging.info(f"Successfully extracted profile data for {request.linkedin_url} in {extraction_duration:.2f}s")
        
        return SingleProfileResponse(
            success=True,
            data=linkedin_profile,
            message=f"Profile data extracted successfully in {extraction_duration:.2f}s"
        )
        
    except Exception as e:
        logging.error(f"Error in get_single_profile: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to extract profile data: {str(e)}")

@router.post("/batch", response_model=BatchProfilesResponse)
async def get_batch_profiles(request: BatchProfilesRequest):
    """
    Fetch multiple LinkedIn profiles using RapidAPI with batch processing.
    
    This endpoint processes multiple LinkedIn profiles efficiently with:
    - Rate limiting to respect API limits
    - Caching for improved performance
    - Batch processing statistics
    - Concurrent processing with configurable workers
    """
    try:
        start_time = time.time()
        service = get_rapidapi_service()
        
        # Convert URLs to strings
        urls = [str(url) for url in request.linkedin_urls]
        
        profiles_data = await service.fetch_and_transform_batched(
            urls, 
            max_workers=request.max_workers
        )
        
        total_duration = time.time() - start_time
        
        # Process results
        profiles = []
        successful_count = 0
        failed_count = 0
        
        for profile_data in profiles_data:
            if "error" in profile_data:
                failed_count += 1
                profiles.append(LinkedInProfileData(
                    name="",
                    contact=ContactInfo(),
                    universities=[],
                    experiences=[],
                    skills=[],
                    file_name="",
                    error=profile_data["error"]
                ))
            else:
                successful_count += 1
                linkedin_profile = LinkedInProfileData(
                    name=profile_data["name"],
                    contact=ContactInfo(
                        linkedin=profile_data["contact"]["linkedin"],
                        location=profile_data["contact"]["location"]
                    ),
                    universities=[
                        University(
                            name=uni["name"],
                            location=uni["location"],
                            major=uni["major"],
                            degree=uni["degree"],
                            tenure=uni["tenure"]
                        ) for uni in profile_data["universities"]
                    ],
                    experiences=[
                        Experience(
                            company=exp["company"],
                            location=exp["location"],
                            tenure=exp["tenure"],
                            position=exp["position"]
                        ) for exp in profile_data["experiences"]
                    ],
                    skills=profile_data["skills"],
                    file_name=profile_data["file_name"]
                )
                profiles.append(linkedin_profile)
        
        logging.info(f"Processed {len(urls)} profiles: {successful_count} successful, {failed_count} failed in {total_duration:.2f}s")
        
        return BatchProfilesResponse(
            success=True,
            profiles=profiles,
            total_processed=len(urls),
            successful_count=successful_count,
            failed_count=failed_count
        )
        
    except Exception as e:
        logging.error(f"Error in get_batch_profiles: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process batch profiles: {str(e)}")

@router.post("/csv", response_model=CSVProcessingResponse)
async def process_csv_file(request: CSVProcessingRequest):
    """
    Process a CSV file containing LinkedIn URLs and extract profile data.
    
    This endpoint:
    - Extracts LinkedIn URLs from the CSV file
    - Fetches profile data for each URL
    - Saves results as JSON and CSV files
    - Returns file paths and processing statistics
    """
    try:
        start_time = time.time()
        service = get_rapidapi_service()
        
        # Process the CSV file
        json_path, csv_path = await service.process_linkedin_csv(
            request.csv_file_path,
            request.output_dir
        )
        
        total_duration = time.time() - start_time
        
        if json_path and csv_path:
            # Count successful profiles
            successful_profiles = 0
            if os.path.exists(json_path):
                import json
                with open(json_path, 'r') as f:
                    data = json.load(f)
                    successful_profiles = len(data)
            
            # Count total profiles from CSV
            total_profiles = 0
            if os.path.exists(request.csv_file_path):
                import csv
                with open(request.csv_file_path, 'r') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        for value in row.values():
                            if value and "linkedin.com/in/" in str(value):
                                total_profiles += 1
            
            logging.info(f"CSV processing completed: {successful_profiles}/{total_profiles} profiles in {total_duration:.2f}s")
            
            return CSVProcessingResponse(
                success=True,
                json_path=json_path,
                csv_path=csv_path,
                total_profiles=total_profiles,
                successful_profiles=successful_profiles
            )
        else:
            return CSVProcessingResponse(
                success=False,
                error="Failed to process CSV file",
                total_profiles=0,
                successful_profiles=0
            )
        
    except Exception as e:
        logging.error(f"Error in process_csv_file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process CSV file: {str(e)}")

@router.post("/upload-csv", response_model=CSVProcessingResponse)
async def upload_and_process_csv(
    file: UploadFile = File(...),
    output_dir: str = "output"
):
    """
    Upload a CSV file and process LinkedIn URLs.
    
    This endpoint accepts a CSV file upload and processes it immediately.
    """
    try:
        # Create temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name
        
        # Process the uploaded file
        service = get_rapidapi_service()
        json_path, csv_path = await service.process_linkedin_csv(temp_file_path, output_dir)
        
        # Clean up temporary file
        os.unlink(temp_file_path)
        
        if json_path and csv_path:
            # Count successful profiles
            successful_profiles = 0
            if os.path.exists(json_path):
                import json
                with open(json_path, 'r') as f:
                    data = json.load(f)
                    successful_profiles = len(data)
            
            return CSVProcessingResponse(
                success=True,
                json_path=json_path,
                csv_path=csv_path,
                total_profiles=successful_profiles,  # Approximate
                successful_profiles=successful_profiles
            )
        else:
            return CSVProcessingResponse(
                success=False,
                error="Failed to process uploaded CSV file",
                total_profiles=0,
                successful_profiles=0
            )
        
    except Exception as e:
        logging.error(f"Error in upload_and_process_csv: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process uploaded CSV: {str(e)}")

@router.get("/cache/stats", response_model=CacheStatsResponse)
async def get_cache_stats():
    """
    Get statistics about cached LinkedIn profiles.
    
    Returns information about:
    - Total number of cached profiles
    - Cache directory location
    - Total cache size
    """
    try:
        service = get_rapidapi_service()
        stats = await service.get_cache_stats()
        
        if "error" in stats:
            return CacheStatsResponse(
                success=False,
                total_cached_profiles=0,
                cache_directory="",
                total_cache_size_bytes=0,
                total_cache_size_mb=0.0,
                error=stats["error"]
            )
        
        return CacheStatsResponse(
            success=True,
            total_cached_profiles=stats["total_cached_profiles"],
            cache_directory=stats["cache_directory"],
            total_cache_size_bytes=stats["total_cache_size_bytes"],
            total_cache_size_mb=stats["total_cache_size_mb"]
        )
        
    except Exception as e:
        logging.error(f"Error in get_cache_stats: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get cache stats: {str(e)}")

@router.delete("/cache/clear", response_model=ClearCacheResponse)
async def clear_cache(background_tasks: BackgroundTasks):
    """
    Clear all cached LinkedIn profile data.
    
    This endpoint removes all cached profile files to free up storage space.
    The operation is performed in the background to avoid blocking the response.
    """
    try:
        def clear_cache_task():
            try:
                service = get_rapidapi_service()
                result = service.clear_cache()
                logging.info(f"Cache cleared: {result['cleared_count']} files")
            except Exception as e:
                logging.error(f"Error in background cache clearing: {str(e)}")
        
        # Run cache clearing in background
        background_tasks.add_task(clear_cache_task)
        
        return ClearCacheResponse(
            success=True,
            message="Cache clearing initiated in background",
            cleared_count=0  # Will be updated in background
        )
        
    except Exception as e:
        logging.error(f"Error in clear_cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to clear cache: {str(e)}")

@router.get("/health")
async def health_check():
    """
    Health check endpoint for RapidAPI LinkedIn service.
    """
    try:
        service = get_rapidapi_service()
        return {
            "status": "healthy",
            "service": "rapidapi-linkedin",
            "cache_directory": service.cache_dir,
            "api_host": service.api_host
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "service": "rapidapi-linkedin",
            "error": str(e)
        } 

@router.post("/batch-candidates", response_model=BatchCandidatesResponse)
async def batch_candidates(
    req: BatchCandidatesRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Enforce user ownership
    job = db.query(JobDescription).filter(JobDescription.id == req.job_id, JobDescription.user_id == current_user.id).first()
    if not job:
        raise HTTPException(status_code=403, detail="You do not have access to this job or it does not exist.")
    try:
        candidates = rapidapi_batch_service.fetch_and_store_candidates(db, req.job_id, current_user.id)
        
        # Return success response with user and job information
        return BatchCandidatesResponse(
            success=True,
            user_id=current_user.id,
            job_id=req.job_id,
            message=f"Successfully processed {len(candidates)} candidates for job ID {req.job_id}",
            candidates_processed=len(candidates)
        )
    except Exception as e:
        logging.error(f"Error in batch_candidates: {str(e)}")
        return BatchCandidatesResponse(
            success=False,
            user_id=current_user.id,
            job_id=req.job_id,
            error=str(e)
        )

@router.get("/processing-stats/{job_id}")
async def get_processing_statistics(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get processing statistics for a specific job.
    
    Returns information about:
    - Total URLs found by SerpAPI
    - URLs processed by RapidAPI
    - Success/failure rates
    - Cost analysis and savings
    """
    try:
        from uuid import UUID
        
        # Validate job_id format
        try:
            job_uuid = UUID(job_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid job_id format")
        
        # Enforce user ownership
        job = db.query(JobDescription).filter(
            JobDescription.id == job_uuid, 
            JobDescription.user_id == current_user.id
        ).first()
        if not job:
            raise HTTPException(status_code=403, detail="You do not have access to this job or it does not exist.")
        
        stats = rapidapi_batch_service.get_processing_statistics(db, job_uuid)
        
        return {
            "success": True,
            "job_id": job_id,
            "statistics": stats
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in get_processing_statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get processing statistics: {str(e)}")

@router.get("/url-details/{job_id}")
async def get_url_processing_details(
    job_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get detailed information about URL processing status for a specific job.
    
    Returns detailed information about:
    - Which URLs have been processed successfully
    - Which URLs failed processing
    - Which URLs are still pending
    - Error messages for failed extractions
    """
    try:
        from uuid import UUID
        
        # Validate job_id format
        try:
            job_uuid = UUID(job_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid job_id format")
        
        # Enforce user ownership
        job = db.query(JobDescription).filter(
            JobDescription.id == job_uuid, 
            JobDescription.user_id == current_user.id
        ).first()
        if not job:
            raise HTTPException(status_code=403, detail="You do not have access to this job or it does not exist.")
        
        details = rapidapi_batch_service.get_url_processing_details(db, job_uuid)
        
        return {
            "success": True,
            "job_id": job_id,
            "details": details
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Error in get_url_processing_details: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get URL processing details: {str(e)}") 