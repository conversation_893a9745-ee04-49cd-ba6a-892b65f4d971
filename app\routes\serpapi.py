from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import JSONResponse
from app.models.serpapi import (
    SearchQueryRequest,
    SearchResponse,
    BooleanSearchRequest,
    CacheClearResponse,
    LocationVerificationRequest,
    LocationVerificationResponse
)
from app.services.serpapi_service import SerpAPIService
from app.logger import logging
from typing import List
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.auth.security import get_current_user
from app.db.db_models import User

router = APIRouter(prefix="/api/v1/serpapi", tags=["SerpAPI"])

serpapi_service = SerpAPIService()

@router.post("/search", response_model=SearchResponse)
async def search_linkedin_profiles(
    request: SearchQueryRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Search for LinkedIn profiles using SerpAPI and store results in serpapi_results.
    Requires authentication.
    """
    try:
        logging.info(f"User {current_user.id} searching LinkedIn profiles with query: {request.query}")
        
        # Convert request to dict for the service
        search_params = {
            "query": request.query,
            "max_pages": request.max_pages,
            "location": request.location,
            "company": request.company,
            "title": request.title,
            "skills": request.skills
        }
        job_id = request.job_id
        
        # Perform the search
        result = await serpapi_service.search_profiles(search_params)
        linkedin_urls = result.get("linkedin_urls", [])
        enhanced_query = result.get("query", request.query)
        
        # Batch insert into serpapi_results
        if linkedin_urls:
            serpapi_service.batch_insert_results(db, job_id, linkedin_urls, enhanced_query)
        
        logging.info(f"Found {result['total_results']} LinkedIn profiles and inserted into serpapi_results")
        return JSONResponse(content=result)

    except Exception as e:
        logging.error(f"Error searching LinkedIn profiles: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/boolean-search", response_model=SearchResponse)
async def boolean_search_profiles(request: BooleanSearchRequest):
    """
    Perform a boolean search for LinkedIn profiles with enhanced query building.
    
    This endpoint constructs a proper Google X-ray search query based on job requirements:
    - Job title (quoted for exact match)
    - Required skills (with OR operators)
    - Location preferences (quoted for exact match)
    - Experience level
    - Target company (quoted for exact match)
    - Industry/domain
    - Education requirements
    
    The query is formatted for optimal Google X-ray search results.
    """
    try:
        logging.info(f"Enhanced boolean search for job title: {request.job_title}")
        
        # Convert request to dict for the service
        search_params = {
            "job_title": request.job_title,
            "skills": request.skills,
            "location": request.location,
            "experience_level": request.experience_level,
            "company": request.company,
            "max_pages": request.max_pages
        }
        
        # Perform the enhanced boolean search
        result = await serpapi_service.boolean_search_profiles(search_params)
        
        logging.info(f"Enhanced boolean search found {result['total_results']} LinkedIn profiles")
        return JSONResponse(content=result)

    except Exception as e:
        logging.error(f"Error in boolean search: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/search-history")
async def get_search_history():
    """
    Get search history from cache.
    
    Returns a list of all cached search results with metadata.
    """
    try:
        logging.info("Retrieving search history")
        history = await serpapi_service.get_search_history()
        return {
            "total_cached_searches": len(history),
            "history": history
        }
    except Exception as e:
        logging.error(f"Error retrieving search history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cache", response_model=CacheClearResponse)
async def clear_search_cache():
    """
    Clear all cached search results.
    
    This will remove all cached search responses to free up storage.
    """
    try:
        logging.info("Clearing search cache")
        result = await serpapi_service.clear_cache()
        return JSONResponse(content=result)
    except Exception as e:
        logging.error(f"Error clearing cache: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """
    Health check endpoint for SerpAPI service.
    
    Returns the status of the SerpAPI service and cache directory.
    """
    try:
        import os
        cache_dir = serpapi_service.cache_dir
        cache_exists = os.path.exists(cache_dir)
        cache_files = len([f for f in os.listdir(cache_dir) if f.endswith('.json')]) if cache_exists else 0
        
        return {
            "status": "healthy",
            "cache_directory": cache_dir,
            "cache_exists": cache_exists,
            "cached_files": cache_files,
            "service": "SerpAPIService"
        }
    except Exception as e:
        logging.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/verify-location", response_model=LocationVerificationResponse)
async def verify_candidate_location(request: LocationVerificationRequest):
    """
    Verify candidate location using multiple search strategies.
    
    This endpoint uses SerpAPI to search for candidate information across:
    - LinkedIn profiles
    - General web search
    - Company-specific searches
    
    Returns confidence scores and evidence sources.
    """
    try:
        verification_result = await serpapi_service.verify_candidate_location(
            candidate_name=request.candidate_name,
            expected_location=request.expected_location,
            company=request.company
        )
        
        if "error" in verification_result:
            return LocationVerificationResponse(
                success=False,
                candidate_name=request.candidate_name,
                expected_location=request.expected_location,
                company=request.company,
                error=verification_result["error"],
                verification_result="error"
            )
        
        return LocationVerificationResponse(
            success=True,
            candidate_name=verification_result["candidate_name"],
            expected_location=verification_result["expected_location"],
            company=verification_result["company"],
            location_confidence=verification_result["location_confidence"],
            verification_result=verification_result["verification_result"],
            linkedin_matches=verification_result["linkedin_matches"],
            web_matches=verification_result["web_matches"],
            company_matches=verification_result["company_matches"],
            evidence_sources=verification_result["evidence_sources"]
        )
        
    except Exception as e:
        logging.error(f"Error in verify_candidate_location: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to verify location: {str(e)}") 