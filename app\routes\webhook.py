from fastapi import APIRouter, Depends, HTTPException, status, Header
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.auth.schemas import PaymentWebhook, PaymentWebhookResponse
from app.services.user_service import UserService
from fastapi.responses import JSONResponse
from typing import Optional
import os
from app.logger import logging

router = APIRouter(prefix="/webhook", tags=["Webhooks"])
user_service = UserService()

# Get webhook API key from environment
WEBHOOK_API_KEY = os.getenv("WEBHOOK_API_KEY", "G7kZ2ePq8xT9A3bL4dF6wR1hY0mS5vU")

def verify_webhook_key(x_api_key: Optional[str] = Header(None)):
    """Verify webhook API key"""
    if not x_api_key:
        logging.warning("Webhook called without API key")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing X-API-Key header"
        )
    
    if x_api_key != WEBHOOK_API_KEY:
        logging.warning(f"Webhook called with invalid API key: {x_api_key}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API key"
        )
    
    return True

@router.post("/payment", response_model=PaymentWebhookResponse)
def payment_webhook(
    webhook_data: PaymentWebhook,
    db: Session = Depends(get_db),
    _: bool = Depends(verify_webhook_key)
):
    """
    Payment webhook endpoint for external payment systems
    
    This endpoint is called by external payment systems after successful payment.
    It updates user status and plan based on the payment information.
    
    Required headers:
    - X-API-Key: Secret key for webhook authentication
    
    Required fields:
    - status: User status (active, inactive, upgrading)
    - plan: User plan (trial, starter, pro)
    - user_id: User ID in our system
    
    Optional fields:
    - user_email: User email for verification
    - customer_id: Customer ID from payment system
    - bill_id: Bill/Invoice ID from payment system
    """
    try:
        logging.info(f"Payment webhook received for user: {webhook_data.user_id}")
        logging.info(f"New status: {webhook_data.status}, New plan: {webhook_data.plan}")
        
        # Process the webhook
        result = user_service.process_payment_webhook(db, webhook_data)
        
        if result["success"]:
            logging.info(f"Payment webhook processed successfully: {result}")
            return JSONResponse(
                status_code=status.HTTP_200_OK,
                content=result
            )
        else:
            logging.error(f"Payment webhook processing failed: {result}")
            return JSONResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                content=result
            )
            
    except Exception as e:
        logging.error(f"Payment webhook error: {str(e)}")
        error_response = {
            "success": False,
            "message": f"Webhook processing failed: {str(e)}",
            "user_id": webhook_data.user_id,
            "updated_fields": {}
        }
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response
        )

@router.get("/health")
def webhook_health_check():
    """Health check endpoint for webhook service"""
    return {
        "status": "healthy",
        "service": "webhook",
        "endpoints": [
            "POST /webhook/payment - Payment webhook"
        ]
    }
