import os
import httpx
from app.logger import logging

dashboard_host = os.getenv("DASHBOARD_HOST", "http://localhost:3000")
dashboard_key = os.getenv("DASHBOARD_KEY", "default_api_key")

async def dashboard_post_jd(job_id, user_id, data):
    logging.info(f"Post JD to Dashboard with data: {job_id, user_id, data}")
    headers = {"Authorization": f"Bearer {dashboard_key}"}
    async with httpx.AsyncClient() as client:
        response = await client.post(f'{dashboard_host}/jd', json=data, headers=headers)
        return response.json()
    
async def dashboard_post_candidates(job_id, user_id, data):
    logging.info(f"Post JD to Dashboard with data: {job_id, user_id, data}")
    headers = {"Authorization": f"Bearer {dashboard_key}"}
    async with httpx.AsyncClient() as client:
        response = await client.post(f'{dashboard_host}/jd', json=data, headers=headers)
        return response.json()