from app.models.schemas import JobDescription as JobDescriptionSchema, JobDescriptionResponse
from typing import Optional, Dict, Union
import json
import os
from dotenv import load_dotenv
from openai import OpenAI, AsyncOpenAI
from tenacity import retry, stop_after_attempt, wait_exponential
import uuid
from datetime import datetime
from app.logger import logging
import unicodedata
from app.models.job_description import JobDescriptionData, Requirements, SkillPreference
from sqlalchemy.orm import Session
from app.db.db_models import JobDescription as JobDescriptionModel
import re
from pprint import pformat

load_dotenv()

class JobDescriptionService:
    def __init__(self):
        # Initialize OpenAI client
        # api_key = os.getenv("OPENAI_API_KEY")
        api_key="********************************************************************************************************************************************************************"
        if not api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        self.client = AsyncOpenAI(api_key=api_key)
        
    async def _generate_suggested_titles(self, role: str) -> list[str]:
        """Generate suggested job titles for a given role using OpenAI."""
        if not role or role == "N/A":
            return []
        
        try:
            prompt = f"""Given the job title "{role}", suggest 5 alternative or related job titles that a candidate for this role might also be qualified for or interested in.
            
            Return a JSON list of strings. For example:
            ["Senior Software Engineer", "Backend Developer", "Full Stack Engineer"]
            
            Return ONLY the JSON list, no markdown formatting or additional text."""

            response = await self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are an expert career advisor and recruitment specialist."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.4,
                max_tokens=150
            )
            
            content = response.choices[0].message.content.strip()
            
            # Clean and parse the response
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            
            titles = json.loads(content)
            
            if isinstance(titles, list) and all(isinstance(t, str) for t in titles):
                return titles
            
            logging.warning(f"Generated titles for '{role}' was not a list of strings: {titles}")
            return []
            
        except Exception as e:
            logging.warning(f"Could not generate suggested titles for role '{role}': {e}")
            return []
        
    def parse_linkedin_job_data(self, data: dict) -> dict:
        """
        Parse LinkedIn job data dict into normalized job description format.
        """
        result = {
            "Benefits": [],
            "Company": data.get("company", {}).get("name", "N/A"),
            "Location": data.get("location", "N/A"),
            "Requirements": {
                "Experience": data.get("formattedExperienceLevel", "N/A"),
                "Minimum Qualification": "N/A",
                "Preferred Qualification": "N/A",
                "Skills": []
            },
            "Responsibilities": [],
            "Role": data.get("title", "N/A"),
            "SkillsPreference": [],
            "Suggested_Job_Titles": [],
        }

        # Parse description for responsibilities, skills, qualifications
        description = data.get("description", "")
        if description:
            # Extract Key Qualifications
            key_qual_match = re.search(r"Key Qualifications(.*?)(Compensation|Salary|If interested|Key Words|Disclaimer|$)", description, re.DOTALL | re.IGNORECASE)
            if key_qual_match:
                qualifications = key_qual_match.group(1)
                skills = [line.strip('-•* \n') for line in qualifications.split('\n') if line.strip()]
                result["Requirements"]["Skills"] = skills
                result["SkillsPreference"] = [{"name": s, "status": "R"} for s in skills]

            # Extract Key Words for Search
            key_words_match = re.search(r"Key Words for Search:(.*?)(Disclaimer|$)", description, re.DOTALL | re.IGNORECASE)
            if key_words_match:
                keywords = key_words_match.group(1)
                keyword_list = [k.strip() for k in keywords.split(',') if k.strip()]
                result["Suggested_Job_Titles"] = keyword_list

            # Extract Responsibilities (first section or lines before Key Qualifications)
            resp_match = re.search(r"Position Focus(.*?)(Key Qualifications|Compensation|Salary|If interested|Key Words|Disclaimer|$)", description, re.DOTALL | re.IGNORECASE)
            if resp_match:
                responsibilities = resp_match.group(1)
                resp_lines = [line.strip('-•* \n') for line in responsibilities.split('\n') if line.strip()]
                result["Responsibilities"] = resp_lines

            # Extract Benefits (Compensation & Benefits section)
            benefits_match = re.search(r"Compensation & Benefits(.*?)(If interested|Key Words|Disclaimer|$)", description, re.DOTALL | re.IGNORECASE)
            if benefits_match:
                benefits = benefits_match.group(1)
                benefit_lines = [line.strip('-•* \n') for line in benefits.split('\n') if line.strip()]
                # Try to parse as key-value pairs
                benefit_dict = {}
                for line in benefit_lines:
                    if ':' in line:
                        k, v = line.split(':', 1)
                        benefit_dict[k.strip()] = v.strip()
                if benefit_dict:
                    result["Benefits"].append(benefit_dict)
                else:
                    result["Benefits"] = benefit_lines

        # Fallback: If SkillsPreference is empty, add default
        if not result["SkillsPreference"]:
            result["SkillsPreference"] = [{"name": "", "status": "R"}]

        return result

    # No retry decorator: only one attempt per request
    async def parse_and_save_job_description(
        self,
        db: Session,
        job_description_text: str,
        user_id: uuid.UUID,
        parse_method: str,
        scraped: dict = None  # <-- new optional argument
    ) -> Dict:
        """Parse a job description, save it to the DB, and return structured data."""
        try:
            # Hybrid logic: use direct parsing if scraped data is present and has 'data', else use LLM
            logging.info('-----', pformat(data))
            if scraped and isinstance(scraped, dict) and "data" in scraped:
                normalized_result = self.parse_linkedin_job_data(scraped["data"])
            else:
                # Clean the text
                cleaned_text = job_description_text.encode("ascii", "ignore").decode("ascii")

                # Create the prompt with the exact structure we want
                prompt = f'''Parse the following job description and extract information in the exact JSON format specified below.
If any information is not found, use "N/A" for string fields and empty lists [] for array fields.
For SkillsPreference, mark all skills as "R" (Required).

Extract the required skills as a list of keywords, where each keyword is ideally a single word (e.g., "Python", "Kubernetes"), but never more than three words (e.g., "project management", "cloud infrastructure"). Do not include long phrases or sentences.

Job Description:
{cleaned_text}

Return a JSON object with EXACTLY this structure:
{{
    "Benefits": [],
    "Company": "Company name",
    "Location": "Job location",
    "Requirements": {{
        "Experience": "Required years of experience",
        "Minimum Qualification": "Minimum education/qualification required",
        "Preferred Qualification": "Preferred education/qualification",
        "Skills": ["List", "of", "required", "skills"]
    }},
    "Responsibilities": ["List", "of", "responsibilities"],
    "Role": "Job title",
    "SkillsPreference": [
        {{"name": "Skill name", "status": "R"}}
    ],
    "Suggested_Job_Titles": ["List", "of", "related", "job", "titles"]
}}

Ensure all fields are present and follow the exact structure. Use "N/A" for missing string values and empty lists [] for missing array values.
Return ONLY the JSON object, no markdown formatting or additional text.'''

                # Call OpenAI API
                response = await self.client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[
                        {"role": "system", "content": "You are a job description parser that returns data in a specific JSON format."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1
                )

                # Parse the response
                try:
                    content = response.choices[0].message.content.strip()
                    # Remove any markdown formatting if present
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    content = content.strip()
                    parsed_data = json.loads(content)
                    # Generate suggested titles based on the parsed role
                    role = parsed_data.get("Role", "N/A")
                    suggested_titles = await self._generate_suggested_titles(role)
                    # Normalize the result to the user-specified structure
                    normalized_result = {
                        "Benefits": parsed_data.get("Benefits", []),
                        "Company": parsed_data.get("Company", "N/A"),
                        "Location": parsed_data.get("Location", "N/A"),
                        "Requirements": {
                            "Experience": parsed_data.get("Requirements", {}).get("Experience", "N/A"),
                            "Minimum Qualification": parsed_data.get("Requirements", {}).get("Minimum Qualification", "N/A"),
                            "Preferred Qualification": parsed_data.get("Requirements", {}).get("Preferred Qualification", "N/A"),
                            "Skills": parsed_data.get("Requirements", {}).get("Skills", [])
                        },
                        "Responsibilities": parsed_data.get("Responsibilities", []),
                        "Role": role,
                        "SkillsPreference": [],
                        "Suggested_Job_Titles": suggested_titles
                    }
                    # Populate SkillsPreference from Requirements.Skills
                    skills = normalized_result["Requirements"]["Skills"]
                    normalized_result["SkillsPreference"] = [
                        {"name": skill, "status": "R"} for skill in skills if isinstance(skill, str)
                    ]
                    # Failsafe: If SkillsPreference is empty, add default
                    if not normalized_result["SkillsPreference"]:
                        normalized_result["SkillsPreference"] = [{"name": "", "status": "R"}]
                except json.JSONDecodeError as e:
                    logging.error(f"Error parsing JSON response: {str(e)}")
                    logging.error(f"Raw response: {content}")
                    raise Exception("Failed to parse job description")

            # Check if all key fields are empty, N/A, or None (applies to both URL and LLM/plain text)
            def is_empty(val):
                if val is None:
                    return True
                if isinstance(val, str) and (val.strip().lower() in ("", "n/a", "none")):
                    return True
                if isinstance(val, list) and (not val or all(is_empty(v) for v in val)):
                    return True
                if isinstance(val, dict) and all(is_empty(v) for v in val.values()):
                    return True
                return False
            skills_field = normalized_result.get("Requirements", {}).get("Skills")
            other_fields = [
                normalized_result.get("Company"),
                normalized_result.get("Location"),
                normalized_result.get("Role"),
                normalized_result.get("Benefits"),
                normalized_result.get("Responsibilities"),
                normalized_result.get("Suggested_Job_Titles"),
            ]
            if all(is_empty(f) for f in other_fields):
                # If all other fields are empty, do not create even if skills is not empty
                raise Exception("Parsing was not successful. Please provide a more detailed or valid job description.")

            # Save to database
            db_jd = JobDescriptionModel(
                user_id=user_id,
                job_title=normalized_result.get("Role", "N/A"),
                location=normalized_result.get("Location", "N/A"),
                jd_text=normalized_result,
                parse_method=parse_method
            )
            db.add(db_jd)
            db.commit()
            db.refresh(db_jd)
            # Return the result in the desired format
            return json.loads(json.dumps(normalized_result, indent=2))
        except Exception as e:
            logging.error(f"Error processing job description: {str(e)}")
            raise Exception(f"Error processing job description: {str(e)}")
    
    async def get_job_description(self, db: Session, job_id: str, user_id: uuid.UUID) -> Optional[Dict]:
        """Get a specific job description by ID, scoped to the user."""
        jd = db.query(JobDescriptionModel).filter(
            JobDescriptionModel.id == job_id,
            JobDescriptionModel.user_id == user_id
        ).first()
        return jd.jd_text if jd else None

    async def list_job_descriptions(self, db: Session, user_id: uuid.UUID) -> list[Dict]:
        """List all job descriptions for a specific user."""
        jds = db.query(JobDescriptionModel).filter(JobDescriptionModel.user_id == user_id).all()
        return [jd.jd_text for jd in jds]

    def _clean_text(self, text: str) -> str:
        """Clean and normalize text by converting to ASCII."""
        if not text:
            return ""
        # Normalize Unicode characters
        text = unicodedata.normalize("NFKD", text)
        # Remove any remaining non-ASCII characters
        text = text.encode("ascii", "ignore").decode("ascii")
        return text.strip()

    async def _cache_job_description(self, job_id: str, job_description: JobDescriptionResponse) -> None:
        """Cache a job description."""
        # Implementation here
        pass

    async def get_cached_job_description(self, job_id: str) -> Optional[Dict]:
        """Get a cached job description by ID."""
        try:
            # Try to initialize Redis if available
            try:
                import redis
                redis_client = redis.Redis(
                    host=os.getenv("REDIS_HOST", "localhost"),
                    port=int(os.getenv("REDIS_PORT", 6379)),
                    db=0
                )
                jd_json = redis_client.get(f"jd:{job_id}")
            except ImportError:
                print("Redis not available, using in-memory storage")
                jd_json = None
                
            if jd_json:
                return json.loads(jd_json)
            return None
        except Exception as e:
            logging.error(f"Error retrieving job description: {str(e)}")
            return None

    async def list_cached_job_descriptions(self) -> list[JobDescriptionResponse]:
        """List all cached job descriptions."""
        # Implementation here
        return [] 