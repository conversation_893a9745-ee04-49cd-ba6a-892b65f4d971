import os
import json
import time
import random
import requests
from typing import List, Dict, Optional
from app.logger import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

class LinkedInProfileService:
    def __init__(self):
        self.cache_dir = os.path.join(os.getcwd(), "linkedin_profile_cache")
        os.makedirs(self.cache_dir, exist_ok=True)
        
        # User agents for rotation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        ]

    def _get_cache_key(self, url: str) -> str:
        """Generate a cache key for the LinkedIn URL."""
        import hashlib
        return hashlib.md5(url.encode()).hexdigest()

    def _get_cached_profile(self, url: str) -> Optional[Dict]:
        """Get cached profile data if available."""
        try:
            cache_key = self._get_cache_key(url)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"Error reading cached profile: {str(e)}")
        return None

    def _cache_profile(self, url: str, profile_data: Dict) -> None:
        """Cache profile data."""
        try:
            cache_key = self._get_cache_key(url)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            with open(cache_file, 'w') as f:
                json.dump(profile_data, f, indent=2)
            logging.info(f"Cached profile data for: {url}")
        except Exception as e:
            logging.error(f"Error caching profile: {str(e)}")

    def _setup_driver(self) -> webdriver.Chrome:
        """Setup Chrome WebDriver with appropriate options."""
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument(f"--user-agent={random.choice(self.user_agents)}")
        
        # Add additional options to avoid detection
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        driver = webdriver.Chrome(service=webdriver.chrome.service.Service(ChromeDriverManager().install()), options=chrome_options)
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver

    def _extract_profile_data(self, soup: BeautifulSoup, url: str) -> Dict:
        """Extract profile data from BeautifulSoup object."""
        profile_data = {
            "url": url,
            "name": "",
            "headline": "",
            "location": "",
            "company": "",
            "education": "",
            "skills": [],
            "experience": [],
            "extraction_time": time.time()
        }
        
        try:
            # Debug: Log the page title to see if we're on the right page
            page_title = soup.find('title')
            if page_title:
                logging.info(f"Page title: {page_title.get_text().strip()}")
            
            # Multiple selectors for name (LinkedIn changes these frequently)
            name_selectors = [
                'h1.text-heading-xlarge',
                'h1.pv-text-details__left-panel',
                'h1.break-words',
                'h1.text-heading-xlarge.break-words',
                'h1[data-section="headline"]',
                'h1',
                '.pv-text-details__left-panel h1',
                '.ph5.pb5 h1'
            ]
            
            name_elem = None
            for selector in name_selectors:
                name_elem = soup.select_one(selector)
                if name_elem and name_elem.get_text().strip():
                    break
            
            if name_elem:
                profile_data["name"] = name_elem.get_text().strip()
                logging.info(f"Found name: {profile_data['name']}")
            
            # Multiple selectors for headline
            headline_selectors = [
                '.text-body-medium.break-words',
                '.pv-text-details__left-panel .text-body-medium',
                '.ph5.pb5 .text-body-medium',
                '.pv-text-details__left-panel div[data-section="headline"]',
                '.text-body-medium',
                '.pv-text-details__left-panel div',
                '.ph5.pb5 div.text-body-medium'
            ]
            
            headline_elem = None
            for selector in headline_selectors:
                headline_elem = soup.select_one(selector)
                if headline_elem and headline_elem.get_text().strip():
                    break
            
            if headline_elem:
                profile_data["headline"] = headline_elem.get_text().strip()
                logging.info(f"Found headline: {profile_data['headline']}")
            
            # Multiple selectors for location
            location_selectors = [
                '.text-body-small.inline.t-black--light.break-words',
                '.pv-text-details__left-panel .text-body-small',
                '.ph5.pb5 .text-body-small',
                '.text-body-small',
                '.pv-text-details__left-panel span',
                '.ph5.pb5 span.text-body-small',
                '[data-section="location"]',
                '.pv-text-details__left-panel .inline'
            ]
            
            location_elem = None
            for selector in location_selectors:
                location_elem = soup.select_one(selector)
                if location_elem and location_elem.get_text().strip():
                    break
            
            if location_elem:
                profile_data["location"] = location_elem.get_text().strip()
                logging.info(f"Found location: {profile_data['location']}")
            
            # Extract current company from headline or experience section
            if profile_data["headline"]:
                # Try to extract company from headline
                headline_text = profile_data["headline"].lower()
                if " at " in headline_text:
                    company_part = headline_text.split(" at ")[-1]
                    profile_data["company"] = company_part.strip()
                elif " chez " in headline_text:  # French
                    company_part = headline_text.split(" chez ")[-1]
                    profile_data["company"] = company_part.strip()
                elif " bei " in headline_text:  # German
                    company_part = headline_text.split(" bei ")[-1]
                    profile_data["company"] = company_part.strip()
            
            # Extract skills - multiple approaches
            skills = []
            
            # Method 1: Skills section
            skills_sections = soup.find_all('section', {'class': 'artdeco-card'})
            for section in skills_sections:
                skill_elements = section.find_all('span', {'class': 'pv-skill-category-entity__name-text'})
                if not skill_elements:
                    skill_elements = section.find_all('span', {'class': 'pv-skill-category-entity__name'})
                if not skill_elements:
                    skill_elements = section.find_all('span', {'class': 'skill-category-entity__name'})
                
                for skill_elem in skill_elements:
                    skill_text = skill_elem.get_text().strip()
                    if skill_text and skill_text not in skills:
                        skills.append(skill_text)
            
            # Method 2: Look for skills in the page
            if not skills:
                skill_patterns = [
                    'span[class*="skill"]',
                    'span[class*="endorsement"]',
                    '.pv-skill-category-entity__name',
                    '.skill-category-entity__name'
                ]
                
                for pattern in skill_patterns:
                    skill_elements = soup.select(pattern)
                    for elem in skill_elements:
                        skill_text = elem.get_text().strip()
                        if skill_text and skill_text not in skills and len(skill_text) < 50:
                            skills.append(skill_text)
            
            profile_data["skills"] = skills[:10]  # Limit to 10 skills
            logging.info(f"Found {len(skills)} skills")
            
            # Extract experience - multiple approaches
            experience = []
            
            # Method 1: Experience section by ID
            experience_section = soup.find('section', {'id': 'experience'})
            if not experience_section:
                experience_section = soup.find('section', {'id': 'experience-section'})
            
            if experience_section:
                exp_elements = experience_section.find_all('li', {'class': 'artdeco-list__item'})
                if not exp_elements:
                    exp_elements = experience_section.find_all('li', {'class': 'pv-entity__position-group-pager'})
                
                for exp_elem in exp_elements[:3]:  # Get first 3 experiences
                    exp_data = {}
                    
                    # Company name
                    company_elem = exp_elem.find('span', {'class': 'pv-entity__company-summary-info'})
                    if not company_elem:
                        company_elem = exp_elem.find('span', {'class': 'pv-entity__company-name'})
                    if not company_elem:
                        company_elem = exp_elem.find('h3', {'class': 'pv-entity__company-name'})
                    
                    if company_elem:
                        exp_data["company"] = company_elem.get_text().strip()
                    
                    # Position
                    position_elem = exp_elem.find('h3', {'class': 'pv-entity__name'})
                    if not position_elem:
                        position_elem = exp_elem.find('h3', {'class': 'pv-entity__position-title'})
                    
                    if position_elem:
                        exp_data["position"] = position_elem.get_text().strip()
                    
                    # Duration
                    duration_elem = exp_elem.find('span', {'class': 'pv-entity__date-range'})
                    if not duration_elem:
                        duration_elem = exp_elem.find('span', {'class': 'pv-entity__date-range-v2'})
                    
                    if duration_elem:
                        exp_data["duration"] = duration_elem.get_text().strip()
                    
                    if exp_data:
                        experience.append(exp_data)
            
            # Method 2: Look for experience in the page
            if not experience:
                exp_patterns = [
                    '.pv-entity__position-group',
                    '.pv-entity__position',
                    '.experience-section li'
                ]
                
                for pattern in exp_patterns:
                    exp_elements = soup.select(pattern)
                    for exp_elem in exp_elements[:3]:
                        exp_data = {}
                        
                        # Try to extract company and position
                        text_content = exp_elem.get_text().strip()
                        if text_content and len(text_content) > 10:
                            exp_data["company"] = text_content[:50]  # First 50 chars as fallback
                        
                        if exp_data:
                            experience.append(exp_data)
            
            profile_data["experience"] = experience
            logging.info(f"Found {len(experience)} experience entries")
            
            # Extract education
            education_section = soup.find('section', {'id': 'education'})
            if not education_section:
                education_section = soup.find('section', {'id': 'education-section'})
            
            if education_section:
                edu_elem = education_section.find('h3', {'class': 'pv-entity__school-name'})
                if not edu_elem:
                    edu_elem = education_section.find('h3', {'class': 'pv-entity__name'})
                
                if edu_elem:
                    profile_data["education"] = edu_elem.get_text().strip()
                    logging.info(f"Found education: {profile_data['education']}")
            
            # Fallback: Try to extract any meaningful text if we haven't found anything
            if not any([profile_data["name"], profile_data["headline"], profile_data["location"]]):
                logging.warning("No profile data found with standard selectors, trying fallback methods")
                
                # Look for any meaningful text in the main content area
                main_content = soup.find('main') or soup.find('body')
                if main_content:
                    # Try to find name in any h1 or prominent text
                    h1_elements = main_content.find_all('h1')
                    for h1 in h1_elements:
                        text = h1.get_text().strip()
                        if text and len(text) < 100 and not profile_data["name"]:
                            profile_data["name"] = text
                            logging.info(f"Fallback name found: {text}")
                            break
                    
                    # Try to find location in any text containing common location indicators
                    location_indicators = ['california', 'new york', 'london', 'paris', 'berlin', 'tokyo', 'singapore']
                    all_text = main_content.get_text().lower()
                    for indicator in location_indicators:
                        if indicator in all_text:
                            # Find the actual location text
                            for elem in main_content.find_all(['span', 'div', 'p']):
                                text = elem.get_text().lower()
                                if indicator in text and len(text) < 100:
                                    profile_data["location"] = elem.get_text().strip()
                                    logging.info(f"Fallback location found: {profile_data['location']}")
                                    break
                            if profile_data["location"]:
                                break
            
        except Exception as e:
            logging.error(f"Error extracting profile data: {str(e)}")
        
        # Log final results
        logging.info(f"Extraction results: Name='{profile_data['name']}', Location='{profile_data['location']}', Company='{profile_data['company']}'")
        
        return profile_data

    async def get_profile_details_alternative(self, linkedin_url: str) -> Dict:
        """
        Alternative method to get profile details using a more robust approach.
        This method tries multiple strategies to extract profile data.
        """
        try:
            logging.info(f"Using alternative method for: {linkedin_url}")
            
            # Check cache first
            cached_data = self._get_cached_profile(linkedin_url)
            if cached_data and any([cached_data.get("name"), cached_data.get("location")]):
                logging.info(f"Returning cached profile data for: {linkedin_url}")
                return cached_data
            
            # Try different approaches
            approaches = [
                self._try_selenium_approach,
                self._try_requests_approach,
                self._try_public_data_approach
            ]
            
            for approach in approaches:
                try:
                    profile_data = await approach(linkedin_url)
                    if profile_data and any([profile_data.get("name"), profile_data.get("location")]):
                        logging.info(f"Success with approach: {approach.__name__}")
                        self._cache_profile(linkedin_url, profile_data)
                        return profile_data
                except Exception as e:
                    logging.warning(f"Approach {approach.__name__} failed: {str(e)}")
                    continue
            
            # If all approaches fail, return empty data with error
            return {
                "url": linkedin_url,
                "error": "All extraction methods failed. LinkedIn may require authentication or the profile may be private.",
                "extraction_time": time.time()
            }
            
        except Exception as e:
            logging.error(f"Error in alternative profile extraction: {str(e)}")
            return {
                "url": linkedin_url,
                "error": str(e),
                "extraction_time": time.time()
            }

    async def _try_selenium_approach(self, linkedin_url: str) -> Dict:
        """Try Selenium with enhanced anti-detection."""
        driver = None
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Add more realistic user agent
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
            
            driver = webdriver.Chrome(service=webdriver.chrome.service.Service(ChromeDriverManager().install()), options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # Navigate to profile
            driver.get(linkedin_url)
            time.sleep(5)  # Longer wait
            
            # Check if we're redirected to login
            current_url = driver.current_url
            if "signin" in current_url or "login" in current_url:
                raise Exception("Redirected to login page")
            
            # Wait for content to load
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # Try to scroll to load dynamic content
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            time.sleep(2)
            
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            return self._extract_profile_data(soup, linkedin_url)
            
        finally:
            if driver:
                driver.quit()

    async def _try_requests_approach(self, linkedin_url: str) -> Dict:
        """Try using requests with headers that mimic a real browser."""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            
            response = requests.get(linkedin_url, headers=headers, timeout=10)
            
            if response.status_code != 200:
                raise Exception(f"HTTP {response.status_code}")
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Check if we got a login page
            if "signin" in response.url or "login" in response.url:
                raise Exception("Redirected to login page")
            
            return self._extract_profile_data(soup, linkedin_url)
            
        except Exception as e:
            raise Exception(f"Requests approach failed: {str(e)}")

    async def _try_public_data_approach(self, linkedin_url: str) -> Dict:
        """Try to extract public data using alternative methods."""
        try:
            # This approach tries to extract any publicly available information
            # from the URL structure or basic page elements
            
            profile_data = {
                "url": linkedin_url,
                "name": "",
                "headline": "",
                "location": "",
                "company": "",
                "education": "",
                "skills": [],
                "experience": [],
                "extraction_time": time.time()
            }
            
            # Try to extract username from URL
            if "/in/" in linkedin_url:
                username = linkedin_url.split("/in/")[-1].split("/")[0]
                if username and username != "alberta1":  # Skip obviously fake usernames
                    profile_data["name"] = username.replace("-", " ").replace("_", " ").title()
            
            # Try to get basic page info
            try:
                headers = {
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                }
                response = requests.get(linkedin_url, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Try to get page title
                    title = soup.find('title')
                    if title:
                        title_text = title.get_text().strip()
                        if title_text and "LinkedIn" not in title_text and len(title_text) < 100:
                            profile_data["name"] = title_text
                    
                    # Look for any location-like text
                    location_patterns = [
                        r'\b[A-Z][a-z]+,\s*[A-Z]{2}\b',  # City, State
                        r'\b[A-Z][a-z]+,\s*[A-Z][a-z]+\b',  # City, Country
                        r'\b[A-Z][a-z]+\s*[A-Z]{2}\b',  # City State
                    ]
                    
                    import re
                    page_text = soup.get_text()
                    for pattern in location_patterns:
                        matches = re.findall(pattern, page_text)
                        if matches:
                            profile_data["location"] = matches[0]
                            break
                            
            except Exception as e:
                logging.warning(f"Public data extraction failed: {str(e)}")
            
            return profile_data
            
        except Exception as e:
            raise Exception(f"Public data approach failed: {str(e)}")

    # Update the main method to use the alternative approach
    async def get_profile_details(self, linkedin_url: str) -> Dict:
        """
        Get detailed profile information from a LinkedIn URL.
        Now uses alternative approaches to handle LinkedIn's restrictions.
        """
        return await self.get_profile_details_alternative(linkedin_url)

    async def get_multiple_profiles(self, linkedin_urls: List[str], max_concurrent: int = 3) -> List[Dict]:
        """
        Get profile details for multiple LinkedIn URLs with rate limiting.
        
        Parameters:
            linkedin_urls (List[str]): List of LinkedIn profile URLs
            max_concurrent (int): Maximum concurrent requests
            
        Returns:
            List[Dict]: List of profile information
        """
        results = []
        
        for i, url in enumerate(linkedin_urls):
            try:
                profile_data = await self.get_profile_details(url)
                results.append(profile_data)
                
                # Rate limiting
                if i < len(linkedin_urls) - 1:  # Don't sleep after the last request
                    time.sleep(random.uniform(1, 3))
                    
            except Exception as e:
                logging.error(f"Error processing URL {url}: {str(e)}")
                results.append({
                    "url": url,
                    "error": str(e),
                    "extraction_time": time.time()
                })
        
        return results

    async def verify_location(self, linkedin_url: str, expected_location: str) -> Dict:
        """
        Verify if a candidate's location matches the expected location.
        
        Parameters:
            linkedin_url (str): LinkedIn profile URL
            expected_location (str): Expected location to verify
            
        Returns:
            Dict: Verification result with location match details
        """
        try:
            profile_data = await self.get_profile_details(linkedin_url)
            
            if "error" in profile_data:
                return {
                    "url": linkedin_url,
                    "verification_success": False,
                    "error": profile_data["error"],
                    "expected_location": expected_location,
                    "actual_location": "Unknown"
                }
            
            actual_location = profile_data.get("location", "").lower()
            expected_location_lower = expected_location.lower()
            
            # Simple location matching (can be enhanced)
            location_match = expected_location_lower in actual_location or actual_location in expected_location_lower
            
            return {
                "url": linkedin_url,
                "verification_success": True,
                "location_match": location_match,
                "expected_location": expected_location,
                "actual_location": profile_data.get("location", "Not found"),
                "profile_data": profile_data
            }
            
        except Exception as e:
            logging.error(f"Error verifying location for {linkedin_url}: {str(e)}")
            return {
                "url": linkedin_url,
                "verification_success": False,
                "error": str(e),
                "expected_location": expected_location,
                "actual_location": "Unknown"
            }

    async def get_cache_stats(self) -> Dict:
        """Get statistics about cached profiles."""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
            total_size = sum(os.path.getsize(os.path.join(self.cache_dir, f)) for f in cache_files)
            
            return {
                "total_cached_profiles": len(cache_files),
                "cache_directory": self.cache_dir,
                "total_cache_size_bytes": total_size,
                "total_cache_size_mb": round(total_size / (1024 * 1024), 2)
            }
        except Exception as e:
            logging.error(f"Error getting cache stats: {str(e)}")
            return {"error": str(e)}

    async def clear_profile_cache(self) -> Dict:
        """Clear all cached profile data."""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
            for filename in cache_files:
                os.remove(os.path.join(self.cache_dir, filename))
            
            logging.info(f"Cleared {len(cache_files)} cached profile files")
            return {
                "message": f"Cleared {len(cache_files)} cached profile files",
                "cleared_count": len(cache_files)
            }
        except Exception as e:
            logging.error(f"Error clearing profile cache: {str(e)}")
            raise Exception(f"Failed to clear profile cache: {str(e)}") 