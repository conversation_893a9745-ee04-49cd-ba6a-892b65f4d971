from typing import List, Dict, Optional
import os
import json
import csv
from urllib.parse import quote_plus
import urllib.request
import ssl
import time
import random
from datetime import datetime
from pydantic import BaseModel, validator
from app.models.linkedin import LinkedInJob, LinkedInJobResponse
from app.services.scraping_service import ScrapingService

# Configure logging
from app.logger import logging

class SearchQueryModel(BaseModel):
    query: str

    @validator('query')
    def validate_query(cls, v):
        if not isinstance(v, str) or len(v) < 3 or len(v) > 100:
            raise ValueError('Query must be a string between 3 and 100 characters.')
        return v

class LinkedInService:
    def __init__(self):
        self.proxy = os.getenv('PROXY', 'http://brd-customer-hl_17b98357-zone-dash_dev_serp_api:<EMAIL>:33335')
        self.sleep_range = (1, 2)  # seconds between requests
        self.retry_limit = 3
        
        # Setup opener with proxy
        self.opener = urllib.request.build_opener(
            urllib.request.ProxyHandler({'http': self.proxy, 'https': self.proxy}),
            urllib.request.HTTPSHandler(context=ssl._create_unverified_context())
        )
        self.opener.addheaders = [('User-Agent', 'Mozilla/5.0')]
        
        # Create cache directory
        self.csv_cache_dir = os.path.join(os.getcwd(), "linkedin_csv_cache")
        os.makedirs(self.csv_cache_dir, exist_ok=True)
        
        self.scraping_service = ScrapingService()
        logging.info("LinkedInService initialized")
        
    def _random_ua(self, k=1) -> str:
        """Generate a random user agent string."""
        ua_pct = {
            "ua": {
                "0": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36",
                "1": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:105.0) Gecko/20100101 Firefox/105.0",
                "2": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36",
                "3": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:106.0) Gecko/20100101 Firefox/106.0",
                "4": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36",
                "5": "Mozilla/5.0 (X11; Linux x86_64; rv:105.0) Gecko/20100101 Firefox/105.0",
                "6": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/105.0.0.0 Safari/537.36",
                "7": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36",
                "8": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/106.0.0.0 Safari/537.36",
                "9": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.0 Safari/605.1.15",
                "10": "Mozilla/5.0 (X11; Linux x86_64; rv:106.0) Gecko/20100101 Firefox/106.0",
                "11": "Mozilla/5.0 (Windows NT 10.0; rv:105.0) Gecko/20100101 Firefox/105.0",
                "12": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:105.0) Gecko/20100101 Firefox/105.0",
                "13": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:105.0) Gecko/20100101 Firefox/105.0",
                "14": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/107.0.0.0 Safari/537.36"
            },
            "pct": {
                "0": 28.8, "1": 13.28, "2": 10.98, "3": 8.55, "4": 6.25, "5": 5.56,
                "6": 4.53, "7": 4.27, "8": 3.57, "9": 2.93, "10": 2.99, "11": 2.55,
                "12": 2.44, "13": 1.7, "14": 1.59
            }
        }
        return random.choices(list(ua_pct['ua'].values()), list(ua_pct['pct'].values()), k=k)[0]
        
    def _clean_boolean_string(self, boolean_string: str) -> str:
        """Convert OpenAI-style boolean string into a Google X-ray search query."""
        # Remove title:, location:, and AND/OR keywords
        cleaned = re.sub(r'(title:|location:)', '', boolean_string, flags=re.IGNORECASE)
        cleaned = re.sub(r'\b(AND|OR)\b', '', cleaned, flags=re.IGNORECASE)
        cleaned = cleaned.replace('"', '').strip()

        # Add quotes around each word group
        tokens = [f'"{token.strip()}"' for token in cleaned.split() if token.strip()]
        query = ' '.join(tokens)

        # Append LinkedIn site filter
        query += ' site:linkedin.com/in OR site:linkedin.com/pub'
        return query.strip()
        
    async def search_profiles(self, query: str, max_pages: int = 1) -> List[str]:
        """
        Search for LinkedIn profiles using the provided query.
        
        Args:
            query (str): The search query
            max_pages (int): Number of pages to search
            
        Returns:
            List[str]: List of LinkedIn profile URLs
        """
        # Validate query
        search_query = SearchQueryModel(query=query)
        
        # Add LinkedIn site filter if not present
        linkedin_suffix = "site:linkedin.com/in OR site:linkedin.com/pub"
        if linkedin_suffix.lower() not in query.lower():
            query = f"{query.strip()} {linkedin_suffix}"
            
        linkedin_urls = set()
        encoded_query = quote_plus(query)
        
        for page in range(max_pages):
            start = page * 10
            url = f'https://www.google.com/search?q={encoded_query}&start={start}&brd_json=1'
            
            for attempt in range(self.retry_limit):
                try:
                    response = self.opener.open(url, timeout=10).read().decode()
                    data = json.loads(response)
                    
                    results = data.get('organic', [])
                    for item in results:
                        link = item.get('link', '')
                        if 'linkedin.com/in' in link or 'linkedin.com/pub' in link:
                            linkedin_urls.add(link)
                            
                    logging.info(f"[Page {page+1}] Found {len(results)} results, {len(linkedin_urls)} total unique URLs")
                    break  # success
                except Exception as e:
                    logging.error(f"[Page {page+1}] Attempt {attempt+1} failed: {e}")
                    if attempt < self.retry_limit - 1:
                        time.sleep(2 ** attempt)  # exponential backoff
                    else:
                        logging.error(f"[Page {page+1}] Giving up after {self.retry_limit} attempts.")
                        
            time.sleep(random.uniform(*self.sleep_range))
            
        return list(linkedin_urls)
        
    async def save_profiles_to_csv(self, profiles: List[str], output_dir: str = None) -> str:
        """
        Save LinkedIn profile URLs to a CSV file.
        
        Args:
            profiles (List[str]): List of LinkedIn profile URLs
            output_dir (str, optional): Directory to save the CSV file
            
        Returns:
            str: Path to the saved CSV file
        """
        if not output_dir:
            output_dir = self.csv_cache_dir
            
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_path = os.path.join(output_dir, f"linkedin_profiles_{timestamp}.csv")
        
        with open(csv_path, 'w', newline='') as csv_file:
            writer = csv.writer(csv_file)
            writer.writerow(['LinkedIn Profile URL'])
            for profile in profiles:
                writer.writerow([profile])
                
        logging.info(f"LinkedIn profiles saved to: {csv_path}")
        return csv_path

    async def create_job(self, job: LinkedInJob) -> LinkedInJobResponse:
        """Create a new LinkedIn job posting."""
        try:
            logging.info(f"Creating new LinkedIn job: {job.title}")
            # Implementation here
            return LinkedInJobResponse(**job.dict())
        except Exception as e:
            logging.error(f"Error creating LinkedIn job: {str(e)}")
            raise

    async def get_job(self, job_id: str) -> Optional[LinkedInJobResponse]:
        """Get a LinkedIn job posting by ID."""
        try:
            logging.info(f"Retrieving LinkedIn job with ID: {job_id}")
            # Implementation here
            return None
        except Exception as e:
            logging.error(f"Error retrieving LinkedIn job: {str(e)}")
            raise

    async def list_jobs(self) -> List[LinkedInJobResponse]:
        """List all LinkedIn job postings."""
        try:
            logging.info("Listing all LinkedIn jobs")
            # Implementation here
            return []
        except Exception as e:
            logging.error(f"Error listing LinkedIn jobs: {str(e)}")
            raise

    async def apply_to_job(self, job_id: str) -> dict:
        """Apply to a LinkedIn job posting."""
        try:
            logging.info(f"Applying to LinkedIn job with ID: {job_id}")
            # Implementation here
            return {"status": "success", "message": "Application submitted"}
        except Exception as e:
            logging.error(f"Error applying to LinkedIn job: {str(e)}")
            raise 