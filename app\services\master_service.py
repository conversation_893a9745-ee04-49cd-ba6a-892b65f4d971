import os
import httpx
from app.logger import logging

master_service_host = os.getenv("MASTER_SERVICE_HOST", "http://localhost:3030")
master_service_key = os.getenv("MASTER_SERVICE_KEY", "default_api_key")
env = os.getenv("ENV", "prod")
platform = os.getenv("PLATFORM", "dash")
platform_level = os.getenv("PLATFORM_LEVEL", "global")



# sync registered user to master service
# curl --location 'http://localhost:3030/user/sync' \
# --header 'Content-Type: application/json' \
# --data-raw '{
#   "env": "prod",
#   "email": "<EMAIL>",
#   "companyName": "ABC",
#   "action": "register",
#   "platform": "dash",
#   "platformLevel": "global",
#   "platformUserId": null
# }'

async def sync_master_user(user_id: str, email: str, company_name: str, action: str = "register"):
    logging.info("sync_master_user called")
    try:
        if not master_service_host or not master_service_key:
            logging.warning("Master service host or key is not set. Skipping user sync.")
            return None
        
        if not email or not user_id:
            logging.error("Email or user ID is missing. Cannot sync user.")
            return None

        if action not in ["register", "login"]:
            logging.error(f"Invalid action: {action}. Must be one of 'register', 'login'")
            return None

        logging.info(f"Sync user to master service: {action} - {email}")
        headers = {"X-API-Key": master_service_key}
        data = {
            "env": env,
            "email": email,
            "companyName": company_name, 
            "action": action,
            "platform": platform,
            "platformLevel": platform_level,
            "platformUserId": user_id
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(f'{master_service_host}/user/sync', json=data, headers=headers)
            return response.json()
    except Exception as e:
        logging.error(f"Error syncing user to master service: {e}")
        return None
    
# curl --location 'http://localhost:3030/user' \
# --header 'Content-Type: application/json' \
# --data-raw '{
#   "env": "prod",
#   "email": "<EMAIL>",
#   "platform": "dash",
#   "platformLevel": "global"
# }'

async def get_master_user(email: str):
    try:
        if not master_service_host or not master_service_key:
            logging.warning("Master service host or key is not set. Skipping user fetch.")
            return None
        
        if not email:
            logging.error("Email is missing. Cannot fetch user.")
            return None

        logging.info(f"Fetch user from master service: {email}")
        headers = {"X-API-Key": master_service_key}
        data = {
            "env": env,
            "email": email,
            "platform": platform,
            "platformLevel": platform_level
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(f'{master_service_host}/user', json=data, headers=headers)
            return response.json()
    except Exception as e:
        logging.error(f"Error fetching user from master service: {e}")
        return None
    

# method PUT to update user in master service
# curl --location 'http://localhost:3030/user/sync' \
# --header 'Content-Type: application/json' \
# --data-raw '{
#   "env": "prod",
#   "email": "<EMAIL>",
#   "platform": "dash",
#   "platformLevel": "global",
#   "credits": 8,
#   "companyName": "TalentLabs"
# }

async def update_master_user(email: str, company_name: str, credits: int):
    try:
        if not master_service_host or not master_service_key:
            logging.warning("Master service host or key is not set. Skipping user update.")
            return None
        
        if not email:
            logging.error("Email is missing. Cannot update user.")
            return None

        logging.info(f"Update user in master service: {email}")
        headers = {"api_key": master_service_key}
        headers = {"X-API-Key": master_service_key}
        data = {
            "env": env,
            "email": email,
            "companyName": company_name,
            "credits": credits,
            "platform": platform,
            "platformLevel": platform_level
        }
        async with httpx.AsyncClient() as client:
            response = await client.put(f'{master_service_host}/user', json=data, headers=headers)
            return response.json()
    except Exception as e:
        logging.error(f"Error updating user in master service: {e}")
        return None