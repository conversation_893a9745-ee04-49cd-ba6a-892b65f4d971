from sqlalchemy.orm import Session
from app.db.db_models import Note, JobDescription
from app.models.note_schemas import NoteCreate
from uuid import UUI<PERSON>
from typing import List, Dict, Any
from datetime import datetime

class NoteService:
    def create_note(self, db: Session, user_id: UUID, note: NoteCreate) -> Dict[str, Any]:
        # Update job description if job_title or location is provided
        if note.job_title or note.location:
            job_desc = db.query(JobDescription).filter_by(id=note.job_id, user_id=user_id).first()
            if job_desc:
                # Only update job_title if current value is empty or "N/A"
                if note.job_title and self._is_field_empty_or_na(job_desc.job_title):
                    job_desc.job_title = note.job_title
                
                # Only update location if current value is empty or "N/A"
                if note.location and self._is_field_empty_or_na(job_desc.location):
                    job_desc.location = note.location
                
                db.commit()
        
        # Check if a note already exists for this job_id and user_id
        db_note = db.query(Note).filter_by(job_id=note.job_id, user_id=user_id).first()
        if db_note:
            db_note.note_text = note.note_text
            db_note.created_at = datetime.utcnow()
            db.commit()
            db.refresh(db_note)
        else:
            # If not, create a new note
            db_note = Note(
                job_id=note.job_id,
                user_id=user_id,
                note_text=note.note_text
            )
            db.add(db_note)
            db.commit()
            db.refresh(db_note)
        
        # Check for missing fields in the associated job description (after potential updates)
        missing_fields = self._check_missing_fields(db, note.job_id)
        
        # Return note data with missing fields information
        return {
            "id": str(db_note.id),
            "job_id": str(db_note.job_id),
            "user_id": str(db_note.user_id),
            "note_text": db_note.note_text,
            "created_at": db_note.created_at.isoformat() if db_note.created_at else None,
            "missing_fields": missing_fields
        }
    
    def _is_field_empty_or_na(self, field_value: str) -> bool:
        """Check if a field value is empty or 'N/A'."""
        if not field_value:
            return True
        if isinstance(field_value, str) and field_value.strip().upper() in ("", "N/A", "NONE"):
            return True
        return False
    
    def _check_missing_fields(self, db: Session, job_id: UUID) -> List[str]:
        """Check if job_title or location fields are missing from the job description."""
        missing_fields = []
        
        # Get the job description
        job_desc = db.query(JobDescription).filter_by(id=job_id).first()
        if job_desc:
            # Check job_title field
            if not job_desc.job_title or job_desc.job_title.strip().upper() == "N/A":
                missing_fields.append("job_title")
            
            # Check location field
            if not job_desc.location or job_desc.location.strip().upper() == "N/A":
                missing_fields.append("location")
        
        return missing_fields

    def list_notes_for_job(self, db: Session, user_id: UUID, job_id: UUID) -> List[Note]:
        return db.query(Note).filter_by(user_id=user_id, job_id=job_id).order_by(Note.created_at.desc()).all()