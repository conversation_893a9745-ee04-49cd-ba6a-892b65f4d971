from sqlalchemy.orm import Session
from app.db.db_models import Note
from app.models.note_schemas import NoteCreate
from uuid import UUI<PERSON>
from typing import List
from datetime import datetime

class NoteService:
    def create_note(self, db: Session, user_id: UUID, note: NoteCreate) -> Note:
        # Check if a note already exists for this job_id and user_id
        db_note = db.query(Note).filter_by(job_id=note.job_id, user_id=user_id).first()
        if db_note:
            db_note.note_text = note.note_text
            db_note.created_at = datetime.utcnow()
            db.commit()
            db.refresh(db_note)
            return db_note
        # If not, create a new note
        db_note = Note(
            job_id=note.job_id,
            user_id=user_id,
            note_text=note.note_text
        )
        db.add(db_note)
        db.commit()
        db.refresh(db_note)
        return db_note

    def list_notes_for_job(self, db: Session, user_id: UUID, job_id: UUID) -> List[Note]:
        return db.query(Note).filter_by(user_id=user_id, job_id=job_id).order_by(Note.created_at.desc()).all() 