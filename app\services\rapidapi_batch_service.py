import time
import requests
from sqlalchemy.orm import Session
from sqlalchemy import <PERSON><PERSON><PERSON>
from app.db.db_models import SerpAPIResult, RapidAPIResult, JobDescription
from uuid import UUID
from typing import List, Dict, Any
import os
from app.logger import logging

RAPIDAPI_KEY = os.getenv("RAPIDAPI_KEY", "**************************************************")
RAPIDAPI_URL = "https://linkedin-api8.p.rapidapi.com/get-profile-data-by-url"

class RapidAPIBatchService:
    def _clean_linkedin_url(self, url: str) -> str:
        """
        Clean LinkedIn URL by removing tracking parameters and ensuring it's a valid profile URL.
        
        Args:
            url (str): Raw LinkedIn URL
            
        Returns:
            str: Cleaned LinkedIn URL or empty string if invalid
        """
        try:
            # Remove tracking parameters
            tracking_params = ['?_ri_', '&_ei_', '&_di_', '?trk=', '&trk=']
            cleaned_url = url
            for param in tracking_params:
                if param in cleaned_url:
                    cleaned_url = cleaned_url.split(param)[0]
            
            # Ensure it's a valid LinkedIn profile URL
            if '/in/' in cleaned_url or '/pub/' in cleaned_url:
                # Remove any trailing slashes or parameters
                cleaned_url = cleaned_url.rstrip('/?&')
                return cleaned_url
            
            return ""
        except Exception as e:
            logging.error(f"Error cleaning LinkedIn URL {url}: {e}")
            return ""

    def fetch_and_store_candidates(self, db: Session, job_id: UUID, user_id: UUID = None) -> List[Dict[str, Any]]:
        logging.info(f"[Batch] Starting fetch_and_store_candidates for job_id={job_id}, user_id={user_id}")
        # 1. Optionally enforce user ownership
        job_query = db.query(JobDescription).filter(JobDescription.id == job_id)
        if user_id is not None:
            job_query = job_query.filter(JobDescription.user_id == user_id)
        job = job_query.first()
        if not job:
            logging.error(f"[Batch] Job not found or access denied for job_id={job_id}, user_id={user_id}")
            raise Exception("Job not found or you do not have access to this job.")
        # 2. Get all LinkedIn URLs for this job
        serp_results = db.query(SerpAPIResult).filter(SerpAPIResult.job_id == job_id).all()
        logging.info(f"[Batch] Found {len(serp_results)} LinkedIn URLs for job_id={job_id}")
        
        # Filter out invalid URLs and already processed URLs
        valid_unprocessed_serp_results = []
        already_processed_count = 0
        invalid_url_count = 0
        
        for serp in serp_results:
            cleaned_url = self._clean_linkedin_url(serp.linkedin_url)
            if not cleaned_url:
                logging.warning(f"[Batch] Skipping invalid LinkedIn URL from database: {serp.linkedin_url}")
                invalid_url_count += 1
                continue
            
            # Check if this URL has already been processed successfully by RapidAPI
            existing_rapid_result = db.query(RapidAPIResult).filter(
                RapidAPIResult.serp_result_id == serp.id
            ).first()
            
            if existing_rapid_result:
                # Since we only store successful results, if a record exists, it was successful
                already_processed_count += 1
                logging.debug(f"[Batch] Skipping already processed URL: {cleaned_url}")
                continue
            else:
                # New URL or previously failed URL (not stored), needs processing
                valid_unprocessed_serp_results.append(serp)
        
        logging.info(f"[Batch] URL Analysis for job_id={job_id}:")
        logging.info(f"  - Total URLs from SerpAPI: {len(serp_results)}")
        logging.info(f"  - Invalid URLs: {invalid_url_count}")
        logging.info(f"  - Already processed successfully: {already_processed_count}")
        logging.info(f"  - New/retry URLs to process: {len(valid_unprocessed_serp_results)}")
        
        if not valid_unprocessed_serp_results:
            logging.info(f"[Batch] No new URLs to process for job_id={job_id}. All URLs have been processed successfully.")
            # Return existing candidates from successful extractions
            existing_candidates = self._get_existing_candidates(db, job_id)
            return existing_candidates
        
        serp_results = valid_unprocessed_serp_results
        candidates = []
        BATCH_SIZE = 50
        RETRY_LIMIT = 2
        RETRY_DELAY = 1
        api_calls_made = 0
        successful_extractions = 0
        failed_extractions = 0
        for batch_start in range(0, len(serp_results), BATCH_SIZE):
            batch = serp_results[batch_start:batch_start+BATCH_SIZE]
            logging.info(f"[Batch] Processing batch {batch_start//BATCH_SIZE+1} ({len(batch)} candidates)")
            for serp in batch:
                # Clean the LinkedIn URL
                cleaned_url = self._clean_linkedin_url(serp.linkedin_url)
                if not cleaned_url:
                    logging.warning(f"[Batch] Skipping invalid LinkedIn URL: {serp.linkedin_url}")
                    continue
                
                # Check for existing RapidAPIResult for this serp_result_id
                rapid_result = db.query(RapidAPIResult).filter(RapidAPIResult.serp_result_id == serp.id).order_by(RapidAPIResult.created_at.desc()).first()
                use_api = True
                if rapid_result is not None:
                    content_json = rapid_result.content_json
                    # Use cached result if not a failed extraction (success is not False)
                    if not (isinstance(content_json, dict) and content_json.get("success") is False):
                        mapped = self.map_candidate(content_json, serp.linkedin_url)
                        candidates.append(mapped)
                        use_api = False
                        logging.info(f"[Batch] Used cached RapidAPIResult for {cleaned_url}")
                    else:
                        # Retry failed extractions
                        logging.info(f"[Batch] Retrying failed extraction for {cleaned_url}")
                        use_api = True
                if use_api:
                    headers = {
                        "x-rapidapi-key": RAPIDAPI_KEY,
                        "x-rapidapi-host": "linkedin-api8.p.rapidapi.com"
                    }
                    params = {"url": cleaned_url}
                    attempt = 0
                    success = False
                    content_json = None
                    api_calls_made += 1
                    while attempt < RETRY_LIMIT and not success:
                        try:
                            logging.info(f"[Batch] Fetching {cleaned_url} from RapidAPI (attempt {attempt+1})")
                            response = requests.get(RAPIDAPI_URL, headers=headers, params=params, timeout=15)
                            if response.status_code == 200:
                                content_json = response.json()
                                # Check for RapidAPI error structure
                                if (isinstance(content_json, dict) and content_json.get("success") is False):
                                    error_message = content_json.get("message", "Unknown error")
                                    logging.warning(f"[Batch] RapidAPI returned error for {cleaned_url}: {error_message}")
                                    
                                    # Retry for specific error types
                                    if "url is not valid" in error_message.lower() or "not found" in error_message.lower():
                                        logging.info(f"[Batch] URL validation error, will retry: {cleaned_url}")
                                        attempt += 1
                                        time.sleep(RETRY_DELAY)
                                        continue
                                    else:
                                        # For other errors, don't retry
                                        break
                                # 4. Store in rapidapi_results
                                rapid_result = RapidAPIResult(
                                    job_id=job_id,
                                    serp_result_id=serp.id,
                                    content_json=content_json,
                                    extracted_name=content_json.get("firstName", "") + " " + content_json.get("lastName", ""),
                                    extracted_position=content_json.get("headline", "")
                                )
                                db.add(rapid_result)
                                db.commit()
                                db.refresh(rapid_result)
                                # 5. Map to information_extracted.json format
                                mapped = self.map_candidate(content_json, serp.linkedin_url)
                                candidates.append(mapped)
                                successful_extractions += 1
                                logging.info(f"[Batch] Successfully fetched and stored candidate for {cleaned_url}")
                                success = True
                            else:
                                logging.warning(f"[Batch] RapidAPI non-200 response for {cleaned_url}: {response.status_code}")
                                attempt += 1
                                time.sleep(RETRY_DELAY)
                        except Exception as ex:
                            logging.error(f"[Batch] Exception during RapidAPI fetch for {cleaned_url}: {ex}")
                            attempt += 1
                            time.sleep(RETRY_DELAY)
                    # If all retries failed, don't store in database, just log the failure
                    if not success:
                        failed_extractions += 1
                        error_message = content_json.get("message") if content_json and isinstance(content_json, dict) else "Unknown error"
                        logging.error(f"[Batch] Failed to fetch candidate for {cleaned_url} after {RETRY_LIMIT} attempts. Error: {error_message}")
                        # Don't add failed candidates to the results list either
            # Rate limit: sleep 60 seconds if more batches remain
            if batch_start + BATCH_SIZE < len(serp_results):
                logging.info(f"[Batch] Batch {batch_start//BATCH_SIZE+1} complete. Sleeping for 60 seconds to respect rate limits.")
                time.sleep(60)
        # 6. Update job_descriptions.information_extracted_json
        job.information_extracted_json = candidates
        db.commit()
        
        # Final summary with cost information
        logging.info(f"[Batch] Processing Summary for job_id={job_id}:")
        logging.info(f"  - API calls made: {api_calls_made}")
        logging.info(f"  - Successful extractions: {successful_extractions}")
        logging.info(f"  - Failed extractions: {failed_extractions}")
        logging.info(f"  - Total candidates returned: {len(candidates)}")
        logging.info(f"  - Estimated cost: ${api_calls_made * 0.01:.2f} (assuming $0.01 per API call)")
        
        return candidates

    def _get_existing_candidates(self, db: Session, job_id: UUID) -> List[Dict[str, Any]]:
        """
        Get existing candidates that have been successfully processed for a job_id.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            
        Returns:
            List[Dict]: List of existing candidate data
        """
        try:
            # Get all RapidAPI results for this job_id (all stored results are successful)
            successful_results = db.query(RapidAPIResult).join(
                SerpAPIResult, SerpAPIResult.id == RapidAPIResult.serp_result_id
            ).filter(
                SerpAPIResult.job_id == job_id
            ).all()
            
            candidates = []
            for rapid_result in successful_results:
                try:
                    # Get the corresponding SerpAPI result for the LinkedIn URL
                    serp_result = db.query(SerpAPIResult).filter(
                        SerpAPIResult.id == rapid_result.serp_result_id
                    ).first()
                    
                    if serp_result:
                        # Map the candidate data
                        mapped = self.map_candidate(rapid_result.content_json, serp_result.linkedin_url)
                        candidates.append(mapped)
                except Exception as e:
                    logging.error(f"Error mapping existing candidate: {e}")
                    continue
            
            logging.info(f"[Batch] Retrieved {len(candidates)} existing candidates for job_id={job_id}")
            return candidates
            
        except Exception as e:
            logging.error(f"Error getting existing candidates for job_id {job_id}: {str(e)}")
            return []

    def get_processing_statistics(self, db: Session, job_id: UUID) -> Dict[str, Any]:
        """
        Get statistics about the processing status for a job.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            
        Returns:
            Dict: Processing statistics
        """
        try:
            from sqlalchemy import func
            
            # Get total URLs for this job
            total_urls = db.query(func.count(SerpAPIResult.id)).filter(
                SerpAPIResult.job_id == job_id
            ).scalar() or 0
            
            # Get URLs that have been processed by RapidAPI
            processed_urls = db.query(func.count(RapidAPIResult.id)).join(
                SerpAPIResult, SerpAPIResult.id == RapidAPIResult.serp_result_id
            ).filter(SerpAPIResult.job_id == job_id).scalar() or 0
            
            # Get URLs with successful extractions (all stored results are successful)
            successful_extractions = db.query(func.count(RapidAPIResult.id)).join(
                SerpAPIResult, SerpAPIResult.id == RapidAPIResult.serp_result_id
            ).filter(
                SerpAPIResult.job_id == job_id
            ).scalar() or 0
            
            # Failed extractions are not stored in database, so count is always 0
            failed_extractions = 0
            
            # Calculate cost savings (assuming $0.01 per API call)
            pending_urls = total_urls - processed_urls
            cost_savings = pending_urls * 0.01
            estimated_total_cost = total_urls * 0.01
            actual_cost = processed_urls * 0.01
            
            return {
                "job_id": str(job_id),
                "total_urls": total_urls,
                "processed_urls": processed_urls,
                "pending_urls": pending_urls,
                "successful_extractions": successful_extractions,
                "failed_extractions": failed_extractions,
                "success_rate": 100.0 if processed_urls > 0 else 0,  # All stored results are successful
                "cost_analysis": {
                    "estimated_total_cost": f"${estimated_total_cost:.2f}",
                    "actual_cost": f"${actual_cost:.2f}",
                    "cost_savings": f"${cost_savings:.2f}",
                    "cost_savings_percentage": f"{(cost_savings / estimated_total_cost * 100):.1f}%" if estimated_total_cost > 0 else "0%"
                }
            }
        except Exception as e:
            logging.error(f"Error getting processing statistics for job_id {job_id}: {str(e)}")
            return {
                "job_id": str(job_id),
                "error": str(e)
            }

    def get_url_processing_details(self, db: Session, job_id: UUID) -> Dict[str, Any]:
        """
        Get detailed information about which URLs have been processed and which are pending.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            
        Returns:
            Dict: Detailed URL processing information
        """
        try:
            # Get all SerpAPI results for this job
            serp_results = db.query(SerpAPIResult).filter(SerpAPIResult.job_id == job_id).all()
            
            processed_urls = []
            pending_urls = []
            
            for serp_result in serp_results:
                # Check if this URL has been processed by RapidAPI
                rapid_result = db.query(RapidAPIResult).filter(
                    RapidAPIResult.serp_result_id == serp_result.id
                ).first()
                
                url_info = {
                    "serp_result_id": str(serp_result.id),
                    "linkedin_url": serp_result.linkedin_url,
                    "cleaned_url": self._clean_linkedin_url(serp_result.linkedin_url),
                    "created_at": serp_result.created_at.isoformat() if serp_result.created_at else None
                }
                
                if rapid_result:
                    # URL has been processed successfully (only successful results are stored)
                    url_info.update({
                        "rapid_result_id": str(rapid_result.id),
                        "status": "success",
                        "extracted_name": rapid_result.extracted_name,
                        "extracted_position": rapid_result.extracted_position,
                        "processed_at": rapid_result.created_at.isoformat() if rapid_result.created_at else None
                    })
                    processed_urls.append(url_info)
                else:
                    # URL is pending (never processed or failed and not stored)
                    url_info["status"] = "pending"
                    pending_urls.append(url_info)
            
            return {
                "job_id": str(job_id),
                "total_urls": len(serp_results),
                "processed_urls": len(processed_urls),
                "pending_urls": len(pending_urls),
                "processed_details": processed_urls,
                "pending_details": pending_urls
            }
            
        except Exception as e:
            logging.error(f"Error getting URL processing details for job_id {job_id}: {str(e)}")
            return {
                "job_id": str(job_id),
                "error": str(e)
            }

    def map_candidate(self, api_json: Dict, linkedin_url: str) -> Dict:
        # Map the RapidAPI JSON to the information_extracted.json format
        # This is a simplified example; adjust as needed for your real mapping
        return {
            "Name": f"{api_json.get('firstName', '')} {api_json.get('lastName', '')}",
            "Contact": {
                "LinkedIn": linkedin_url,
                "Location": api_json.get("geo", {}).get("full", "")
            },
            "Universities": [
                {
                    "Name": edu.get("schoolName", ""),
                    "Location": edu.get("geo", {}).get("full", ""),
                    "Major": edu.get("fieldOfStudy", ""),
                    "Degree": edu.get("degree", ""),
                    "Tenure": f"{edu.get('start', {}).get('year', '')} - {edu.get('end', {}).get('year', '')}"
                } for edu in api_json.get("educations", [])
            ],
            "Experiences": [
                {
                    "Company": pos.get("companyName", ""),
                    "Location": pos.get("location", ""),
                    "Tenure": f"{pos.get('start', {}).get('month', '')}/{pos.get('start', {}).get('year', '')} - {pos.get('end', {}).get('month', '')}/{pos.get('end', {}).get('year', '')}",
                    "Position": pos.get("title", "")
                } for pos in api_json.get("position", [])
            ],
            "Skills": [s.get("name", "") for s in api_json.get("skills", [])],
            "FileName": api_json.get("urn", "")
        } 