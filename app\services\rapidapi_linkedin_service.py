import os
import json
import time
import random
import requests
import csv
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
from app.logger import logging

class RapidAPILinkedInService:
    def __init__(self, rapidapi_key: str = None):
        """
        Initialize the RapidAPI LinkedIn service.
        
        Args:
            rapidapi_key (str): RapidAPI key for LinkedIn API access
        """
        self.rapidapi_key = rapidapi_key or os.getenv("RAPIDAPI_KEY")
        if not self.rapidapi_key:
            raise ValueError("RapidAPI key is required. Set RAPIDAPI_KEY environment variable or pass it to constructor.")
        
        self.api_url = "https://linkedin-api8.p.rapidapi.com/get-profile-data-by-url"
        self.api_host = "linkedin-api8.p.rapidapi.com"
        
        # User agents for rotation
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Firefox/89.0"
        ]
        
        # Cache directory
        self.cache_dir = os.path.join(os.getcwd(), "rapidapi_linkedin_cache")
        os.makedirs(self.cache_dir, exist_ok=True)

    def _get_cache_key(self, url: str) -> str:
        """Generate a cache key for the LinkedIn URL."""
        import hashlib
        return hashlib.md5(url.encode()).hexdigest()

    def _get_cached_profile(self, url: str) -> Optional[Dict]:
        """Get cached profile data if available."""
        try:
            cache_key = self._get_cache_key(url)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"Error reading cached profile: {str(e)}")
        return None

    def _cache_profile(self, url: str, profile_data: Dict) -> None:
        """Cache profile data."""
        try:
            cache_key = self._get_cache_key(url)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            with open(cache_file, 'w') as f:
                json.dump(profile_data, f, indent=2)
            logging.info(f"Cached profile data for: {url}")
        except Exception as e:
            logging.error(f"Error caching profile: {str(e)}")

    def _log_error(self, profile_url: str, error_message: str) -> None:
        """Log errors encountered during API requests."""
        try:
            with open("rapidapi_error_log.txt", "a") as log_file:
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                log_file.write(f"[{timestamp}] Error fetching {profile_url}: {error_message}\n")
        except Exception as e:
            logging.error(f"Error writing to error log: {str(e)}")

    async def fetch_linkedin_profile(self, profile_url: str, retries: int = 3) -> Dict:
        """
        Fetch LinkedIn profile data for a given URL using RapidAPI.
        
        Args:
            profile_url (str): LinkedIn profile URL
            retries (int): Number of retry attempts
            
        Returns:
            Dict: Profile data or error information
        """
        try:
            # Check cache first
            cached_data = self._get_cached_profile(profile_url)
            if cached_data:
                logging.info(f"Returning cached profile data for: {profile_url}")
                return cached_data
            
            logging.info(f"Fetching profile data for: {profile_url}")
            
            headers = {
                "x-rapidapi-key": self.rapidapi_key,
                "x-rapidapi-host": self.api_host,
                "User-Agent": random.choice(self.user_agents)
            }
            
            params = {"url": profile_url}
            
            for attempt in range(retries):
                try:
                    response = requests.get(
                        self.api_url, 
                        headers=headers, 
                        params=params, 
                        timeout=15
                    )
                    
                    if response.status_code == 200:
                        profile_data = response.json()
                        
                        # Cache the successful result
                        self._cache_profile(profile_url, profile_data)
                        
                        logging.info(f"Successfully fetched profile data for: {profile_url}")
                        return profile_data
                        
                    elif response.status_code in [429, 503]:  # Rate limit or service unavailable
                        wait_time = 2 ** attempt + random.uniform(0, 1)
                        logging.warning(f"Rate limit hit for {profile_url}, waiting {wait_time}s before retry {attempt + 1}")
                        time.sleep(wait_time)
                        
                    else:
                        error_msg = f"API request failed with status code: {response.status_code}"
                        logging.error(f"{error_msg} for {profile_url}")
                        self._log_error(profile_url, error_msg)
                        return {"error": error_msg}
                        
                except requests.RequestException as e:
                    wait_time = 2 ** attempt + random.uniform(0, 1)
                    logging.warning(f"Request failed for {profile_url}, waiting {wait_time}s before retry {attempt + 1}: {str(e)}")
                    time.sleep(wait_time)
            
            error_msg = "Max retries exceeded"
            logging.error(f"{error_msg} for {profile_url}")
            self._log_error(profile_url, error_msg)
            return {"error": error_msg}
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            logging.error(f"{error_msg} for {profile_url}")
            self._log_error(profile_url, error_msg)
            return {"error": error_msg}

    def transform_linkedin_data(self, api_response: Dict) -> Dict:
        """
        Transform the raw LinkedIn API response into a structured format.
        
        Args:
            api_response (Dict): Raw API response
            
        Returns:
            Dict: Transformed profile data
        """
        if not api_response or "error" in api_response:
            return {
                "name": "",
                "contact": {
                    "linkedin": "",
                    "location": ""
                },
                "universities": [],
                "experiences": [],
                "skills": [],
                "file_name": "",
                "error": api_response.get("error", "No data available")
            }

        profile = api_response
        name = f"{profile.get('firstName', '')} {profile.get('lastName', '')}".strip()

        contact = {
            "linkedin": f"https://www.linkedin.com/in/{profile.get('username', '')}",
            "location": profile.get("geo", {}).get("full", "")
        }

        # Extract education/universities
        universities = []
        for education in profile.get("educations", []):
            start_year = education.get('start', {}).get('year', '')
            end_year = education.get('end', {}).get('year', '')
            tenure = f"{start_year} - {end_year}".strip(" -")
            
            universities.append({
                "name": education.get("schoolName", ""),
                "location": education.get("geo", {}).get("full", ""),
                "major": education.get("fieldOfStudy", ""),
                "degree": education.get("degree", ""),
                "tenure": tenure
            })

        # Extract work experiences
        experiences = []
        for position in profile.get("position", []):
            start_month = position.get('start', {}).get('month', '')
            start_year = position.get('start', {}).get('year', '')
            end_month = position.get('end', {}).get('month', '')
            end_year = position.get('end', {}).get('year', '')
            
            start_date = f"{start_month}/{start_year}".strip("/")
            end_date = f"{end_month}/{end_year}".strip("/")
            tenure = f"{start_date} - {end_date}" if start_date or end_date else ""
            
            experiences.append({
                "company": position.get("companyName", ""),
                "location": position.get("location", ""),
                "tenure": tenure,
                "position": position.get("title", "")
            })

        # Extract skills
        skills = [skill.get("name", "") for skill in profile.get("skills", [])]
        file_name = profile.get("urn", "")

        return {
            "name": name,
            "contact": contact,
            "universities": universities,
            "experiences": experiences,
            "skills": skills,
            "file_name": file_name
        }

    async def fetch_and_transform_batched(self, profile_urls: List[str], max_workers: int = 5) -> List[Dict]:
        """
        Process LinkedIn URLs in batches with rate limiting.
        
        Args:
            profile_urls (List[str]): List of LinkedIn profile URLs
            max_workers (int): Maximum concurrent workers
            
        Returns:
            List[Dict]: List of transformed profile data
        """
        total_urls = len(profile_urls)
        processed_data = []
        
        logging.info(f"Starting batch processing of {total_urls} LinkedIn profiles")
        
        # Process in batches of 50 to respect API limits
        for i in range(0, total_urls, 50):
            batch_urls = profile_urls[i:i + 50]
            batch_num = i // 50 + 1
            logging.info(f"Processing batch {batch_num}: {len(batch_urls)} URLs...")

            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_url = {
                    executor.submit(self.fetch_linkedin_profile, url): url 
                    for url in batch_urls
                }
                
                for future in as_completed(future_to_url):
                    try:
                        profile_data = future.result(timeout=20)
                        transformed_data = self.transform_linkedin_data(profile_data)
                        
                        linkedin_url = future_to_url[future].split("?")[0]
                        
                        # Only include profiles with valid data
                        if transformed_data["contact"]["linkedin"] != "https://www.linkedin.com/in/":
                            processed_data.append(transformed_data)
                            logging.info(f"Successfully processed: {linkedin_url}")
                        else:
                            logging.warning(f"Skipping empty profile for: {linkedin_url}")
                            
                    except Exception as e:
                        linkedin_url = future_to_url[future]
                        logging.error(f"Error processing {linkedin_url}: {str(e)}")
            
            # Rate limiting: wait between batches
            if i + 50 < total_urls:
                logging.info("Rate limit reached, waiting 60 seconds before next batch...")
                time.sleep(60)

        logging.info(f"Batch processing completed. Processed {len(processed_data)} profiles successfully.")
        return processed_data

    def extract_linkedin_urls_from_csv(self, csv_file_path: str) -> Tuple[List[str], Optional[str]]:
        """
        Extract LinkedIn URLs from a CSV file.
        
        Args:
            csv_file_path (str): Path to the CSV file
            
        Returns:
            Tuple[List[str], Optional[str]]: List of LinkedIn URLs and error message if any
        """
        linkedin_urls = set()
        
        try:
            with open(csv_file_path, mode='r', encoding='utf-8') as file:
                reader = csv.DictReader(file)
                headers = reader.fieldnames
                logging.info(f"CSV headers: {headers}")

                for row_num, row in enumerate(reader):
                    for key, value in row.items():
                        if isinstance(value, str):
                            value = value.strip()
                            # Check if value is a LinkedIn URL
                            if "linkedin.com/in/" in value:
                                if not value.startswith("http"):
                                    value = "https://" + value
                                linkedin_urls.add(value.split("?")[0])
            
            if not linkedin_urls:
                logging.warning("No valid LinkedIn URLs found in CSV.")
                return [], "No valid LinkedIn URLs found in CSV."
            else:
                logging.info(f"Extracted {len(linkedin_urls)} LinkedIn URLs from CSV")

        except Exception as e:
            error_msg = f"Error extracting LinkedIn URLs: {str(e)}"
            logging.error(error_msg)
            return [], error_msg

        return list(linkedin_urls), None

    async def process_linkedin_csv(self, csv_file_path: str, output_dir: str = "output") -> Tuple[Optional[str], Optional[str]]:
        """
        Process a CSV of LinkedIn URLs, fetch profile data, and save as JSON and CSV.
        
        Args:
            csv_file_path (str): Path to the input CSV file
            output_dir (str): Directory to save output files
            
        Returns:
            Tuple[Optional[str], Optional[str]]: Paths to JSON and CSV output files
        """
        try:
            if not os.path.exists(csv_file_path):
                raise FileNotFoundError(f"CSV file not found: {csv_file_path}")

            logging.info(f"Extracting LinkedIn URLs from: {csv_file_path}")
            linkedin_urls, extraction_error = self.extract_linkedin_urls_from_csv(csv_file_path)
            
            if extraction_error:
                raise Exception(f"Extraction error: {extraction_error}")
            
            if not linkedin_urls:
                raise ValueError("No valid LinkedIn URLs found in the CSV.")

            logging.info(f"Found {len(linkedin_urls)} LinkedIn profiles. Starting fetch...")

            # Fetch and transform in batches
            final_output = await self.fetch_and_transform_batched(linkedin_urls)

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Save JSON
            json_path = os.path.join(output_dir, "information_extracted.json")
            with open(json_path, "w") as jf:
                json.dump(final_output, jf, indent=4)
            logging.info(f"Saved JSON output to: {json_path}")

            # Save CSV
            csv_path = os.path.join(output_dir, "raw_candidates.csv")
            if final_output:
                keys = final_output[0].keys()
                with open(csv_path, "w", newline='', encoding='utf-8') as cf:
                    writer = csv.DictWriter(cf, fieldnames=keys)
                    writer.writeheader()
                    writer.writerows(final_output)
                logging.info(f"Saved CSV output to: {csv_path}")
            else:
                logging.warning("No data to write to CSV.")

            return json_path, csv_path

        except Exception as e:
            logging.exception("Error during processing.")
            logging.error(f"Failed to process: {e}")
            return None, None

    async def get_cache_stats(self) -> Dict:
        """Get statistics about cached profiles."""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
            total_size = sum(os.path.getsize(os.path.join(self.cache_dir, f)) for f in cache_files)
            
            return {
                "total_cached_profiles": len(cache_files),
                "cache_directory": self.cache_dir,
                "total_cache_size_bytes": total_size,
                "total_cache_size_mb": round(total_size / (1024 * 1024), 2)
            }
        except Exception as e:
            logging.error(f"Error getting cache stats: {str(e)}")
            return {"error": str(e)}

    async def clear_cache(self) -> Dict:
        """Clear all cached profile data."""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
            for filename in cache_files:
                os.remove(os.path.join(self.cache_dir, filename))
            
            logging.info(f"Cleared {len(cache_files)} cached profile files")
            return {
                "message": f"Cleared {len(cache_files)} cached profile files",
                "cleared_count": len(cache_files)
            }
        except Exception as e:
            logging.error(f"Error clearing cache: {str(e)}")
            raise Exception(f"Failed to clear cache: {str(e)}") 