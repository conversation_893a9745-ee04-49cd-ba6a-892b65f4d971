from typing import Optional, Dict, Any
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import json
import os
from app.logger import logging
import time

import random
import hashlib
from urllib.parse import urlparse, parse_qs
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
import unicodedata


class ScrapingService:
    def __init__(self):
        self.cache_dir = "scrape_cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        
    def _url_hash(self, url: str) -> str:
        return hashlib.md5(url.encode()).hexdigest()
        
    def _cache_response(self, url: str, data: Dict[str, Any]):
        try:
            file_path = os.path.join(self.cache_dir, self._url_hash(url) + ".json")
            with open(file_path, 'w') as f:
                json.dump(data, f)
            logging.info(f"Cached response for URL: {url}")
        except Exception as e:
            logging.error(f"Error caching response: {str(e)}")
            
    def _get_cached_response(self, url: str) -> Optional[Dict[str, Any]]:
        try:
            file_path = os.path.join(self.cache_dir, self._url_hash(url) + ".json")
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logging.error(f"Error reading cached response: {str(e)}")
            return None
        
    def _setup_driver(self) -> webdriver.Chrome:
        try:
            logging.info("Setting up Chrome WebDriver...")
            options = Options()
            options.add_argument("--headless=new")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-gpu")
            options.add_argument("--window-size=1920,1080")
            options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            
            driver_path = ChromeDriverManager().install()
            # Ensure driver_path is the actual binary, not a directory or a text file
            if os.path.isdir(driver_path):
                # For Mac ARM, the binary is usually named 'chromedriver-mac-arm64'
                possible_bin = os.path.join(driver_path, 'chromedriver-mac-arm64')
                if os.path.exists(possible_bin):
                    driver_path = possible_bin
                else:
                    # Fallback to 'chromedriver'
                    driver_path = os.path.join(driver_path, 'chromedriver')
            elif driver_path.endswith('THIRD_PARTY_NOTICES.chromedriver'):
                # If the path is a text file, get its parent and append the binary name
                parent = os.path.dirname(driver_path)
                possible_bin = os.path.join(parent, 'chromedriver-mac-arm64')
                if os.path.exists(possible_bin):
                    driver_path = possible_bin
                else:
                    driver_path = os.path.join(parent, 'chromedriver')

            driver = webdriver.Chrome(service=Service(driver_path), options=options)
            logging.info("Chrome WebDriver setup complete")
            return driver
        except Exception as e:
            logging.error(f"Error setting up Chrome driver: {str(e)}")
            raise
            
    def _extract_google_doc_content(self, doc_url: str) -> Optional[Dict[str, str]]:
        try:
            logging.info(f"Extracting content from Google Doc: {doc_url}")
            if "/edit" in doc_url:
                doc_url = doc_url.split("/edit")[0] + "/export?format=txt"
            
            response = requests.get(doc_url)
            if response.status_code == 200:
                return {
                    "source": "Google Docs",
                    "full_text": response.text.strip()
                }
            logging.warning(f"Failed to fetch Google Doc content. Status code: {response.status_code}")
            return None
        except Exception as e:
            logging.error(f"Error extracting Google Doc content: {str(e)}")
            return None
            
    def _scrape_linkedin(self, driver: webdriver.Chrome, url: str) -> Dict[str, str]:
        try:
            logging.info(f"Scraping LinkedIn URL: {url}")
            job_id = self._extract_linkedin_job_id(url)
            if not job_id:
                logging.warning(f"Could not extract job ID from URL: {url}")
                return self._scrape_linkedin_direct(driver, url)

            # Try RapidAPI first
            rapidapi_key = os.getenv("RAPIDAPI_KEY")
            if rapidapi_key:
                try:
                    logging.info("Attempting to fetch job details via RapidAPI")
                    response = requests.get(
                        "https://linkedin-data-api.p.rapidapi.com/get-job-details",
                        headers={
                            "x-rapidapi-key": rapidapi_key,
                            "x-rapidapi-host": "linkedin-data-api.p.rapidapi.com"
                        },
                        params={"id": job_id}
                    )
                    if response.status_code == 200:
                        return response.json()
                    logging.warning(f"RapidAPI request failed with status code: {response.status_code}")
                except Exception as e:
                    logging.warning(f"LinkedIn API failed: {str(e)}")

            # Fallback to direct scraping
            logging.info("Falling back to direct LinkedIn scraping")
            return self._scrape_linkedin_direct(driver, url)
            
        except Exception as e:
            logging.error(f"Error in LinkedIn scraping: {str(e)}")
            raise

    def _scrape_linkedin_direct(self, driver: webdriver.Chrome, url: str) -> Dict[str, str]:
        try:
            logging.info(f"Directly scraping LinkedIn URL: {url}")
            driver.get(url)
            
            # Wait for page load
            try:
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logging.info("Page body loaded")
            except TimeoutException:
                logging.error("Timeout waiting for page body to load")
                raise
            
            # Wait for job details to load
            time.sleep(2)  # Give JavaScript time to load content
            
            # Extract job details
            try:
                soup = BeautifulSoup(driver.page_source, "html.parser")
                
                title = soup.find("h1")
                location = soup.find("span", class_="topcard__flavor--bullet")
                description = soup.find("div", class_="description__text")
                
                if not description:
                    logging.warning("Could not find job description element")
                    description = soup.find("div", class_="show-more-less-html__markup")
                
                if not description:
                    logging.warning("Could not find any description content")
                    description = soup.find("div", class_="job-description")
                
                # Clean and normalize text
                def clean_text(text):
                    if not text:
                        return "N/A"
                    # Normalize Unicode characters
                    text = unicodedata.normalize("NFKD", text)
                    # Remove any remaining non-ASCII characters
                    text = text.encode("ascii", "ignore").decode("ascii")
                    return text.strip()
                
                result = {
                    "source": "LinkedIn Scrape",
                    "title": clean_text(title.get_text(strip=True) if title else None),
                    "location": clean_text(location.get_text(strip=True) if location else None),
                    "company": "N/A",  # LinkedIn often requires login for company info
                    "description": clean_text(description.get_text("\n", strip=True) if description else None),
                    "full_text": clean_text(soup.get_text("\n", strip=True)),
                    "job_requirements": []  # LinkedIn often requires login for detailed requirements
                }
                
                logging.info("Successfully extracted job details")
                return result
                
            except NoSuchElementException as e:
                logging.error(f"Could not find required element: {str(e)}")
                raise
                
        except TimeoutException:
            logging.error("Timeout waiting for LinkedIn page to load")
            raise
        except WebDriverException as e:
            logging.error(f"WebDriver error: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Error scraping LinkedIn directly: {str(e)}")
            raise
        
    def _extract_linkedin_job_id(self, url: str) -> Optional[str]:
        try:
            parsed = urlparse(url)
            query = parse_qs(parsed.query)
            job_id = query.get("currentJobId", [None])[0]
            if job_id:
                logging.info(f"Extracted job ID: {job_id}")
            else:
                logging.warning("Could not extract job ID from URL")
            return job_id
        except Exception as e:
            logging.error(f"Error extracting LinkedIn job ID: {str(e)}")
            return None
        
    def _scrape_generic(self, driver: webdriver.Chrome, url: str) -> Dict[str, Any]:
        try:
            logging.info(f"Scraping generic URL: {url}")
            driver.get(url)
            
            try:
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logging.info("Page body loaded")
            except TimeoutException:
                logging.error("Timeout waiting for page body to load")
                raise

            # Handle iframes
            iframe_src = self._find_iframe_src(driver, url)
            if iframe_src:
                logging.info(f"Found iframe source: {iframe_src}")
                driver.get(iframe_src)
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Extract content
            title = soup.title.string.strip() if soup.title else "N/A"
            paragraphs = [p.get_text(strip=True) for p in soup.find_all('p')]
            job_descriptions = [div.get_text(strip=True) for div in soup.find_all(['div', 'section', 'article'])
                              if len(div.get_text(strip=True)) > 300]
            job_requirements = [ul.get_text(strip=True) for ul in soup.find_all(['ul', 'ol'])]
            
            result = {
                "source": "Generic",
                "title": title,
                "paragraphs": paragraphs,
                "job_descriptions": job_descriptions,
                "job_requirements": job_requirements,
                "full_text": soup.get_text("\n", strip=True)
            }
            
            logging.info("Successfully extracted content from generic URL")
            return result
            
        except Exception as e:
            logging.error(f"Error in generic scraping: {str(e)}")
            raise

    def _find_iframe_src(self, driver: webdriver.Chrome, url: str) -> Optional[str]:
        try:
            iframe_elements = driver.find_elements(By.TAG_NAME, "iframe")
            for iframe in iframe_elements:
                src = iframe.get_attribute("src")
                if src and any(x in src for x in ["greenhouse", "ashbyhq", "lever"]):
                    logging.info(f"Found job board iframe: {src}")
                    return src

            parsed_url = urlparse(url)
            query = parse_qs(parsed_url.query)
            ashby_jid = query.get("ashby_jid", [None])[0]
            if ashby_jid:
                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                iframe_src = f"{base_url}/jobs/{ashby_jid}"
                logging.info(f"Found Ashby job iframe: {iframe_src}")
                return iframe_src

            if "fa.oraclecloud.com" in parsed_url.netloc:
                logging.info("Found Oracle Cloud iframe")
                return url

            return None
        except Exception as e:
            logging.error(f"Error finding iframe source: {str(e)}")
            return None
        
    async def scrape_job_description(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape job description from various sources."""
        try:
            logging.info(f"Starting job description scraping for URL: {url}")
            
            # Check cache first
            cached = self._get_cached_response(url)
            if cached:
                logging.info(f"Returning cached response for URL: {url}")
                return cached

            driver = self._setup_driver()
            try:
                if "linkedin.com" in url:
                    logging.info(f"Detected LinkedIn URL: {url}")
                    result = self._scrape_linkedin(driver, url)
                elif "docs.google.com" in url:
                    logging.info(f"Detected Google Doc URL: {url}")
                    result = self._extract_google_doc_content(url)
                else:
                    logging.info(f"Detected generic URL: {url}")
                    result = self._scrape_generic(driver, url)
                    
                if result:
                    logging.info("Successfully scraped job description")
                    self._cache_response(url, result)
                    return result
                else:
                    logging.warning(f"No content found for URL: {url}")
                    return None
                    
            except Exception as e:
                logging.error(f"Error during scraping: {str(e)}")
                raise
            finally:
                try:
                    driver.quit()
                    logging.info("WebDriver quit successfully")
                except Exception as e:
                    logging.warning(f"Error quitting driver: {str(e)}")
                    
        except Exception as e:
            logging.error(f"Error in scrape_job_description: {str(e)}")
            raise 