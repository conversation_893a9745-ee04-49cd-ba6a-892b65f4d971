from typing import Optional, Dict, Any
import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver import Remote, ChromeOptions
from selenium.webdriver.chromium.remote_connection import ChromiumRemoteConnection
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
import json
import os
from app.logger import logging
import time
import re
import hashlib
from urllib.parse import urlparse, parse_qs
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
import unicodedata
import undetected_chromedriver as uc


"""
ScrapingService
├── scrape_job_description(url)
│   ├── Check cache
│   ├── Detect URL type: LinkedIn / Google Docs / Generic
│   ├── Dispatch scraping logic
│   └── Cache + Return
├── _setup_driver()
├── _extract_google_doc_content()
├── _scrape_linkedin()
│   └── _scrape_linkedin_direct()
├── _scrape_generic()
│   └── _find_iframe_src()
├── _cache_response()
├── _get_cached_response()
└── _url_hash()
"""

class ScrapingService:
    def __init__(self):
        self.cache_dir = "scrape_cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        
    def _url_hash(self, url: str) -> str:
        return hashlib.md5(url.encode()).hexdigest()
        
    def _cache_response(self, url: str, data: Dict[str, Any]):
        try:
            # Check if data is valid
            title = data.get("title", "").strip()
            description = data.get("description", "").strip()

            if not title or title == "N/A" or not description or description == "N/A":
                logging.warning(f"Skipping cache for URL (missing title/description): {url}")
                return

            file_path = os.path.join(self.cache_dir, self._url_hash(url) + ".json")
            with open(file_path, 'w') as f:
                json.dump(data, f)
            logging.info(f"Cached response for URL: {url}")

        except Exception as e:
            logging.error(f"Error caching response: {str(e)}")
            
    def _get_cached_response(self, url: str) -> Optional[Dict[str, Any]]:
        try:
            file_path = os.path.join(self.cache_dir, self._url_hash(url) + ".json")
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logging.error(f"Error reading cached response: {str(e)}")
            return None
    def _setup_local_driver(self) -> webdriver.Chrome:
        try:
            logging.info("Setting up local undetected Chrome WebDriver...")
            options = uc.ChromeOptions()
            options.add_argument("--headless=new")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-gpu")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_argument("--window-size=1280,800")

            driver = uc.Chrome(options=options, use_subprocess=True)
            logging.info("Local undetected Chrome WebDriver connected.")
            return driver
        except Exception as e:
            logging.error(f"Error setting up local driver: {str(e)}")
            raise
        
    def _setup_bright_data_driver(self) -> webdriver.Remote:
        try:
            logging.info("Setting up Bright Data remote Selenium driver...")
            scraper_proxy_auth = os.getenv('SCRAPER_PROXY_AUTH')
            sbr_url = f"https://{scraper_proxy_auth}@brd.superproxy.io:9515"
            connection = ChromiumRemoteConnection(sbr_url, "goog", "chrome")

            chrome_options = ChromeOptions()
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--headless=new")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-gpu")

            driver = Remote(connection, options=chrome_options)
            logging.info("Bright Data WebDriver connected.")
            return driver

        except Exception as e:
            logging.error(f"Error setting up Bright Data remote driver: {str(e)}")
            raise

    def _extract_google_doc_content(self, doc_url: str) -> Optional[Dict[str, str]]:
        try:
            logging.info(f"Extracting content from Google Doc: {doc_url}")
            if "/edit" in doc_url:
                doc_url = doc_url.split("/edit")[0] + "/export?format=txt"
            
            response = requests.get(doc_url)
            if response.status_code == 200:
                return {
                    "source": "Google Docs",
                    "full_text": response.text.strip()
                }
            logging.warning(f"Failed to fetch Google Doc content. Status code: {response.status_code}")
            return None
        except Exception as e:
            logging.error(f"Error extracting Google Doc content: {str(e)}")
            return None
            
    def _scrape_linkedin(self, driver: webdriver.Chrome, url: str) -> Dict[str, str]:
        try:
            logging.info(f"Scraping LinkedIn URL: {url}")
            job_id = self._extract_linkedin_job_id(url)
            if not job_id:
                logging.warning(f"Could not extract job ID from URL: {url}")
                return self._scrape_linkedin_direct(driver, url)

            # Try RapidAP Or Other APIs first
            rapidapi_key = os.getenv("RAPIDAPI_KEY")
            if rapidapi_key:
                try:
                    logging.info("Attempting to fetch job details via RapidAPI")
                    response = requests.get(
                        "https://linkedin-data-scraper-api1.p.rapidapi.com/jobs/detail",
                        headers={
                            "x-rapidapi-key": rapidapi_key,
                            "x-rapidapi-host": "linkedin-data-scraper-api1.p.rapidapi.com"
                        },
                        params={"job_id": job_id}
                    )
                    if response.status_code == 200:
                        return response.json()
                    logging.warning(f"RapidAPI request failed with status code: {response.status_code}")
                except Exception as e:
                    logging.warning(f"LinkedIn API failed: {str(e)}")

            # Fallback to direct scraping
            new_url = f"https://www.linkedin.com/jobs/view/{job_id}"
            logging.info("Falling back to direct LinkedIn scraping")
            return self._scrape_linkedin_direct(driver, new_url)
            
        except Exception as e:
            logging.error(f"Error in LinkedIn scraping: {str(e)}")
            raise

    def _scrape_linkedin_direct(
        self,
        driver: webdriver.Chrome,
        url: str,
        fallback_used: bool = False
    ) -> Dict[str, str]:
        try:
            logging.info(f"Directly scraping LinkedIn URL: {url}")
            driver.get(url)
            
            # Wait for page load
            try:
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logging.info("Page body loaded")
            except TimeoutException:
                logging.error("Timeout waiting for page body to load")
                raise
            
            # Wait for job details to load
            time.sleep(2)  # Give JavaScript time to load content
            
            # Extract job details
            try:
                soup = BeautifulSoup(driver.page_source, "html.parser")
                
                title = soup.find("h1")
                location = soup.find("span", class_="topcard__flavor--bullet")
                description = soup.find("div", class_="description__text")

                if not location:
                    logging.warning("Could not find location element")
                    location = soup.find("div", class_="job-details-jobs-unified-top-card__primary-description-container")
                
                if not description:
                    logging.warning("Could not find job description element")
                    description = soup.find("div", class_="show-more-less-html__markup")
                
                if not description:
                    logging.warning("Could not find any description content")
                    description = soup.find("div", class_="job-description")
                
                if not description:
                    logging.warning("Could not find any description content")
                    description = soup.find("div", class_="jobs-description__container")
                
                # Clean and normalize text
                def clean_text(text):
                    if not text:
                        return "N/A"
                    # Normalize Unicode characters
                    text = unicodedata.normalize("NFKD", text)
                    # Remove any remaining non-ASCII characters
                    text = text.encode("ascii", "ignore").decode("ascii")
                    return text.strip()

                # Check if we're redirected to login page
                if not description and not location and self._is_linkedin_login_page(driver):
                    logging.warning("LinkedIn login page detected")
                    if fallback_used:
                        logging.error("Already used Bright Data fallback, aborting to avoid infinite loop")
                        raise Exception("LinkedIn login required - proxy fallback failed")

                    logging.info("Attempting Bright Data fallback now...")
                    try:
                        driver.quit()
                    except Exception:
                        pass
                    # Retry with Bright Data
                    proxy_driver = self._setup_bright_data_driver()
                    return self._scrape_linkedin_direct(proxy_driver, url, fallback_used=True)
                
                result = {
                    "source": "LinkedIn Scrape",
                    "title": clean_text(title.get_text(strip=True) if title else None),
                    "location": clean_text(location.get_text(strip=True) if location else None),
                    "company": "N/A",  # LinkedIn often requires login for company info
                    "description": clean_text(description.get_text("\n", strip=True) if description else None),
                    "full_text": clean_text(soup.get_text("\n", strip=True)),
                    "job_requirements": []  # LinkedIn often requires login for detailed requirements
                }
                
                logging.info("Successfully extracted job details")
                return result
                
            except NoSuchElementException as e:
                logging.error(f"Could not find required element: {str(e)}")
                raise
                
        except TimeoutException:
            logging.error("Timeout waiting for LinkedIn page to load")
            raise
        except WebDriverException as e:
            logging.error(f"WebDriver error: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Error scraping LinkedIn directly: {str(e)}")
            raise
        
    def _extract_linkedin_job_id(self, url: str) -> Optional[str]:
        try:
            parsed = urlparse(url)
            query = parse_qs(parsed.query)
            job_id = query.get("currentJobId", [None])[0]

            # Fallback: parse from path if query param is not found
            if not job_id:
                match = re.search(r"/jobs/view/(\d+)", parsed.path)
                if match:
                    job_id = match.group(1)

            if job_id:
                logging.info(f"Extracted job ID: {job_id}")
            else:
                logging.warning("Could not extract job ID from URL")

            return job_id
        except Exception as e:
            logging.error(f"Error extracting LinkedIn job ID: {str(e)}")
            return None
        
    def _scrape_generic(self, driver: webdriver.Chrome, url: str) -> Dict[str, Any]:
        try:
            logging.info(f"Scraping generic URL: {url}")
            driver.get(url)

            # Bypass Cloudflare nếu có
            self._wait_cloudflare_bypass(driver)

            # Lazy load toàn bộ nội dung
            self._scroll_to_bottom(driver)

            # Chờ DOM ổn định
            self._wait_until_dom_stable(driver)

            # Iframe detection
            iframe_src = self._find_iframe_src(driver, url)
            if iframe_src:
                logging.info(f"Switching to iframe source: {iframe_src}")
                driver.get(iframe_src)
                self._wait_cloudflare_bypass(driver)
                self._scroll_to_bottom(driver)
                self._wait_until_dom_stable(driver)

            html = driver.page_source
            soup = BeautifulSoup(html, 'html.parser')

            title = soup.title.string.strip() if soup.title else "N/A"
            longest_block = self._extract_longest_text_block(html)

            result = {
                "source": "Generic",
                "title": title,
                "description": longest_block,
                "full_text": soup.get_text("\n", strip=True)
            }

            logging.info("Successfully extracted content from generic URL")
            return result
        except Exception as e:
            logging.error(f"Error in generic scraping: {str(e)}")
            # save for debug later
            try:
                with open("debug_failed_page.html", "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
            except Exception as save_err:
                logging.warning(f"Failed to save debug HTML: {save_err}")

            raise


    def _find_iframe_src(self, driver: webdriver.Chrome, url: str) -> Optional[str]:
        try:
            iframe_elements = driver.find_elements(By.TAG_NAME, "iframe")
            for iframe in iframe_elements:
                src = iframe.get_attribute("src")
                if src and any(x in src for x in ["greenhouse", "ashbyhq", "lever"]):
                    logging.info(f"Found job board iframe: {src}")
                    return src

            parsed_url = urlparse(url)
            query = parse_qs(parsed_url.query)
            ashby_jid = query.get("ashby_jid", [None])[0]
            if ashby_jid:
                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                iframe_src = f"{base_url}/jobs/{ashby_jid}"
                logging.info(f"Found Ashby job iframe: {iframe_src}")
                return iframe_src

            if "fa.oraclecloud.com" in parsed_url.netloc:
                logging.info("Found Oracle Cloud iframe")
                return url

            return None
        except Exception as e:
            logging.error(f"Error finding iframe source: {str(e)}")
            return None
        
    def _is_driver_alive(self, driver):
        try:
            # A simple command to check if the session is still valid
            driver.title
            return True
        except Exception:
            return False
    
    def _scrape_by_url(self, driver, url: str) -> Optional[Dict[str, Any]]:
        if "linkedin.com" in url:
            return self._scrape_linkedin(driver, url)
        elif "docs.google.com" in url:
            return self._extract_google_doc_content(url)
        else:
            return self._scrape_generic(driver, url)

    async def scrape_job_description(self, url: str) -> Optional[Dict[str, Any]]:
        try:
            logging.info(f"Starting job description scraping for URL: {url}")
            
            # Check cache first
            cached = self._get_cached_response(url)
            if cached:
                logging.info(f"Returning cached response for URL: {url}")
                return cached

            try:
                driver = self._setup_local_driver() # use local driver first
                use_bright_data = False
                if not self._is_driver_alive(driver):
                    logging.warning("Local WebDriver is not alive after setup. Retrying with Bright Data...")
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = self._setup_bright_data_driver()
                    use_bright_data = True
            except Exception as e:
                logging.warning(f"Failed to setup local driver: {str(e)}. Falling back to Bright Data.")
                driver = self._setup_bright_data_driver()
                use_bright_data = True

            try:
                result = self._scrape_by_url(driver, url)
                if self._should_fallback_to_proxy(result, driver) and not use_bright_data:
                    logging.warning("Local scraping failed or blocked. Retrying with Bright Data...")
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = self._setup_bright_data_driver()
                    result = self._scrape_by_url(driver, url)

                if result:
                    logging.info("Successfully scraped job description")
                    self._cache_response(url, result)
                    return result
                else:
                    logging.warning(f"No content found for URL: {url}")
                    return None

            except WebDriverException as e:
                logging.error(f"WebDriverException: {str(e)}")
                if (not self._is_driver_alive(driver) or "no such window" in str(e)):
                    logging.info("WebDriver crashed, retrying with a new driver...")
                else:
                    raise
            except Exception as e:
                logging.error(f"Error during scraping: {str(e)}")
                # Check if it's a LinkedIn login requirement
                if self._should_fallback_to_proxy(None, driver, e) and not use_bright_data:
                    logging.info("LinkedIn login detected, switching to Bright Data proxy...")
                    try:
                        driver.quit()
                    except:
                        pass
                    driver = self._setup_bright_data_driver()
                    use_bright_data = True
                    
                    # Retry with Bright Data
                    result = self._scrape_by_url(driver, url)
                    if result:
                        logging.info("Successfully scraped with Bright Data proxy")
                        self._cache_response(url, result)
                        return result
                else:
                    raise
            finally:
                try:
                    if self._is_driver_alive(driver):
                        driver.quit()
                    logging.info("WebDriver quit successfully")
                except Exception as e:
                    logging.warning(f"Error quitting driver: {str(e)}")
            return None

        except Exception as e:
            logging.error(f"Error in scrape_job_description: {str(e)}")
            raise

    def _should_fallback_to_proxy(self, result, driver, exception: Optional[Exception] = None) -> bool:
        if result is None:
            return True
        if self._is_cloudflare_challenge(driver):
            return True
        if exception and "LinkedIn login required" in str(exception):
            return True
        return False

    def _scroll_to_bottom(self, driver, pause_time=1.0, max_tries=10):
        last_height = driver.execute_script("return document.body.scrollHeight")
        tries = 0
        while tries < max_tries:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(pause_time)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
            tries += 1

    def _wait_until_dom_stable(self, driver, timeout=15, interval=0.5):
        old_dom = ""
        stable_count = 0
        max_stable_count = 3
        start_time = time.time()

        while time.time() - start_time < timeout:
            new_dom = driver.page_source
            if new_dom == old_dom:
                stable_count += 1
                if stable_count >= max_stable_count:
                    return True
            else:
                stable_count = 0
                old_dom = new_dom
            time.sleep(interval)
        logging.warning("DOM did not stabilize in time")
        return False

    def _extract_longest_text_block(self, html: str) -> str:
        soup = BeautifulSoup(html, "html.parser")
        candidates = soup.find_all(["div", "section", "article"])
        longest = max(candidates, key=lambda tag: len(tag.get_text(strip=True)), default=None)
        return longest.get_text(strip=True) if longest else ""
    
    def _is_cloudflare_challenge(self, driver) -> bool:
        try:
            page_source = driver.page_source.lower()
            title = driver.title.lower()
            url = driver.current_url.lower()

            # Check if URL contains known Cloudflare challenge path
            if "/cdn-cgi/challenge-platform" in url:
                return True

            # Check for exact titles that typically appear on Cloudflare challenge pages
            if title.strip() in ["just a moment...", "attention required! | cloudflare"]:
                return True

            # Check for known Cloudflare-specific elements in the HTML
            if 'id="challenge-form"' in page_source or 'class="cf-browser-verification"' in page_source:
                return True

            # Use regex to detect elements with IDs or classes starting with "cf-"
            # if re.search(r'id="cf-[^"]+"', page_source) or re.search(r'class="[^"]*cf-[^"]*"', page_source):
            #     return True

            # No Cloudflare challenge indicators found
            return False

        except Exception as e:
            logging.error(f"Error checking for Cloudflare challenge: {str(e)}")
            return False

    def _is_linkedin_login_page(self, driver) -> bool:
        try:
            soup = BeautifulSoup(driver.page_source, "html.parser")
            h1 = soup.find("h1")
            if h1 and h1.get_text(strip=True).lower() == "sign in":
                logging.info("Detected LinkedIn login page via <h1> == 'Sign in'")
                return True
            return False
        except Exception as e:
            logging.error(f"Error checking for LinkedIn login page: {str(e)}")
            return False



    
    def _wait_cloudflare_bypass(self, driver, timeout=10):
        logging.info("Waiting for Cloudflare JS challenge (if any)")
        for _ in range(timeout):

            if self._is_cloudflare_challenge(driver):
                logging.info("Cloudflare challenge detected, waiting...")
            else:
                logging.info("Cloudflare challenge bypassed")
                return True

            time.sleep(1)

        logging.warning("Cloudflare challenge may still be active")
        return False
