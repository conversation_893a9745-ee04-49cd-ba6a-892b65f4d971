from typing import Optional, Dict, Any
import requests
from bs4 import BeautifulSoup
from seleniumwire import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import json
import os
from app.logger import logging
import time
import re
import hashlib
from urllib.parse import urlparse, parse_qs
from selenium.common.exceptions import TimeoutException, WebDriverException, NoSuchElementException
import unicodedata
import undetected_chromedriver as uc


"""
ScrapingService
├── scrape_job_description(url)
│   ├── Check cache
│   ├── Detect URL type: LinkedIn / Google Docs / Generic
│   ├── Dispatch scraping logic
│   └── Cache + Return
├── _setup_driver()
├── _extract_google_doc_content()
├── _scrape_linkedin()
│   └── _scrape_linkedin_direct()
├── _scrape_generic()
│   └── _find_iframe_src()
├── _cache_response()
├── _get_cached_response()
└── _url_hash()
"""

class ScrapingService:
    def __init__(self):
        self.cache_dir = "scrape_cache"
        os.makedirs(self.cache_dir, exist_ok=True)
        
    def _url_hash(self, url: str) -> str:
        return hashlib.md5(url.encode()).hexdigest()
        
    def _cache_response(self, url: str, data: Dict[str, Any]):
        try:
            file_path = os.path.join(self.cache_dir, self._url_hash(url) + ".json")
            with open(file_path, 'w') as f:
                json.dump(data, f)
            logging.info(f"Cached response for URL: {url}")
        except Exception as e:
            logging.error(f"Error caching response: {str(e)}")
            
    def _get_cached_response(self, url: str) -> Optional[Dict[str, Any]]:
        try:
            file_path = os.path.join(self.cache_dir, self._url_hash(url) + ".json")
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    return json.load(f)
            return None
        except Exception as e:
            logging.error(f"Error reading cached response: {str(e)}")
            return None
        
    def _setup_driver(self) -> webdriver.Chrome:
        try:
            logging.info("Setting up undetected Chrome WebDriver with proxy...")

            # Proxy credentials from environment variable
            proxy_user = os.getenv("SCRAPER_PROXY_USER")
            proxy_pass = os.getenv("SCRAPER_PROXY_PASS")
            proxy_host = os.getenv("SCRAPER_PROXY_HOST", "brd.superproxy.io")
            proxy_port = int(os.getenv("SCRAPER_PROXY_PORT", 9222))

            proxy_url = f"{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
            logging.info(f"Using proxy: {proxy_url}")

            # Cấu hình proxy cho selenium-wire
            seleniumwire_options = {
                'proxy': {
                    'http': f"http://{proxy_url}",
                    'https': f"https://{proxy_url}",
                    'no_proxy': 'localhost,127.0.0.1'
                }
            }

            chrome_options = uc.ChromeOptions()
            chrome_options.add_argument("--headless=new")  # với Chrome >= 115
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--window-size=1280,800")
            chrome_options.add_argument(
                "user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
                "AppleWebKit/537.36 (KHTML, like Gecko) "
                "Chrome/120.0.0.0 Safari/537.36"
            )

            # Tạo driver bằng cách kết hợp undetected_chromedriver và selenium-wire
            driver = uc.Chrome(
                options=chrome_options,
                seleniumwire_options=seleniumwire_options,
                use_subprocess=True
            )

            logging.info("Driver setup complete with proxy and stealth")
            return driver

        except Exception as e:
            logging.error(f"Error setting up Chrome driver with proxy: {str(e)}")
            raise


        except Exception as e:
            logging.error(f"Error setting up undetected Chrome driver with proxy: {str(e)}")
            raise


            
    def _extract_google_doc_content(self, doc_url: str) -> Optional[Dict[str, str]]:
        try:
            logging.info(f"Extracting content from Google Doc: {doc_url}")
            if "/edit" in doc_url:
                doc_url = doc_url.split("/edit")[0] + "/export?format=txt"
            
            response = requests.get(doc_url)
            if response.status_code == 200:
                return {
                    "source": "Google Docs",
                    "full_text": response.text.strip()
                }
            logging.warning(f"Failed to fetch Google Doc content. Status code: {response.status_code}")
            return None
        except Exception as e:
            logging.error(f"Error extracting Google Doc content: {str(e)}")
            return None
            
    def _scrape_linkedin(self, driver: webdriver.Chrome, url: str) -> Dict[str, str]:
        try:
            logging.info(f"Scraping LinkedIn URL: {url}")
            job_id = self._extract_linkedin_job_id(url)
            if not job_id:
                logging.warning(f"Could not extract job ID from URL: {url}")
                return self._scrape_linkedin_direct(driver, url)

            # Try RapidAPI first
            # rapidapi_key = os.getenv("RAPIDAPI_KEY")
            # if rapidapi_key:
            #     try:
            #         logging.info("Attempting to fetch job details via RapidAPI")
            #         response = requests.get(
            #             "https://linkedin-data-scraper-api1.p.rapidapi.com/jobs/detail",
            #             headers={
            #                 "x-rapidapi-key": rapidapi_key,
            #                 "x-rapidapi-host": "linkedin-data-scraper-api1.p.rapidapi.com"
            #             },
            #             params={"job_id": job_id}
            #         )
            #         if response.status_code == 200:
            #             return response.json()
            #         logging.warning(f"RapidAPI request failed with status code: {response.status_code}")
            #     except Exception as e:
            #         logging.warning(f"LinkedIn API failed: {str(e)}")

            # Fallback to direct scraping
            logging.info("Falling back to direct LinkedIn scraping")
            return self._scrape_linkedin_direct(driver, url)
            
        except Exception as e:
            logging.error(f"Error in LinkedIn scraping: {str(e)}")
            raise

    def _scrape_linkedin_direct(self, driver: webdriver.Chrome, url: str) -> Dict[str, str]:
        try:
            logging.info(f"Directly scraping LinkedIn URL: {url}")
            driver.get(url)
            
            # Wait for page load
            try:
                WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                logging.info("Page body loaded")
            except TimeoutException:
                logging.error("Timeout waiting for page body to load")
                raise
            
            # Wait for job details to load
            time.sleep(2)  # Give JavaScript time to load content
            
            # Extract job details
            try:
                soup = BeautifulSoup(driver.page_source, "html.parser")
                
                title = soup.find("h1")
                location = soup.find("span", class_="topcard__flavor--bullet")
                description = soup.find("div", class_="description__text")
                
                if not description:
                    logging.warning("Could not find job description element")
                    description = soup.find("div", class_="show-more-less-html__markup")
                
                if not description:
                    logging.warning("Could not find any description content")
                    description = soup.find("div", class_="job-description")
                
                # Clean and normalize text
                def clean_text(text):
                    if not text:
                        return "N/A"
                    # Normalize Unicode characters
                    text = unicodedata.normalize("NFKD", text)
                    # Remove any remaining non-ASCII characters
                    text = text.encode("ascii", "ignore").decode("ascii")
                    return text.strip()
                
                result = {
                    "source": "LinkedIn Scrape",
                    "title": clean_text(title.get_text(strip=True) if title else None),
                    "location": clean_text(location.get_text(strip=True) if location else None),
                    "company": "N/A",  # LinkedIn often requires login for company info
                    "description": clean_text(description.get_text("\n", strip=True) if description else None),
                    "full_text": clean_text(soup.get_text("\n", strip=True)),
                    "job_requirements": []  # LinkedIn often requires login for detailed requirements
                }
                
                logging.info("Successfully extracted job details")
                return result
                
            except NoSuchElementException as e:
                logging.error(f"Could not find required element: {str(e)}")
                raise
                
        except TimeoutException:
            logging.error("Timeout waiting for LinkedIn page to load")
            raise
        except WebDriverException as e:
            logging.error(f"WebDriver error: {str(e)}")
            raise
        except Exception as e:
            logging.error(f"Error scraping LinkedIn directly: {str(e)}")
            raise
        
    def _extract_linkedin_job_id(self, url: str) -> Optional[str]:
        try:
            parsed = urlparse(url)
            query = parse_qs(parsed.query)
            job_id = query.get("currentJobId", [None])[0]

            # Fallback: parse from path if query param is not found
            if not job_id:
                match = re.search(r"/jobs/view/(\d+)", parsed.path)
                if match:
                    job_id = match.group(1)

            if job_id:
                logging.info(f"Extracted job ID: {job_id}")
            else:
                logging.warning("Could not extract job ID from URL")

            return job_id
        except Exception as e:
            logging.error(f"Error extracting LinkedIn job ID: {str(e)}")
            return None
        
    def _scrape_generic(self, driver: webdriver.Chrome, url: str) -> Dict[str, Any]:
        try:
            logging.info(f"Scraping generic URL: {url}")
            driver.get(url)

            # Bypass Cloudflare nếu có
            self._wait_cloudflare_bypass(driver)

            # Lazy load toàn bộ nội dung
            self._scroll_to_bottom(driver)

            # Chờ DOM ổn định
            self._wait_until_dom_stable(driver)

            # Iframe detection
            iframe_src = self._find_iframe_src(driver, url)
            if iframe_src:
                logging.info(f"Switching to iframe source: {iframe_src}")
                driver.get(iframe_src)
                self._wait_cloudflare_bypass(driver)
                self._scroll_to_bottom(driver)
                self._wait_until_dom_stable(driver)

            html = driver.page_source
            soup = BeautifulSoup(html, 'html.parser')

            title = soup.title.string.strip() if soup.title else "N/A"
            longest_block = self._extract_longest_text_block(html)

            result = {
                "source": "Generic",
                "title": title,
                "description": longest_block,
                "full_text": soup.get_text("\n", strip=True)
            }

            logging.info("Successfully extracted content from generic URL")
            return result

        except Exception as e:
            logging.error(f"Error in generic scraping: {str(e)}")

            # Lưu lại để debug sau
            try:
                with open("debug_failed_page.html", "w", encoding="utf-8") as f:
                    f.write(driver.page_source)
            except Exception as save_err:
                logging.warning(f"Failed to save debug HTML: {save_err}")

            raise


    def _find_iframe_src(self, driver: webdriver.Chrome, url: str) -> Optional[str]:
        try:
            iframe_elements = driver.find_elements(By.TAG_NAME, "iframe")
            for iframe in iframe_elements:
                src = iframe.get_attribute("src")
                if src and any(x in src for x in ["greenhouse", "ashbyhq", "lever"]):
                    logging.info(f"Found job board iframe: {src}")
                    return src

            parsed_url = urlparse(url)
            query = parse_qs(parsed_url.query)
            ashby_jid = query.get("ashby_jid", [None])[0]
            if ashby_jid:
                base_url = f"{parsed_url.scheme}://{parsed_url.netloc}"
                iframe_src = f"{base_url}/jobs/{ashby_jid}"
                logging.info(f"Found Ashby job iframe: {iframe_src}")
                return iframe_src

            if "fa.oraclecloud.com" in parsed_url.netloc:
                logging.info("Found Oracle Cloud iframe")
                return url

            return None
        except Exception as e:
            logging.error(f"Error finding iframe source: {str(e)}")
            return None
        
    async def scrape_job_description(self, url: str) -> Optional[Dict[str, Any]]:
        """Scrape job description from various sources."""
        try:
            logging.info(f"Starting job description scraping for URL: {url}")
            
            # Check cache first
            # cached = self._get_cached_response(url)
            # if cached:
            #     logging.info(f"Returning cached response for URL: {url}")
            #     return cached

            driver = self._setup_driver()
            try:
                if "linkedin.com" in url:
                    logging.info(f"Detected LinkedIn URL: {url}")
                    result = self._scrape_linkedin(driver, url)
                elif "docs.google.com" in url:
                    logging.info(f"Detected Google Doc URL: {url}")
                    result = self._extract_google_doc_content(url)
                else:
                    logging.info(f"Detected generic URL: {url}")
                    result = self._scrape_generic(driver, url)
                    
                if result:
                    logging.info("Successfully scraped job description")
                    self._cache_response(url, result)
                    return result
                else:
                    logging.warning(f"No content found for URL: {url}")
                    return None
                    
            except Exception as e:
                logging.error(f"Error during scraping: {str(e)}")
                raise
            finally:
                try:
                    driver.quit()
                    logging.info("WebDriver quit successfully")
                except Exception as e:
                    logging.warning(f"Error quitting driver: {str(e)}")
                    
        except Exception as e:
            logging.error(f"Error in scrape_job_description: {str(e)}")
            raise

    def _scroll_to_bottom(self, driver, pause_time=1.0, max_tries=10):
        last_height = driver.execute_script("return document.body.scrollHeight")
        tries = 0
        while tries < max_tries:
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(pause_time)
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height
            tries += 1

    def _wait_until_dom_stable(self, driver, timeout=15, interval=0.5):
        old_dom = ""
        stable_count = 0
        max_stable_count = 3
        start_time = time.time()

        while time.time() - start_time < timeout:
            new_dom = driver.page_source
            if new_dom == old_dom:
                stable_count += 1
                if stable_count >= max_stable_count:
                    return True
            else:
                stable_count = 0
                old_dom = new_dom
            time.sleep(interval)
        logging.warning("DOM did not stabilize in time")
        return False

    def _extract_longest_text_block(self, html: str) -> str:
        soup = BeautifulSoup(html, "html.parser")
        candidates = soup.find_all(["div", "section", "article"])
        longest = max(candidates, key=lambda tag: len(tag.get_text(strip=True)), default=None)
        return longest.get_text(strip=True) if longest else ""
    
    def _is_cloudflare_challenge(self, driver) -> bool:
        try:
            page_source = driver.page_source.lower()
            title = driver.title.lower()

            challenge_indicators = [
                "cloudflare", "attention required", "checking your browser",
                "just a moment", "verify you are human", "/cdn-cgi/challenge-platform"
            ]

            return any(keyword in page_source or keyword in title for keyword in challenge_indicators)
        except Exception as e:
            logging.error(f"Error checking for Cloudflare challenge: {str(e)}")
            return False

    
    def _wait_cloudflare_bypass(self, driver, timeout=20):
        logging.info("Waiting for Cloudflare JS challenge (if any)")
        for _ in range(timeout):

            if self._is_cloudflare_challenge(driver):
                logging.info("Cloudflare challenge detected, waiting...")
            else:
                logging.info("Cloudflare challenge bypassed")
                return True

            time.sleep(1)

        logging.warning("Cloudflare challenge may still be active")
        return False

