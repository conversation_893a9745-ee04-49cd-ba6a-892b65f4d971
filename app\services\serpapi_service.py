import os
import json
import time
import random
import urllib.request
import urllib.parse
import ssl
import re
from typing import List, Dict, Optional
from app.logger import logging
from sqlalchemy.orm import Session
from app.db.db_models import SerpAPIResult
from uuid import UUID

class SerpAPIService:
    def __init__(self):
        # Configuration
        self.proxy = 'http://brd-customer-hl_17b98357-zone-dash_dev_serp_api:<EMAIL>:33335'
        self.sleep_range = (1, 2)  # seconds between requests
        self.retry_limit = 3
        
        # Setup opener with proxy
        self.opener = urllib.request.build_opener(
            urllib.request.ProxyHandler({'http': self.proxy, 'https': self.proxy}),
            urllib.request.HTTPSHandler(context=ssl._create_unverified_context())
        )
        self.opener.addheaders = [('User-Agent', 'Mozilla/5.0')]
        
        # Cache directory
        self.cache_dir = os.path.join(os.getcwd(), "serpapi_cache")
        os.makedirs(self.cache_dir, exist_ok=True)

    def _url_hash(self, url: str) -> str:
        """Generate a hash for the URL to use as cache key."""
        import hashlib
        return hashlib.md5(url.encode()).hexdigest()

    def _cache_response(self, url: str, response_data: Dict) -> None:
        """Cache the response for a given URL."""
        try:
            cache_key = self._url_hash(url)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            with open(cache_file, 'w') as f:
                json.dump(response_data, f)
            logging.info(f"Cached response for URL: {url}")
        except Exception as e:
            logging.error(f"Error caching response: {str(e)}")

    def _get_cached_response(self, url: str) -> Optional[Dict]:
        """Get cached response if available."""
        try:
            cache_key = self._url_hash(url)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"Error reading cached response: {str(e)}")
        return None

    def _clean_boolean_string(self, boolean_string: str) -> str:
        """
        Convert OpenAI-style boolean string (e.g., title:"Software Developer" AND location:"New York City")
        into a Google X-ray search query like:
        "Software Developer" "New York City" site:linkedin.com/in OR site:linkedin.com/pub
        """
        # Remove title:, location:, and AND/OR keywords
        cleaned = re.sub(r'(title:|location:)', '', boolean_string, flags=re.IGNORECASE)
        cleaned = re.sub(r'\b(AND|OR)\b', '', cleaned, flags=re.IGNORECASE)
        cleaned = cleaned.replace('"', '').strip()

        # Add quotes around each word group
        tokens = [f'"{token.strip()}"' for token in cleaned.split() if token.strip()]
        query = ' '.join(tokens)

        # Append LinkedIn site filter
        query += ' site:linkedin.com/in OR site:linkedin.com/pub'
        return query.strip()

    def _is_valid_linkedin_profile_url(self, url: str) -> bool:
        """
        Validate if a URL is a valid LinkedIn profile URL.
        
        Args:
            url (str): URL to validate
            
        Returns:
            bool: True if valid LinkedIn profile URL, False otherwise
        """
        try:
            # Parse the URL
            parsed = urllib.parse.urlparse(url)
            
            # Check if it's a LinkedIn domain
            if 'linkedin.com' not in parsed.netloc:
                return False
            
            # Check for valid LinkedIn profile paths
            valid_paths = ['/in/', '/pub/']
            if not any(path in parsed.path for path in valid_paths):
                return False
            
            # Filter out organization pages and other non-profile URLs
            invalid_patterns = [
                '/company/',
                '/school/',
                '/showcase/',
                '/pub/cc',  # Organization lookup pages
                '/pub/dir',  # Directory pages
                '?_ri_=',   # Tracking parameters that indicate non-profile pages
                '&_ei_=',   # More tracking parameters
                '&_di_='    # Additional tracking parameters
            ]
            
            for pattern in invalid_patterns:
                if pattern in url:
                    return False
            
            # Check if the path has a reasonable length (profile URLs should have meaningful identifiers)
            path_parts = parsed.path.strip('/').split('/')
            if len(path_parts) < 2:
                return False
            
            # Profile identifier should be at least 3 characters
            if len(path_parts[-1]) < 3:
                return False
            
            return True
            
        except Exception as e:
            logging.warning(f"Error validating LinkedIn URL {url}: {e}")
            return False

    def _build_enhanced_query(self, search_params: Dict) -> str:
        """
        Build an enhanced search query with proper Google X-ray syntax.
        """
        query_parts = []
        
        # Add job title with quotes
        job_title = search_params.get('job_title', '')
        if job_title:
            # Split job title into words and quote each part
            title_words = job_title.split()
            if len(title_words) > 1:
                query_parts.append(f'"{job_title}"')
            else:
                query_parts.append(f'"{job_title}"')
        
        # Add experience level
        experience_level = search_params.get('experience_level', '')
        if experience_level:
            query_parts.append(f'"{experience_level}"')
        
        # Add skills with OR operator
        skills = search_params.get('skills', [])
        if skills:
            skills_quoted = [f'"{skill}"' for skill in skills]
            skills_query = ' OR '.join(skills_quoted)
            query_parts.append(f'({skills_query})')
        
        # Add location with quotes
        location = search_params.get('location', '')
        if location:
            query_parts.append(f'"{location}"')
        
        # Add company with quotes
        company = search_params.get('company', '')
        if company:
            query_parts.append(f'"{company}"')
        
        # Add industry/domain if specified
        industry = search_params.get('industry', '')
        if industry:
            query_parts.append(f'"{industry}"')
        
        # Add education requirements
        education = search_params.get('education', '')
        if education:
            query_parts.append(f'"{education}"')
        
        # Construct the final query
        if query_parts:
            base_query = ' '.join(query_parts)
        else:
            base_query = search_params.get('query', '')
        
        # Add LinkedIn site filter
        linkedin_suffix = "site:linkedin.com/in OR site:linkedin.com/pub"
        if linkedin_suffix.lower() not in base_query.lower():
            final_query = f"{base_query} {linkedin_suffix}"
        else:
            final_query = base_query
        
        return final_query.strip()

    async def fetch_linkedin_profiles(self, query: str, max_pages: int = 1) -> List[str]:
        """
        Search Google via proxy and extract LinkedIn profile URLs.
        """
        linkedin_urls = set()
        linkedin_suffix = "site:linkedin.com/in OR site:linkedin.com/pub"
        
        # Ensure LinkedIn suffix is in the query
        if linkedin_suffix.lower() not in query.lower():
            query = f"{query.strip()} {linkedin_suffix}"

        if not query or not isinstance(query, str) or len(query.strip()) < 5:
            logging.warning(f"[fetch_linkedin_profiles] Skipping search: query is empty or too short: '{query}'")
            return []

        if not getattr(self, 'proxy', None):
            logging.warning("[fetch_linkedin_profiles] Proxy is not set or empty! Requests may fail.")

        logging.info(f"Fetching LinkedIn profiles for query: {query}")
        encoded_query = urllib.parse.quote_plus(query)

        for page in range(max_pages):
            start = page * 10
            url = f'https://www.google.com/search?q={encoded_query}&start={start}&brd_json=1'
            logging.info(f"[fetch_linkedin_profiles] Requesting URL: {url}")

            # Check cache first
            cached_data = self._get_cached_response(url)
            if cached_data:
                logging.info(f"Returning cached response for URL: {url}")
                results = cached_data.get('organic', [])
                for item in results:
                    link = item.get('link', '')
                    if self._is_valid_linkedin_profile_url(link):
                        linkedin_urls.add(link)
                    else:
                        logging.warning(f"Filtered out invalid LinkedIn URL from cache: {link}")
                continue

            # Fetch from API with retries
            for attempt in range(self.retry_limit):
                try:
                    response = self.opener.open(url, timeout=10).read().decode()
                    try:
                        data = json.loads(response)
                    except Exception as json_err:
                        logging.error(f"[fetch_linkedin_profiles] JSON decode error on URL: {url}\nRaw response (first 500 chars): {response[:500]}")
                        raise Exception(f"Failed to parse JSON from Google/SerpAPI response: {json_err}")

                    # Cache the response
                    self._cache_response(url, data)

                    results = data.get('organic', [])
                    for item in results:
                        link = item.get('link', '')
                        if self._is_valid_linkedin_profile_url(link):
                            linkedin_urls.add(link)
                        else:
                            logging.warning(f"Filtered out invalid LinkedIn URL: {link}")

                    logging.info(f"[Page {page+1}] ✅ {len(results)} results, {len(linkedin_urls)} total unique URLs")
                    break  # success
                except Exception as e:
                    logging.error(f"[fetch_linkedin_profiles] [Page {page+1}] ❌ Attempt {attempt+1} failed: {e}")
                    if attempt < self.retry_limit - 1:
                        time.sleep(2 ** attempt)  # exponential backoff
                    else:
                        logging.error(f"[fetch_linkedin_profiles] [Page {page+1}] ❌ Giving up after {self.retry_limit} attempts. Last error: {e}")

            # Sleep between pages
            if page < max_pages - 1:
                time.sleep(random.uniform(*self.sleep_range))

        return sorted(list(linkedin_urls))

    async def search_profiles(self, search_params: Dict) -> Dict:
        """
        Search for LinkedIn profiles based on search parameters.
        
        Parameters:
            search_params (Dict): Dictionary containing search parameters
                - query (str): The search query
                - max_pages (int, optional): Number of pages to search (default: 1)
                - location (str, optional): Location filter
                - company (str, optional): Company filter
                - title (str, optional): Job title filter
                - skills (List[str], optional): Skills filter
                
        Returns:
            Dict: Search results with LinkedIn URLs and metadata
        """
        try:
            query = search_params.get('query', '')
            max_pages = search_params.get('max_pages', 1)
            
            if not query:
                raise ValueError("Query parameter is required")

            # Build enhanced query with additional filters
            enhanced_query = query
            
            # Add location filter
            location = search_params.get('location')
            if location:
                enhanced_query += f' "{location}"'
            
            # Add company filter
            company = search_params.get('company')
            if company:
                enhanced_query += f' "{company}"'
            
            # Add title filter
            title = search_params.get('title')
            if title:
                enhanced_query += f' "{title}"'
            
            # Add skills filter
            skills = search_params.get('skills', [])
            if skills:
                skills_query = " OR ".join([f'"{skill}"' for skill in skills])
                enhanced_query += f' ({skills_query})'

            # Fetch LinkedIn profiles
            linkedin_urls = await self.fetch_linkedin_profiles(enhanced_query, max_pages)
            
            return {
                "query": enhanced_query,
                "total_results": len(linkedin_urls),
                "linkedin_urls": linkedin_urls,
                "search_params": search_params,
                "timestamp": time.time()
            }

        except Exception as e:
            logging.error(f"Error in search_profiles: {str(e)}")
            raise Exception(f"Search failed: {str(e)}")

    async def boolean_search_profiles(self, search_params: Dict) -> Dict:
        """
        Perform a boolean search for LinkedIn profiles with enhanced query building.
        
        Parameters:
            search_params (Dict): Dictionary containing search parameters
                - job_title (str): Job title to search for
                - skills (List[str], optional): Required skills
                - location (str, optional): Preferred location
                - experience_level (str, optional): Experience level
                - company (str, optional): Target company
                - industry (str, optional): Industry/domain
                - education (str, optional): Education requirements
                - max_pages (int, optional): Number of pages to search
                
        Returns:
            Dict: Search results with LinkedIn URLs and metadata
        """
        try:
            # Build enhanced query using the new method
            enhanced_query = self._build_enhanced_query(search_params)
            max_pages = search_params.get('max_pages', 1)
            
            logging.info(f"Enhanced boolean query: {enhanced_query}")
            
            # Fetch LinkedIn profiles
            linkedin_urls = await self.fetch_linkedin_profiles(enhanced_query, max_pages)
            
            return {
                "query": enhanced_query,
                "total_results": len(linkedin_urls),
                "linkedin_urls": linkedin_urls,
                "search_params": search_params,
                "timestamp": time.time()
            }

        except Exception as e:
            logging.error(f"Error in boolean_search_profiles: {str(e)}")
            raise Exception(f"Boolean search failed: {str(e)}")

    async def get_search_history(self) -> List[Dict]:
        """Get search history from cache."""
        try:
            history = []
            for filename in os.listdir(self.cache_dir):
                if filename.endswith('.json'):
                    cache_file = os.path.join(self.cache_dir, filename)
                    with open(cache_file, 'r') as f:
                        data = json.load(f)
                        history.append({
                            "cache_key": filename.replace('.json', ''),
                            "data": data,
                            "file_size": os.path.getsize(cache_file)
                        })
            return history
        except Exception as e:
            logging.error(f"Error getting search history: {str(e)}")
            return []

    async def clear_cache(self) -> Dict:
        """Clear all cached responses."""
        try:
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.json')]
            for filename in cache_files:
                os.remove(os.path.join(self.cache_dir, filename))
            
            logging.info(f"Cleared {len(cache_files)} cached files")
            return {
                "message": f"Cleared {len(cache_files)} cached files",
                "cleared_count": len(cache_files)
            }
        except Exception as e:
            logging.error(f"Error clearing cache: {str(e)}")
            raise Exception(f"Failed to clear cache: {str(e)}")

    def get_existing_urls_for_job(self, db: Session, job_id: UUID) -> set:
        """
        Get all existing LinkedIn URLs for a specific job_id.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            
        Returns:
            set: Set of existing LinkedIn URLs for the job
        """
        try:
            existing_urls = set(
                r[0] for r in db.query(SerpAPIResult.linkedin_url)
                .filter(SerpAPIResult.job_id == job_id).all()
            )
            logging.info(f"Found {len(existing_urls)} existing URLs for job_id {job_id}")
            return existing_urls
        except Exception as e:
            logging.error(f"Error getting existing URLs for job_id {job_id}: {str(e)}")
            return set()

    def get_url_statistics(self, db: Session, job_id: UUID) -> Dict:
        """
        Get statistics about URLs for a specific job_id.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            
        Returns:
            Dict: Statistics about URLs for the job
        """
        try:
            from sqlalchemy import func
            
            # Get total URLs for this job
            total_urls = db.query(func.count(SerpAPIResult.id)).filter(
                SerpAPIResult.job_id == job_id
            ).scalar()
            
            # Get URLs that have been processed by RapidAPI
            processed_urls = db.query(func.count(SerpAPIResult.id)).join(
                RapidAPIResult, SerpAPIResult.id == RapidAPIResult.serp_result_id
            ).filter(SerpAPIResult.job_id == job_id).scalar()
            
            # Get unique search queries
            unique_queries = db.query(func.count(func.distinct(SerpAPIResult.search_query))).filter(
                SerpAPIResult.job_id == job_id
            ).scalar()
            
            return {
                "job_id": str(job_id),
                "total_urls": total_urls or 0,
                "processed_urls": processed_urls or 0,
                "pending_urls": (total_urls or 0) - (processed_urls or 0),
                "unique_search_queries": unique_queries or 0
            }
        except Exception as e:
            logging.error(f"Error getting URL statistics for job_id {job_id}: {str(e)}")
            return {
                "job_id": str(job_id),
                "error": str(e)
            }

    def cleanup_duplicate_urls(self, db: Session, job_id: UUID) -> Dict:
        """
        Clean up duplicate LinkedIn URLs for a specific job_id.
        Keeps the first occurrence and removes duplicates.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            
        Returns:
            Dict: Cleanup statistics
        """
        try:
            from sqlalchemy import func
            
            # Find duplicate URLs for this job_id
            duplicates = db.query(
                SerpAPIResult.linkedin_url,
                func.count(SerpAPIResult.id).label('count')
            ).filter(
                SerpAPIResult.job_id == job_id
            ).group_by(SerpAPIResult.linkedin_url).having(
                func.count(SerpAPIResult.id) > 1
            ).all()
            
            total_duplicates = sum(dup.count - 1 for dup in duplicates)
            
            if total_duplicates == 0:
                return {
                    "job_id": str(job_id),
                    "message": "No duplicates found",
                    "duplicates_removed": 0
                }
            
            # Remove duplicates (keep the first occurrence)
            for duplicate in duplicates:
                url = duplicate.linkedin_url
                count = duplicate.count
                
                # Get all records for this URL, ordered by creation time
                records = db.query(SerpAPIResult).filter(
                    SerpAPIResult.job_id == job_id,
                    SerpAPIResult.linkedin_url == url
                ).order_by(SerpAPIResult.created_at).all()
                
                # Keep the first record, delete the rest
                for record in records[1:]:
                    db.delete(record)
            
            db.commit()
            
            logging.info(f"Cleaned up {total_duplicates} duplicate URLs for job_id {job_id}")
            
            return {
                "job_id": str(job_id),
                "message": f"Cleaned up {total_duplicates} duplicate URLs",
                "duplicates_removed": total_duplicates,
                "duplicate_groups": len(duplicates)
            }
            
        except Exception as e:
            logging.error(f"Error cleaning up duplicate URLs for job_id {job_id}: {str(e)}")
            db.rollback()
            return {
                "job_id": str(job_id),
                "error": str(e)
            }

    async def verify_candidate_location(self, candidate_name: str, expected_location: str, company: str = None) -> Dict:
        """
        Verify candidate location using multiple search strategies.
        
        Parameters:
            candidate_name (str): Full name of the candidate
            expected_location (str): Expected location to verify
            company (str): Company name (optional)
            
        Returns:
            Dict: Location verification results with confidence score
        """
        try:
            logging.info(f"Verifying location for {candidate_name} in {expected_location}")
            
            # Strategy 1: Direct LinkedIn search
            linkedin_query = f'"{candidate_name}" "{expected_location}" site:linkedin.com/in/'
            linkedin_results = await self._search_google(linkedin_query, num_results=5)
            
            # Strategy 2: General web search for location mentions
            web_query = f'"{candidate_name}" "{expected_location}"'
            web_results = await self._search_google(web_query, num_results=10)
            
            # Strategy 3: Company-specific search
            company_results = []
            if company:
                company_query = f'"{candidate_name}" "{company}" "{expected_location}"'
                company_results = await self._search_google(company_query, num_results=5)
            
            # Analyze results
            verification_data = {
                "candidate_name": candidate_name,
                "expected_location": expected_location,
                "company": company,
                "linkedin_matches": [],
                "web_matches": [],
                "company_matches": [],
                "location_confidence": 0.0,
                "verification_result": "unknown",
                "evidence_sources": []
            }
            
            # Process LinkedIn results
            for result in linkedin_results:
                if self._analyze_location_match(result, expected_location):
                    verification_data["linkedin_matches"].append({
                        "title": result.get("title", ""),
                        "snippet": result.get("snippet", ""),
                        "link": result.get("link", ""),
                        "confidence": self._calculate_location_confidence(result, expected_location)
                    })
            
            # Process web results
            for result in web_results:
                if self._analyze_location_match(result, expected_location):
                    verification_data["web_matches"].append({
                        "title": result.get("title", ""),
                        "snippet": result.get("snippet", ""),
                        "link": result.get("link", ""),
                        "confidence": self._calculate_location_confidence(result, expected_location)
                    })
            
            # Process company results
            for result in company_results:
                if self._analyze_location_match(result, expected_location):
                    verification_data["company_matches"].append({
                        "title": result.get("title", ""),
                        "snippet": result.get("snippet", ""),
                        "link": result.get("link", ""),
                        "confidence": self._calculate_location_confidence(result, expected_location)
                    })
            
            # Calculate overall confidence
            all_matches = (verification_data["linkedin_matches"] + 
                          verification_data["web_matches"] + 
                          verification_data["company_matches"])
            
            if all_matches:
                total_confidence = sum(match["confidence"] for match in all_matches)
                verification_data["location_confidence"] = min(total_confidence / len(all_matches), 1.0)
                
                # Determine verification result
                if verification_data["location_confidence"] >= 0.7:
                    verification_data["verification_result"] = "confirmed"
                elif verification_data["location_confidence"] >= 0.4:
                    verification_data["verification_result"] = "likely"
                else:
                    verification_data["verification_result"] = "uncertain"
                
                # Collect evidence sources
                verification_data["evidence_sources"] = [
                    match["link"] for match in all_matches[:3]  # Top 3 sources
                ]
            
            logging.info(f"Location verification completed: {verification_data['verification_result']} (confidence: {verification_data['location_confidence']:.2f})")
            
            return verification_data
            
        except Exception as e:
            logging.error(f"Error in location verification: {str(e)}")
            return {
                "candidate_name": candidate_name,
                "expected_location": expected_location,
                "company": company,
                "error": str(e),
                "verification_result": "error"
            }

    def _analyze_location_match(self, search_result: Dict, expected_location: str) -> bool:
        """Analyze if a search result contains location information."""
        try:
            # Normalize location strings
            expected_lower = expected_location.lower()
            
            # Check title and snippet
            title = search_result.get("title", "").lower()
            snippet = search_result.get("snippet", "").lower()
            
            # Look for location mentions
            location_indicators = [
                expected_lower,
                expected_lower.replace(",", ""),
                expected_lower.split(",")[0],  # Just city
                expected_lower.split(",")[-1].strip() if "," in expected_lower else ""  # Just state/country
            ]
            
            for indicator in location_indicators:
                if indicator and (indicator in title or indicator in snippet):
                    return True
            
            return False
            
        except Exception as e:
            logging.warning(f"Error analyzing location match: {str(e)}")
            return False

    def _calculate_location_confidence(self, search_result: Dict, expected_location: str) -> float:
        """Calculate confidence score for location match."""
        try:
            confidence = 0.0
            
            title = search_result.get("title", "").lower()
            snippet = search_result.get("snippet", "").lower()
            expected_lower = expected_location.lower()
            
            # Exact match in title (highest confidence)
            if expected_lower in title:
                confidence += 0.8
            
            # Exact match in snippet
            if expected_lower in snippet:
                confidence += 0.6
            
            # Partial matches
            location_parts = expected_lower.split(",")
            for part in location_parts:
                part = part.strip()
                if part and len(part) > 2:
                    if part in title:
                        confidence += 0.3
                    if part in snippet:
                        confidence += 0.2
            
            # LinkedIn profile links get bonus
            if "linkedin.com/in/" in search_result.get("link", ""):
                confidence += 0.1
            
            return min(confidence, 1.0)
            
        except Exception as e:
            logging.warning(f"Error calculating confidence: {str(e)}")
            return 0.0

    async def _search_google(self, query: str, num_results: int = 10) -> List[Dict]:
        """Perform Google search using SerpAPI."""
        try:
            # Use existing search method
            search_results = await self.search_profiles(query, num_results)
            return search_results.get("organic_results", [])
        except Exception as e:
            logging.error(f"Error in Google search: {str(e)}")
            return [] 

    def batch_insert_results(self, db: Session, job_id: UUID, linkedin_urls: List[str], search_query: str):
        """
        Insert new unique LinkedIn URLs into serpapi_results table.
        Checks for duplicates both within the current job_id and across all job_ids.
        
        Args:
            db (Session): Database session
            job_id (UUID): Job ID
            linkedin_urls (List[str]): List of LinkedIn URLs to insert
            search_query (str): Search query used to find these URLs
        """
        try:
            # Clean and validate URLs first
            valid_urls = []
            for url in linkedin_urls:
                if self._is_valid_linkedin_profile_url(url):
                    valid_urls.append(url)
                else:
                    logging.warning(f"Filtered out invalid LinkedIn URL during batch insert: {url}")
            
            if not valid_urls:
                logging.info(f"No valid LinkedIn URLs to insert for job_id {job_id}")
                return
            
            logging.info(f"Processing {len(valid_urls)} valid LinkedIn URLs for job_id {job_id}")
            
            # Get existing URLs for this specific job_id
            existing_urls_for_job = set(
                r[0] for r in db.query(SerpAPIResult.linkedin_url)
                .filter(SerpAPIResult.job_id == job_id).all()
            )
            
            # Get existing URLs across all job_ids (to avoid global duplicates if needed)
            existing_urls_global = set(
                r[0] for r in db.query(SerpAPIResult.linkedin_url)
                .filter(SerpAPIResult.linkedin_url.in_(valid_urls)).all()
            )
            
            # Filter out URLs that already exist for this job_id
            new_urls_for_job = [url for url in valid_urls if url not in existing_urls_for_job]
            
            # Filter out URLs that exist globally (optional - uncomment if you want global uniqueness)
            # new_urls_global = [url for url in new_urls_for_job if url not in existing_urls_global]
            # new_urls_for_job = new_urls_global
            
            if not new_urls_for_job:
                logging.info(f"No new URLs to insert for job_id {job_id}. All {len(valid_urls)} URLs already exist.")
                return
            
            # Create new SerpAPIResult objects
            new_objs = [
                SerpAPIResult(
                    job_id=job_id,
                    linkedin_url=url,
                    search_query=search_query
                ) for url in new_urls_for_job
            ]
            
            # Insert new records
            if new_objs:
                db.bulk_save_objects(new_objs)
                db.commit()
                
                logging.info(f"Successfully inserted {len(new_objs)} new LinkedIn URLs for job_id {job_id}")
                logging.info(f"Summary: {len(valid_urls)} total URLs, {len(existing_urls_for_job)} already existed for job, {len(new_objs)} newly inserted")
                
                # Log some examples of new URLs (for debugging)
                if new_objs:
                    sample_urls = [obj.linkedin_url for obj in new_objs[:3]]
                    logging.info(f"Sample new URLs: {sample_urls}")
            else:
                logging.info(f"No new URLs to insert for job_id {job_id}")
                
        except Exception as e:
            logging.error(f"Error in batch_insert_results for job_id {job_id}: {str(e)}")
            db.rollback()
            raise 