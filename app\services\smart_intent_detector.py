import re
import logging
from typing import Dict, List
from urllib.parse import urlparse
import json

class SmartIntentDetector:
    """
    Intelligent intent detection system for recruitment chat workflow
    """
    
    def __init__(self, openai_client):
        self.openai_client = openai_client
        self.job_board_domains = {
            'linkedin.com', 'indeed.com', 'glassdoor.com', 'monster.com',
            'ziprecruiter.com', 'careerbuilder.com', 'dice.com'
        }
        
    def detect_intent_and_extract_data(self, query: str, current_step: str = None) -> Dict:
        """
        Main method to detect intent and extract relevant data from user query
        
        Returns:
        {
            "intent": str,
            "confidence": float,
            "extracted_data": dict,
            "reasoning": str
        }
        """
        # Step 1: Basic preprocessing
        cleaned_query = self._preprocess_query(query)
        
        # Step 2: Extract all URLs and analyze them
        urls_analysis = self._analyze_urls_in_query(cleaned_query)
        
        # Step 3: Use LLM for context-aware intent detection
        llm_analysis = self._llm_intent_detection(cleaned_query, urls_analysis, current_step)
        
        # Step 4: Combine rule-based and LLM results
        final_result = self._combine_analysis(cleaned_query, urls_analysis, llm_analysis)
        
        return final_result
    
    def _preprocess_query(self, query: str) -> str:
        """Clean and normalize the query"""
        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', query.strip())
        return cleaned
    
    def _analyze_urls_in_query(self, query: str) -> Dict:
        """
        Extract and categorize all URLs in the query
        """
        # Find all URLs
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'
        urls = re.findall(url_pattern, query)
        
        url_analysis = {
            "all_urls": urls,
            "job_urls": [],
            "company_urls": [],
            "career_landing_urls": [],
            "other_urls": [],
            "primary_job_url": None
        }
        
        for url in urls:
            url_type = self._classify_url(url)
            key = f"{url_type}_urls"
            if key not in url_analysis:
                url_analysis[key] = []
            url_analysis[key].append(url)
            
            # Set primary job URL (first job URL found)
            if url_type == "job" and not url_analysis["primary_job_url"]:
                url_analysis["primary_job_url"] = url
        
        return url_analysis
    
    def _classify_url(self, url: str) -> str:
        """
        Classify URL as job posting, company page, or other
        UNIVERSAL approach - not limited to known job boards
        """
        try:
            parsed = urlparse(url.lower())
            domain = parsed.netloc.replace('www.', '')
            path = parsed.path.lower()
            query_params = parsed.query.lower()
            
            # 1. Known job boards - high confidence
            if any(job_domain in domain for job_domain in self.job_board_domains):
                return "job"  # Job boards always treated as job URLs
            
            # 2. Job indicators in path/params - works for ANY domain
            job_path_indicators = [
                '/careers/', '/career/', '/jobs/', '/job/', '/positions/', '/position/',
                '/hiring/', '/vacancy/', '/vacancies/', '/opportunities/', '/opening/',
                '/recruitment/', '/apply/', '/join-us/', '/work-with-us/'
            ]
            
            # 3. Job-specific patterns in URL
            job_specific_patterns = [
                # have ID, slug after job Keywords
                r'/careers?/[^/]+$',  # /careers/software-engineer
                r'/jobs?/[^/]+$',     # /jobs/123 or /jobs/developer
                r'/positions?/[^/]+$', # /positions/qa-executive
                r'/hiring/[^/]+$',
                # Query parameters
                r'job[_-]?id=',       # ?job_id=123
                r'position[_-]?id=',  # ?position_id=abc
                r'vacancy[_-]?id=',
            ]
            
            # Check path indicators
            has_job_path = any(indicator in path for indicator in job_path_indicators)
            
            # Check if it's specific job (not just landing page)
            import re
            has_job_specific = any(re.search(pattern, path + '?' + query_params) 
                                for pattern in job_specific_patterns)
            
            if has_job_path and has_job_specific:
                return "job"  # ✅ Job detail URL - SCRAPE
            
            elif has_job_path and not has_job_specific:
                return "career_landing"  # Career page but not specific job
            
            # 4. General company indicators
            company_indicators = ['/about', '/contact', '/team', '/company', '/services']
            if any(indicator in path for indicator in company_indicators):
                return "company"
            
            return "other"
            
        except Exception:
            return "other"
    
    def _llm_intent_detection(self, query: str, urls_analysis: Dict, current_step: str) -> Dict:
        """
        Use LLM for sophisticated intent detection
        """
        # Build context for LLM
        context = {
            "query": query,
            "urls_found": len(urls_analysis["all_urls"]),
            "job_urls_count": len(urls_analysis["job_urls"]),
            "company_urls_count": len(urls_analysis["company_urls"]),
            "current_workflow_step": current_step
        }

        safe_query = json.dumps(query[:8000])  # limit and escape

        
        # Enhanced prompt for intent detection
        prompt = f"""
You are an expert at understanding user intentions in a recruitment workflow system.

Current context:
- User query: {safe_query}
- URLs found: {context['urls_found']}
- Job posting URLs: {context['job_urls_count']}
- Company URLs: {context['company_urls_count']}
- Current workflow step: {context['current_workflow_step']}

Available intents:
1. "parse_jd" - User wants to parse a job description from text
2. "parse_jd_url" - User wants to parse a job description from a URL
3. "add_note" - User wants to add a note to a job
4. "search_candidates" - User wants to search for candidates
5. "batch_candidates" - User wants to process candidates in batch
6. "rank_candidates" - User wants to rank candidates
7. "clarification_needed" - Intent is unclear, need more info

Examples to consider:
- "Here's a job description: [long text with company URL]" → parse_jd (ignore company URL)
- "Check this job: https://linkedin.com/jobs/123" → parse_jd_url
- "https://linkedin.com/jobs/123 looks interesting" → parse_jd_url
- "Add a note: candidate seems good" → add_note
- "Find software engineers in New York City" → search_candidates

Note: If current_workflow_step is none (first step):
- 1. It must first return the intent "parse_jd" or "parse_jd_url". If the user query is ambiguous for those steps, return clarity_needed
- 2. If user query is a clear JD, enough information is provided, then return parse_jd because we want to parse the JD as soon as possible, no need to parse jd URL

Analyze the user's intent and return ONLY a JSON response:
{{
    "intent": "one_of_the_available_intents",
    "confidence": 0.0-1.0,
    "reasoning": "brief explanation of why you chose this intent",
    "extracted_data": {{
        "job_description_text": "extracted JD text if present",
        "note_text": "extracted note text if present",
        "job_title": "extracted job title if present",
        "location": "extracted location if present",
        "primary_url": "most relevant URL if present"
    }}
}}
Please keep "job_description_text" short and summarized in 8-10 sentences with Requirements, skills, location, company, role, if it's long.
"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",  # Use faster model for intent detection
                messages=[
                    {"role": "system", "content": "You are an expert intent detection system. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500,
            )
            
            content = response.choices[0].message.content.strip()
            
            # Clean JSON response
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            
            return json.loads(content)
            
        except Exception as e:
            logging.error(f"LLM intent detection failed: {e}")
            logging.debug(f"Prompt size: {len(prompt)} chars")
            logging.error(f"Raw OpenAI JSON:\n{prompt}")
            return {
                "intent": "clarification_needed",
                "confidence": 0.0,
                "reasoning": "LLM analysis failed",
                "extracted_data": {}
            }
    
    def _combine_analysis(self, query: str, urls_analysis: Dict, llm_analysis: Dict) -> Dict:
        """
        Combine rule-based URL analysis with LLM intent detection
        """
        final_result = {
            "intent": llm_analysis.get("intent", "clarification_needed"),
            "confidence": llm_analysis.get("confidence", 0.0),
            "extracted_data": llm_analysis.get("extracted_data", {}),
            "reasoning": llm_analysis.get("reasoning", ""),
            "urls_analysis": urls_analysis
        }
        
        # Override LLM decisions based on strong URL signals
        if urls_analysis["job_urls"] and llm_analysis.get("intent") not in ["parse_jd_url"]:
            # If we found job URLs but LLM didn't detect parse_jd_url intent
            # Check if there's substantial text content that might be JD
            word_count = len(query.split())
            if word_count > 50:  # Likely contains JD text
                final_result["intent"] = "parse_jd"
                final_result["reasoning"] += " | Detected both job URL and substantial text content - prioritizing text parsing"
            else:
                final_result["intent"] = "parse_jd_url"
                final_result["reasoning"] += " | Strong job URL signal detected"
        
        # Set primary URL for processing
        if urls_analysis["primary_job_url"]:
            final_result["extracted_data"]["primary_url"] = urls_analysis["primary_job_url"]
        
        # Enhanced data extraction based on intent
        final_result["extracted_data"] = self._enhance_extracted_data(
            query, final_result["intent"], final_result["extracted_data"], urls_analysis
        )
        
        return final_result
    
    def _enhance_extracted_data(self, query: str, intent: str, extracted_data: Dict, urls_analysis: Dict) -> Dict:
        """
        Enhance extracted data based on intent and context
        """
        enhanced = extracted_data.copy()
        
        if intent == "parse_jd":
            # Extract JD text, excluding URLs and commands
            jd_text = self._extract_jd_text_from_query(query, urls_analysis["all_urls"])
            if jd_text:
                enhanced["job_description_text"] = jd_text
        
        elif intent == "parse_jd_url":
            # Use primary job URL
            if urls_analysis["primary_job_url"]:
                enhanced["url"] = urls_analysis["primary_job_url"]
        
        elif intent == "add_note":
            # Extract note text
            note_text = self._extract_note_text(query)
            if note_text:
                enhanced["note_text"] = note_text
        
        elif intent == "search_candidates":
            # Extract search parameters
            search_params = self._extract_search_params(query)
            enhanced.update(search_params)
        
        return enhanced
    
    def _extract_jd_text_from_query(self, query: str, urls: List[str]) -> str:
        """
        Extract job description text from query, removing URLs and commands
        """
        text = query
        
        # Remove URLs
        for url in urls:
            text = text.replace(url, "")
        
        # Remove common commands
        commands_to_remove = [
            r'parse\s+(this\s+)?(the\s+)?job\s+description:?\s*',
            r'parse\s+jd:?\s*',
            r'job\s+description:?\s*',
            r'jd:?\s*'
        ]
        
        for pattern in commands_to_remove:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        # Clean up extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Only return if substantial content remains
        if len(text.split()) > 10:  # At least 10 words
            return text
        
        return ""
    
    def _extract_note_text(self, query: str) -> str:
        """Extract note text from query"""
        patterns = [
            r'add\s+(a\s+)?note:?\s*(.+)',
            r'note:?\s*(.+)',
            r'add\s+note\s+(.+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, query, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(-1).strip()  # Get last group
        
        # If no pattern matches, but query seems to be about adding note
        if re.search(r'\b(add|note)\b', query, re.IGNORECASE):
            # Remove the command part and return the rest
            cleaned = re.sub(r'^.*?\b(add|note)\b:?\s*', '', query, flags=re.IGNORECASE)
            if cleaned.strip():
                return cleaned.strip()
        
        return ""
    
    def _extract_search_params(self, query: str) -> Dict:
        """Extract search parameters for candidate search"""
        # Use LLM for parameter extraction
        prompt = f"""
Extract job search parameters from this query: "{query}"

Return ONLY JSON:
{{
    "job_title": "extracted job title or empty string",
    "location": "extracted location or empty string",
    "skills": ["list", "of", "skills"] or [],
    "experience_level": "junior/mid/senior or empty string"
}}
"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Extract search parameters. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=200
            )
            
            content = response.choices[0].message.content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            
            return json.loads(content)
            
        except Exception as e:
            logging.error(f"Failed to extract search params: {e}")
            return {"job_title": "", "location": "", "skills": [], "experience_level": ""}
