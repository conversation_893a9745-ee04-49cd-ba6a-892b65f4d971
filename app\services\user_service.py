from sqlalchemy.orm import Session
from app.db.db_models import User
from app.auth.schemas import UserCreate, UserUpdate
from app.auth.security import get_password_hash
from typing import Optional

class UserService:
    def get_user_by_email(self, db: Session, email: str) -> User | None:
        """
        Retrieves a user from the database by their email address.
        """
        return db.query(User).filter(User.email == email).first()

    def create_user(self, db: Session, user: UserCreate) -> User:
        """
        Creates a new user in the database.
        """
        # Hash the user's password before storing it
        hashed_password = get_password_hash(user.password)
        
        db_user = User(
            email=user.username,  # username is the email
            password_hash=hashed_password,
            full_name=user.full_name,
            company_name=user.company_name,
            status=user.status or 'active',  # Use provided status or default
            plan=user.plan or 'trial'        # Use provided plan or default
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        return db_user

    def update_user_status(self, db: Session, user_id: str, status: str) -> Optional[User]:
        """Update user status"""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            user.status = status
            db.commit()
            db.refresh(user)
        return user

    def update_user_plan(self, db: Session, user_id: str, plan: str) -> Optional[User]:
        """Update user plan"""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            user.plan = plan
            db.commit()
            db.refresh(user)
        return user

    def update_user(self, db: Session, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """Update user with multiple fields"""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            # Update only provided fields
            if user_update.status is not None:
                user.status = user_update.status
            if user_update.plan is not None:
                user.plan = user_update.plan
            if user_update.company_name is not None:
                user.company_name = user_update.company_name
            if user_update.full_name is not None:
                user.full_name = user_update.full_name

            db.commit()
            db.refresh(user)
        return user
