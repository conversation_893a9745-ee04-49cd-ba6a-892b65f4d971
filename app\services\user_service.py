from sqlalchemy.orm import Session
from app.db.db_models import User
from app.auth.schemas import UserCreate, UserUpdate, PaymentWebhook
from app.auth.security import get_password_hash
from typing import Optional, Dict, Any
from app.logger import logging
import uuid

class UserService:
    def get_user_by_email(self, db: Session, email: str) -> User | None:
        """
        Retrieves a user from the database by their email address.
        """
        return db.query(User).filter(User.email == email).first()

    def create_user(self, db: Session, user: UserCreate) -> User:
        """
        Creates a new user in the database.
        """
        # Hash the user's password before storing it
        hashed_password = get_password_hash(user.password)
        
        db_user = User(
            email=user.username,  # username is the email
            password_hash=hashed_password,
            full_name=user.full_name,
            company_name=user.company_name,
            status=user.status or 'active',  # Use provided status or default
            plan=user.plan or 'trial'        # Use provided plan or default
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)

        return db_user

    def process_payment_webhook(self, db: Session, webhook_data: PaymentWebhook) -> Dict[str, Any]:
        """Process payment webhook and update user status/plan"""
        try:
            logging.info(f"Processing payment webhook for user_id: {webhook_data.user_id}")

            # Find user by ID
            user = None
            try:
                user_uuid = uuid.UUID(webhook_data.user_id)
                user = db.query(User).filter(User.id == user_uuid).first()
            except ValueError:
                logging.error(f"Invalid user_id format: {webhook_data.user_id}")
                return {
                    "success": False,
                    "message": "Invalid user_id format",
                    "user_id": webhook_data.user_id,
                    "updated_fields": {}
                }

            if not user:
                logging.error(f"User not found: {webhook_data.user_id}")
                return {
                    "success": False,
                    "message": "User not found",
                    "user_id": webhook_data.user_id,
                    "updated_fields": {}
                }

            # Verify email if provided
            if webhook_data.user_email and user.email != webhook_data.user_email:
                logging.warning(f"Email mismatch for user {webhook_data.user_id}: {user.email} != {webhook_data.user_email}")
                return {
                    "success": False,
                    "message": "User email mismatch",
                    "user_id": webhook_data.user_id,
                    "updated_fields": {}
                }

            # Store old values for logging
            old_status = user.status
            old_plan = user.plan

            # Update user status and plan
            user.status = webhook_data.status
            user.plan = webhook_data.plan

            # Commit changes
            db.commit()
            db.refresh(user)

            updated_fields = {
                "status": {"old": old_status, "new": user.status},
                "plan": {"old": old_plan, "new": user.plan}
            }

            # Log the update
            logging.info(f"Payment webhook processed successfully for user {webhook_data.user_id}")
            logging.info(f"Status: {old_status} -> {user.status}")
            logging.info(f"Plan: {old_plan} -> {user.plan}")
            if webhook_data.customer_id:
                logging.info(f"Customer ID: {webhook_data.customer_id}")
            if webhook_data.bill_id:
                logging.info(f"Bill ID: {webhook_data.bill_id}")

            return {
                "success": True,
                "message": "User updated successfully",
                "user_id": webhook_data.user_id,
                "updated_fields": updated_fields
            }

        except Exception as e:
            logging.error(f"Error processing payment webhook: {str(e)}")
            db.rollback()
            return {
                "success": False,
                "message": f"Internal error: {str(e)}",
                "user_id": webhook_data.user_id,
                "updated_fields": {}
            }

    def update_user(self, db: Session, user_id: str, user_update: UserUpdate) -> Optional[User]:
        """Update user with multiple fields"""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            # Update only provided fields
            if user_update.status is not None:
                user.status = user_update.status
            if user_update.plan is not None:
                user.plan = user_update.plan
            if user_update.company_name is not None:
                user.company_name = user_update.company_name
            if user_update.full_name is not None:
                user.full_name = user_update.full_name

            db.commit()
            db.refresh(user)
        return user
