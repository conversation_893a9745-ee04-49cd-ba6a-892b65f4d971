from sqlalchemy.orm import Session
from app.db.db_models import User
from app.auth.schemas import UserCreate
from app.auth.security import get_password_hash

class UserService:
    def get_user_by_email(self, db: Session, email: str) -> User | None:
        """
        Retrieves a user from the database by their email address.
        """
        return db.query(User).filter(User.email == email).first()

    def create_user(self, db: Session, user: UserCreate) -> User:
        """
        Creates a new user in the database.
        """
        # Hash the user's password before storing it
        hashed_password = get_password_hash(user.password)
        
        db_user = User(
            email=user.username,  # username is the email
            password_hash=hashed_password,
            full_name=user.full_name,
            company_name=user.company_name
        )
        
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        return db_user
