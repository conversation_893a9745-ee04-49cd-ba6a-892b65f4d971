import re
import spacy
import httpx

# Load spaCy model for semantic fallback
nlp = spacy.load("en_core_web_md")  # python -m spacy download en_core_web_md

# --- INTENT_MAP and route_query as before (copy from previous code) ---
INTENT_MAP = [
    {
        "name": "scrape_linkedin_profile",
        "keywords": ["linkedin profile", "scrape profile", "profile url"],
        "synonyms": ["get profile", "fetch profile", "profile link"],
        "regex_patterns": [r"linkedin\\.com/in/"],
        "endpoint": "/linkedin-profile/details",
        "method": "POST",
        "required_keys": ["linkedin_url"],
        "description": "Scrape a LinkedIn profile. Please provide the profile URL."
    },
    {
        "name": "parse_job_description_url",
        "keywords": ["job url", "scrape job", "parse job url", "job posting url"],
        "synonyms": ["extract from url", "parse from url", "scrape job posting"],
        "regex_patterns": [r"linkedin\\.com/jobs/"],
        "endpoint": "/api/v1/jd/parse-url",
        "method": "POST",
        "required_keys": ["url"],
        "description": "Parse a job description from a URL. Please provide the job URL."
    },
    {
        "name": "parse_job_description",
        "keywords": ["job description", "parse jd", "analyze job", "extract from jd"],
        "synonyms": ["extract jd", "analyze description", "parse job"],
        "regex_patterns": [],
        "endpoint": "/api/v1/jd/parse",
        "method": "POST",
        "required_keys": ["description"],
        "description": "Parse a raw job description. Please provide the text."
    },
    {
        "name": "serpapi_search",
        "keywords": ["search", "serpapi", "find candidates", "linkedin search"],
        "synonyms": ["profile search", "candidate search", "search linkedin"],
        "regex_patterns": [],
        "endpoint": "/api/v1/serpapi/search",
        "method": "POST",
        "required_keys": ["query"],
        "description": "Search candidates using SerpAPI. Provide a query and optional filters."
    },
    {
        "name": "batch_candidates",
        "keywords": ["batch candidates", "process candidates", "job id"],
        "synonyms": ["gather resumes", "group profiles", "run batch"],
        "regex_patterns": [r"batch.*job", r"process.*candidates"],
        "endpoint": "/rapidapi-linkedin/batch-candidates",
        "method": "POST",
        "required_keys": ["job_id"],
        "description": "Batch process candidates. Please provide a job ID."
    },
]

def semantic_match(user_query, label_set):
    doc = nlp(user_query)
    best_match = None
    best_score = 0
    for label in label_set:
        score = doc.similarity(nlp(label))
        if score > best_score:
            best_score = score
            best_match = label
    return best_match, best_score

def route_query(user_input: str, extracted_data: dict) -> dict:
    user_input_lower = user_input.lower()
    for intent in INTENT_MAP:
        tokens = user_input_lower
        if (
            any(kw in tokens for kw in intent["keywords"] + intent.get("synonyms", [])) or
            any(re.search(p, tokens) for p in intent.get("regex_patterns", []))
        ):
            missing_keys = [key for key in intent["required_keys"] if key not in extracted_data]
            if missing_keys:
                return {
                    "action": "ask_user",
                    "message": f"Missing fields: {', '.join(missing_keys)}. {intent['description']}"
                }
            return {
                "action": "call_api",
                "endpoint": intent["endpoint"],
                "method": intent["method"],
                "payload": {k: extracted_data[k] for k in intent["required_keys"]}
            }
    label_set = [intent["description"] for intent in INTENT_MAP]
    best_label, best_score = semantic_match(user_input, label_set)
    if best_score > 0.85:
        intent = next(i for i in INTENT_MAP if i["description"] == best_label)
        missing_keys = [key for key in intent["required_keys"] if key not in extracted_data]
        if missing_keys:
            return {
                "action": "ask_user",
                "message": f"Missing fields: {', '.join(missing_keys)}. {intent['description']}"
            }
        return {
            "action": "call_api",
            "endpoint": intent["endpoint"],
            "method": intent["method"],
            "payload": {k: extracted_data[k] for k in intent["required_keys"]}
        }
    return {
        "action": "clarify",
        "message": (
            "I didn't fully understand your request. Could you tell me if you want to:\n" +
            "\n".join([f"{idx+1}. {intent['description']}" for idx, intent in enumerate(INTENT_MAP)])
        )
    }

# --- Test cases ---
test_cases = [
    {
        "query": "Scrape this LinkedIn profile: https://www.linkedin.com/in/sumit-kumar6597/",
        "data": {"linkedin_url": "https://www.linkedin.com/in/sumit-kumar6597/"}
    },
    {
        "query": "Parse this job url: https://www.linkedin.com/jobs/collections/top-applicant/?currentJobId=4251766248",
        "data": {"url": "https://www.linkedin.com/jobs/collections/top-applicant/?currentJobId=4251766248"}
    },
    {
        "query": "Parse this job description: Software Engineer, Python, remote...",
        "data": {"description": "Software Engineer, Python, remote..."}
    },
    {
        "query": "Search for software engineer candidates in San Francisco",
        "data": {"query": "software engineer", "location": "San Francisco", "max_pages": 1}
    },
    {
        "query": "Batch candidates for job_id: 7bff7407-fb7d-49fd-b7ec-434e3e2240d7",
        "data": {"job_id": "7bff7407-fb7d-49fd-b7ec-434e3e2240d7"}
    },
    {
        "query": "Can you help me?",
        "data": {}
    }
]

# --- Login and get token ---
def get_access_token():
    login_url = "http://localhost:8000/auth/login"
    payload = {
        "username": "<EMAIL>",
        "password": "Testpassword@2026"
    }
    headers = {"Content-Type": "application/x-www-form-urlencoded"}
    with httpx.Client() as client:
        resp = client.post(login_url, data=payload, headers=headers)
        resp.raise_for_status()
        return resp.json()["access_token"]

access_token = get_access_token()
print(f"Using access token: {access_token[:20]}...")

# --- Run test cases ---
for case in test_cases:
    print(f"\nUser Query: {case['query']}")
    result = route_query(case["query"], case["data"])
    print("Router Output:")
    print(result)
    if result["action"] == "call_api":
        url = f"http://localhost:8000{result['endpoint']}"
        method = result["method"]
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json"
        }
        print(f"Making {method} request to {url} with payload {result['payload']}")
        with httpx.Client() as client:
            if method == "POST":
                resp = client.post(url, json=result["payload"], headers=headers)
            elif method == "GET":
                resp = client.get(url, params=result["payload"], headers=headers)
            else:
                print(f"Unsupported method: {method}")
                continue
            print("API Response:")
            print(resp.text)
    else:
        print("No API call made. Message:")
        print(result["message"])