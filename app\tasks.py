from app.celery_worker import celery_app
from app.db.database import <PERSON><PERSON><PERSON><PERSON>
from app.services.serpapi_service import SerpAPIService
from app.services.rapidapi_batch_service import RapidAPIBatchService
from app.rank.ranking import extract_info
from app.db.db_models import User, JobDescription, RankingResult
from app.services.master_service import update_master_user
from fastapi import BackgroundTasks

@celery_app.task(name="app.tasks.full_process_job")
def full_process_job(job_args, background_tasks=BackgroundTasks):
    step_logs = []
    with SessionLocal() as db:
        try:
            job_id = job_args["job_id"]
            job_title = job_args.get("job_title")
            location = job_args.get("location")
            max_pages = job_args.get("max_pages", 1)
            user_id = job_args["user_id"]
            user_obj = db.query(User).filter(User.id == user_id).first()
            if not user_obj:
                return {"status": "error", "message": "User not found.", "step_logs": step_logs}
            if getattr(user_obj, 'credit', 1) <= 0:
                return {"status": "error", "message": "Your one trial credit has been used.", "step_logs": step_logs}
            jd = db.query(JobDescription).filter(
                JobDescription.id == job_id,
                JobDescription.user_id == user_id
            ).first()
            if not jd:
                step_logs.append(f"Job description not found for job_id {job_id} or access denied.")
                return {"status": "error", "message": "Job description not found or access denied.", "step_logs": step_logs}
            db_job_title = getattr(jd, 'job_title', None)
            db_location = getattr(jd, 'location', None)
            final_job_title = job_title or db_job_title
            final_location = location or db_location
            missing_fields = []
            if not final_job_title or final_job_title in ("N/A", ""):
                missing_fields.append("job_title")
            if not final_location or final_location in ("N/A", ""):
                missing_fields.append("location")
            if missing_fields:
                step_logs.append(f"Missing required field(s): {', '.join(missing_fields)}. Please provide them.")
                return {
                    "status": "error",
                    "message": f"Missing required field(s): {', '.join(missing_fields)}. Please provide them.",
                    "step_logs": step_logs
                }
            step_logs.append(f"Checked job description: found (title: {final_job_title}, location: {final_location})")
            def clean_job_title(title):
                if not title:
                    return ''
                return title.split(',')[0].strip()
            def clean_location(location):
                if not location:
                    return ''
                import re
                location = re.sub(r'\s*\(.*?\)', '', location)
                return location.split(',')[0].strip()
            cleaned_job_title = clean_job_title(final_job_title)
            cleaned_location = clean_location(final_location)
            step_logs.append(f"Cleaned job title: {cleaned_job_title}, Cleaned location: {cleaned_location}")
            search_params = {
                "job_id": job_id,
                "query": cleaned_job_title,
                "location": cleaned_location,
                "max_pages": max_pages
            }
            serpapi_service = SerpAPIService()
            import asyncio
            search_result = asyncio.run(serpapi_service.search_profiles(search_params))
            linkedin_urls = search_result.get("linkedin_urls", [])
            step_logs.append(f"Searched LinkedIn: {len(linkedin_urls)} URLs found.")
            if not linkedin_urls:
                return {"status": "error", "message": "No LinkedIn URLs found.", "step_logs": step_logs}
            serpapi_service.batch_insert_results(db, job_id, linkedin_urls, search_result.get('query', ''))
            step_logs.append(f"Inserted {len(linkedin_urls)} LinkedIn URLs into the database.")
            rapidapi_batch_service = RapidAPIBatchService()
            candidates = rapidapi_batch_service.fetch_and_store_candidates(db, job_id, user_id)
            step_logs.append(f"Batch extraction: {len(candidates)} candidates extracted.")
            if not candidates:
                return {"status": "error", "message": "No candidates extracted.", "step_logs": step_logs}
            ranking_result = extract_info(job_id, save_to_db=True)
            top_5_candidates = []
            if ranking_result and 'merged_df' in ranking_result:
                merged_df = ranking_result['merged_df']
                if hasattr(merged_df, 'to_dict'):
                    df_dict = merged_df.to_dict('records')
                    sorted_candidates = sorted(df_dict, key=lambda x: x.get('New_ranked_Index', 999))
                    valid_candidates = []
                    for candidate in df_dict:
                        name = candidate.get('Name', '').strip()
                        linkedin_url = candidate.get('Extracted_LinkedIn_URLs', '')
                        score = candidate.get('Scores', 0)
                        if (name and name != " " and linkedin_url and "linkedin.com/pub/dir" not in linkedin_url and score > 0):
                            valid_candidates.append(candidate)
                    valid_candidates.sort(key=lambda x: x.get('Scores', 0), reverse=True)
                    for i, candidate in enumerate(valid_candidates[:5]):
                        experiences = candidate.get('Experiences', [])
                        most_recent_position = "None"
                        most_recent_company = "None"
                        total_experience = candidate.get('Tenure_discrete', "N/A")
                        if experiences:
                            most_recent_exp = experiences[0]
                            most_recent_position = most_recent_exp.get('Position', 'None')
                            most_recent_company = most_recent_exp.get('Company', 'None')
                        universities = candidate.get('Universities', [])
                        most_recent_education = "None"
                        if universities:
                            most_recent_uni = universities[0]
                            uni_name = most_recent_uni.get('Name', 'None')
                            uni_degree = most_recent_uni.get('Degree', '')
                            uni_major = most_recent_uni.get('Major', '')
                            if uni_degree and uni_major:
                                most_recent_education = f"{uni_name} ({uni_degree}, {uni_major})"
                            elif uni_degree:
                                most_recent_education = f"{uni_name} ({uni_degree})"
                            else:
                                most_recent_education = uni_name
                        skills = candidate.get('Skills', [])
                        if isinstance(skills, list):
                            skills_str = ', '.join(skills)
                        else:
                            skills_str = str(skills)
                        background = f"{most_recent_position} at {most_recent_company} with {total_experience} years experience"
                        ranking_result_obj = db.query(RankingResult).filter(
                            RankingResult.job_id == job_id,
                            RankingResult.linkedin_url == candidate.get('Extracted_LinkedIn_URLs', '')
                        ).first()
                        candidate_id = str(ranking_result_obj.id) if ranking_result_obj else ''
                        enriched_candidate = {
                            "candidate_id": candidate_id,
                            "rank": i + 1,
                            "name": candidate.get('Name', 'Unknown').strip(),
                            "position": most_recent_position,
                            "company": most_recent_company,
                            "background": background,
                            "education": most_recent_education,
                            "skills": skills if isinstance(skills, list) else [skills_str],
                            "total_experience": total_experience,
                            "score": round(candidate.get('Scores', 0), 2),
                            "linkedin_url": candidate.get('Extracted_LinkedIn_URLs', '')
                        }
                        top_5_candidates.append(enriched_candidate)
            step_logs.append(f"Ranking: top {len(top_5_candidates)} candidates returned.")
            if top_5_candidates:
                if getattr(user_obj, 'credit', 1) > 0:
                    user_obj.credit -= 1
                    db.commit()
                    db.refresh(user_obj)
                    background_tasks.add_task(
                        update_master_user,
                        email=user_obj.email,
                        company_name=user_obj.company_name, 
                        credits=user_obj.credit
                    )
                    step_logs.append("User credit decremented by 1 after successful ranking.")
            return {
                "status": "success",
                "message": "Ranking completed",
                "step_logs": step_logs,
                "data": {
                    "top_5_candidates": top_5_candidates,
                    "credit": user_obj.credit
                }
            }
        except Exception as e:
            step_logs.append(f"Error: {str(e)}")
            return {"status": "error", "message": str(e), "step_logs": step_logs}