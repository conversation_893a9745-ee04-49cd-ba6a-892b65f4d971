import re

def extract_urls(text: str) -> list[str]:
    url_pattern = re.compile(r'https?://[^\s]+')
    return url_pattern.findall(text)

def detect_parse_jd_intent(query: str) -> str:
    query_lower = query.lower()
    urls = extract_urls(query)
    num_urls = len(urls)
    word_count = len(query.split())

    jd_text_keywords = [
        "parse jd", "parse job desc", "parse job description",
        "parse this job description", "parse this job desc",
        "can you help with this jd", "take a look at this jd",
        "any thoughts on this jd", "help me understand this jd",
        "analyze this jd", "extract info from this jd",
        "what do you think of this jd", "pls parse", "please parse", "plz parse",
        "parse this", "parse it", "parse jd pls",
        "see attached jd", "uploaded the jd",
        "jd file attached", "see jd below", "just pasted jd"
    ]

    jd_url_keywords = [
        "parse url", "parse link", "parse job url", "parse job link",
        "parse jd url", "parse job description url", "parse job desc url",
        "parse jd link", "parse job description link", "parse job desc link",
        "parse jd link url", "parse job description link url", "parse job desc link url",
        "parse jd url link", "parse job description url link", "parse job desc url link",
        "parse this url", "parse this link", "parse this job url", "parse this job link",
        "parse this jd url", "parse this job description url", "parse this job desc url",
        "parse this jd link", "parse this job description link", "parse this job desc link",
        "parse this jd link url", "parse this job description link url", "parse this job desc link url",
        "parse this jd url link", "parse this job description url link", "parse this job desc url link",
        "parse this site", "parse the site",
        "parse website", "parse this website", "parse job site",
        "parse this job site", "parse this job website", "parse this job page",
        "check this site", "check this job site", "check job page",
        "scrape this site", "extract from this site", "extract job from site", "extract job from this site",
        "extract job from this job site", "extract job from this job page"
    ]

    if num_urls == 1 and query.strip() == urls[0]:
        return "parse_jd_url"

    if num_urls == 1:
        stripped = query_lower.strip()
        for url in urls:
            if (
                stripped.endswith(url)
                and any(
                    phrase in stripped.replace(url, "").strip()
                    for phrase in [
                        "parse", "parse job", "parse this job", "help parse", "help to parse", "help to parse job"
                    ]
                )
            ):
                return "parse_jd_url"

    if any(kw in query_lower for kw in jd_url_keywords) and num_urls >= 1:
        return "parse_jd_url"

    if any(kw in query_lower for kw in jd_text_keywords) and num_urls == 0:
        return "parse_jd"

    if num_urls > 1:
        return "parse_jd"

    if num_urls == 1 and word_count > 30:
        return "parse_jd"

    if (
        query_lower.startswith("here's the jd") or
        "please parse this job" in query_lower or
        "here is the jd" in query_lower or
        "here’s the job description" in query_lower or
        "job description is as follows" in query_lower or
        "this is the jd" in query_lower or
        "can you parse this jd" in query_lower or
        "can you check this job description" in query_lower or
        "the job desc is" in query_lower
    ):
        return "parse_jd"

    return "parse_jd"