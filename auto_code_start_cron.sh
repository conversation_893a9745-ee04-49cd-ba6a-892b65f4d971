#!/bin/bash

# === SETUP CONDA ENV ===
source /home/<USER>/miniconda3/etc/profile.d/conda.sh
conda activate dashchat_friend

# === CONFIG ===
PROJECT_DIR="/home/<USER>/DashChat"
LOG_PATH="$PROJECT_DIR/app_api_status.txt"
APP_LOG="$PROJECT_DIR/app-Log.txt"
UVICORN_CMD="uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

# === LOG START ===
echo "$(date): <PERSON>ron script triggered" >> "$LOG_PATH"

# === GO TO PROJECT DIR ===
cd "$PROJECT_DIR" || { echo "$(date): Failed to cd to $PROJECT_DIR" >> "$LOG_PATH"; exit 1; }

# === CHECK IF RUNNING ===
if ! pgrep -af "$UVICORN_CMD" > /dev/null; then
    echo "$(date): Starting DashChat app..." >> "$LOG_PATH"
    nohup python -m $UVICORN_CMD >> "$APP_LOG" 2>&1 &
    echo "$(date): DashChat app launched." >> "$LOG_PATH"
else
    echo "$(date): DashChat app already running." >> "$LOG_PATH"
fi
