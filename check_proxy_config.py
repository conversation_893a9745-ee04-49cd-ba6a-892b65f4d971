#!/usr/bin/env python3
"""
Script to check proxy configuration
"""
import os
import requests
import asyncio

def check_environment_variables():
    """Check if proxy environment variables are set"""
    print("=== Checking Environment Variables ===")
    
    proxy_vars = [
        "SCRAPER_PROXY_USER",
        "SCRAPER_PROXY_PASS", 
        "SCRAPER_PROXY_HOST",
        "SCRAPER_PROXY_PORT"
    ]
    
    for var in proxy_vars:
        value = os.getenv(var)
        if value:
            if "PASS" in var:
                print(f"{var}: {'*' * len(value)}")  # Hide password
            else:
                print(f"{var}: {value}")
        else:
            print(f"{var}: NOT SET")
    
    return all(os.getenv(var) for var in proxy_vars)

def test_proxy_connection():
    """Test if proxy is working"""
    print("\n=== Testing Proxy Connection ===")
    
    proxy_user = os.getenv("SCRAPER_PROXY_USER")
    proxy_pass = os.getenv("SCRAPER_PROXY_PASS")
    proxy_host = os.getenv("SCRAPER_PROXY_HOST", "brd.superproxy.io")
    proxy_port = int(os.getenv("SCRAPER_PROXY_PORT", 9222))
    
    if not all([proxy_user, proxy_pass]):
        print("❌ Proxy credentials not set")
        return False
    
    proxy_url = f"http://{proxy_user}:{proxy_pass}@{proxy_host}:{proxy_port}"
    
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    try:
        # Test with a simple IP check service
        print("Testing proxy with httpbin.org...")
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ Proxy working! IP: {ip_info.get('origin')}")
            return True
        else:
            print(f"❌ Proxy test failed. Status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Proxy connection failed: {str(e)}")
        return False

def test_direct_connection():
    """Test direct connection (no proxy)"""
    print("\n=== Testing Direct Connection ===")
    
    try:
        response = requests.get('http://httpbin.org/ip', timeout=10)
        if response.status_code == 200:
            ip_info = response.json()
            print(f"Direct connection IP: {ip_info.get('origin')}")
            return True
        else:
            print(f"Direct connection failed. Status: {response.status_code}")
            return False
    except Exception as e:
        print(f"Direct connection failed: {str(e)}")
        return False

def main():
    """Main function"""
    print("🔍 Proxy Configuration Checker")
    print("=" * 50)
    
    # Check environment variables
    env_ok = check_environment_variables()
    
    # Test direct connection first
    direct_ok = test_direct_connection()
    
    # Test proxy connection if configured
    if env_ok:
        proxy_ok = test_proxy_connection()
        
        if proxy_ok:
            print("\n✅ Proxy configuration looks good!")
        else:
            print("\n❌ Proxy configuration has issues")
            print("\nTroubleshooting tips:")
            print("1. Check proxy credentials")
            print("2. Verify proxy server is accessible")
            print("3. Check firewall settings")
    else:
        print("\n❌ Proxy environment variables not properly set")
        print("\nRequired environment variables:")
        print("- SCRAPER_PROXY_USER")
        print("- SCRAPER_PROXY_PASS")
        print("- SCRAPER_PROXY_HOST (optional, defaults to brd.superproxy.io)")
        print("- SCRAPER_PROXY_PORT (optional, defaults to 9222)")

if __name__ == "__main__":
    main()
