#!/usr/bin/env python3
"""
Migration script to add status and plan fields to users table
Created: 2025-01-15
Description: Add status (active/inactive/upgrading) and plan (trial/starter/pro) fields to users table
"""

import sys
import os
from pathlib import Path

# Add the app directory to Python path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from sqlalchemy import text, create_engine
from sqlalchemy.orm import sessionmaker
from app.db.database import get_database_url
from app.logger import logging

def run_migration():
    """Run the migration to add status and plan fields"""
    
    print("🚀 Starting migration: Add user status and plan fields")
    print("=" * 60)
    
    try:
        # Get database connection
        database_url = get_database_url()
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("✅ Connected to database")
            
            # Step 1: Check if columns already exist
            print("\n📋 Step 1: Checking existing columns...")
            
            check_status_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'status'
            """)
            
            check_plan_query = text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'plan'
            """)
            
            status_exists = db.execute(check_status_query).fetchone()
            plan_exists = db.execute(check_plan_query).fetchone()
            
            if status_exists and plan_exists:
                print("⚠️  Both columns already exist. Migration not needed.")
                return
            
            # Step 2: Add status column if not exists
            if not status_exists:
                print("➕ Adding 'status' column...")
                add_status_query = text("""
                    ALTER TABLE users 
                    ADD COLUMN status VARCHAR(20) DEFAULT 'active' NOT NULL
                """)
                db.execute(add_status_query)
                print("✅ Status column added")
            else:
                print("✅ Status column already exists")
            
            # Step 3: Add plan column if not exists
            if not plan_exists:
                print("➕ Adding 'plan' column...")
                add_plan_query = text("""
                    ALTER TABLE users 
                    ADD COLUMN plan VARCHAR(20) DEFAULT 'trial' NOT NULL
                """)
                db.execute(add_plan_query)
                print("✅ Plan column added")
            else:
                print("✅ Plan column already exists")
            
            # Step 4: Update existing users with default values
            print("\n📝 Step 4: Updating existing users with default values...")
            
            update_existing_query = text("""
                UPDATE users 
                SET status = 'active', plan = 'trial' 
                WHERE status IS NULL OR plan IS NULL
            """)
            
            result = db.execute(update_existing_query)
            updated_count = result.rowcount
            print(f"✅ Updated {updated_count} existing users with default values")
            
            # Step 5: Verify the changes
            print("\n🔍 Step 5: Verifying changes...")
            
            verify_query = text("""
                SELECT COUNT(*) as total_users,
                       COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
                       COUNT(CASE WHEN plan = 'trial' THEN 1 END) as trial_users
                FROM users
            """)
            
            result = db.execute(verify_query).fetchone()
            print(f"📊 Total users: {result.total_users}")
            print(f"📊 Active users: {result.active_users}")
            print(f"📊 Trial users: {result.trial_users}")
            
            # Commit all changes
            db.commit()
            print("\n✅ Migration completed successfully!")
            
    except Exception as e:
        print(f"\n❌ Migration failed: {str(e)}")
        logging.error(f"Migration error: {str(e)}")
        raise

def rollback_migration():
    """Rollback the migration (remove the added columns)"""
    
    print("🔄 Starting rollback: Remove user status and plan fields")
    print("=" * 60)
    
    try:
        # Get database connection
        database_url = get_database_url()
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            print("✅ Connected to database")
            
            # Remove status column
            print("➖ Removing 'status' column...")
            remove_status_query = text("ALTER TABLE users DROP COLUMN IF EXISTS status")
            db.execute(remove_status_query)
            print("✅ Status column removed")
            
            # Remove plan column
            print("➖ Removing 'plan' column...")
            remove_plan_query = text("ALTER TABLE users DROP COLUMN IF EXISTS plan")
            db.execute(remove_plan_query)
            print("✅ Plan column removed")
            
            # Commit changes
            db.commit()
            print("\n✅ Rollback completed successfully!")
            
    except Exception as e:
        print(f"\n❌ Rollback failed: {str(e)}")
        logging.error(f"Rollback error: {str(e)}")
        raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='User status and plan migration')
    parser.add_argument('--rollback', action='store_true', help='Rollback the migration')
    
    args = parser.parse_args()
    
    if args.rollback:
        rollback_migration()
    else:
        run_migration()
