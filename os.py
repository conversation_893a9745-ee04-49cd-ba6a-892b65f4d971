import platform
import os
import subprocess

def get_system_info():
    info = {
        "System": platform.system(),
        "Node Name": platform.node(),
        "Release": platform.release(),
        "Version": platform.version(),
        "Machine": platform.machine(),
        "Processor": platform.processor(),
        "Architecture": platform.architecture(),
    }

    # Optional: Get distribution info (for Linux)
    try:
        distro = subprocess.check_output("lsb_release -a", shell=True, stderr=subprocess.DEVNULL).decode()
        info["Distribution Info"] = distro
    except Exception:
        info["Distribution Info"] = "lsb_release not available"

    return info

if __name__ == "__main__":
    info = get_system_info()
    for key, value in info.items():
        print(f"{key}: {value}")
