uvicorn>=0.15.0
pydantic>=1.8.0
python-dotenv>=0.19.0
requests>=2.26.0
beautifulsoup4>=4.9.3
selenium>=4.1.0
webdriver-manager>=3.5.2
openai>=1.0.0
redis>=4.0.0
python-multipart>=0.0.5
aiofiles>=0.7.0
tenacity>=8.0.1
lxml>=4.9.0
pytest>=7.0.0
pytest-asyncio>=0.18.0 
psycopg2-binary>=2.9.5
sqlalchemy>=1.4.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
fastapi[all]>=0.68.0
IPython
torch
torchvision
ortools
pdfplumber==0.11.0
pdfminer.six


# # Core
# uvicorn>=0.15.0
# pydantic>=1.8.0
# python-dotenv>=0.19.0
# requests>=2.26.0
# beautifulsoup4>=4.9.3
# selenium>=4.1.0
# webdriver-manager>=3.5.2
# openai>=1.0.0
# redis>=4.0.0
# python-multipart>=0.0.5
# aiofiles>=0.7.0
# tenacity>=8.0.1
# lxml>=4.9.0

# # API + Auth
# psycopg2-binary>=2.9.5
# sqlalchemy>=1.4.0
# passlib[bcrypt]>=1.7.4
# python-jose[cryptography]>=3.3.0
# fastapi[all]>=0.68.0
# email-validator

# # NLP / Utility
# orjson
# starlette
# anyio
# joblib
# rich
# aiohttp
# pymysql
# markdown
# rapidfuzz
# gensim
# tqdm
# fuzzywuzzy

# # PDF
# pdfminer.six
# pdfplumber==0.11.0
# pypdf
# spacy

# # Optional / Manual if needed (for ARM64 issues)
# torch
# torchvision
# ortools
