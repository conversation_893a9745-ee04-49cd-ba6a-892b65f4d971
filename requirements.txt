beautifulsoup4==4.13.4
celery==5.5.3
fastapi==0.116.1
PyMuPDF
httpx==0.28.1
nltk==3.9.1
numpy==2.3.1
openai==1.97.1
ortools==9.14.6206
pandas==2.3.1
passlib==1.7.4
pdfplumber==0.11.0
pydantic==2.11.7
PyPDF2==3.0.1
python-dotenv==1.1.1
python_dateutil==2.9.0.post0
python_jose==3.5.0
pytz==2025.2
rapidfuzz==3.13.0
redis==6.2.0
Requests==2.32.4
scikit_learn==1.7.1
selenium==4.34.2
sentence_transformers==5.0.0
skillner==1.0.3
spacy==3.8.7
SQLAlchemy==2.0.41
starlette==0.47.2
tenacity==9.1.2
torch==2.7.1
webdriver_manager==4.0.2
undetected-chromedriver
python-docx==1.2.0



# for the gateway error
# pip install celery[redis]
# sudo apt-get install redis-server
# sudo systemctl start redis-server
# sudo systemctl enable redis-server


# # Core
# uvicorn>=0.15.0
# pydantic>=1.8.0
# python-dotenv>=0.19.0
# requests>=2.26.0
# beautifulsoup4>=4.9.3
# selenium>=4.1.0
# webdriver-manager>=3.5.2
# openai>=1.0.0
# redis>=4.0.0
# python-multipart>=0.0.5
# aiofiles>=0.7.0
# tenacity>=8.0.1
# lxml>=4.9.0

# # API + Auth
# psycopg2-binary>=2.9.5
# sqlalchemy>=1.4.0
# passlib[bcrypt]>=1.7.4
# python-jose[cryptography]>=3.3.0
# fastapi[all]>=0.68.0
# email-validator

# # NLP / Utility
# orjson
# starlette
# anyio
# joblib
# rich
# aiohttp
# pymysql
# markdown
# rapidfuzz
# gensim
# tqdm
# fuzzywuzzy

# # PDF
# pdfminer.six
# pdfplumber==0.11.0
# pypdf
# spacy

# # Optional / Manual if needed (for ARM64 issues)
# torch
# torchvision
# ortools