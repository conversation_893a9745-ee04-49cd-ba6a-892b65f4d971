{"success": true, "message": "", "data": {"id": "4256734127", "state": "LISTED", "title": "Generative AI Engineer", "description": "About UsAt Arbor, we’re not just building tools—we’re reshaping the future of the home improvement and building materials industry. Our mission is to revolutionize how businesses operate by automating complex processes, optimizing supply chains, and building a resilient, intelligent marketplace powered by advanced large language models (LLMs) and machine learning. If you're passionate about pushing AI boundaries, creating transformative applications, and seeing the direct impact of your work, we’d love for you to join our innovative team.\nWhat You’ll DoAs a Generative AI Engineer, you’ll play a critical role in advancing our AI capabilities. You’ll design, develop, and deploy applications powered by state-of-the-art LLMs that enhance every aspect of our platform, from demand forecasting to intelligent customer support and product recommendations.\nKey Responsibilities\nDevelop AI-Driven Applications: Lead the development of generative AI applications using LLMs, directly impacting key areas like demand forecasting, product recommendations, and real-time customer support.Experiment and Innovate: Explore new LLM architectures and techniques to enhance application performance and adaptability. This includes prompt engineering, model fine-tuning, and incorporating external data to improve prediction accuracy.Collaborate with Cross-Functional Teams: Partner closely with product, design, and operations teams to turn business goals into AI-powered solutions that make a measurable difference in user experience and operational efficiency.Optimize for Scale and Performance: Leverage advanced optimization and machine learning techniques to build solutions that are efficient, scalable, and cost-effective while maintaining top-tier performance standards.Champion AI Best Practices: Establish best practices in AI model development, deployment, and documentation to ensure quality and reproducibility as we scale our engineering team.\nWho You AreAI Visionary: You’re passionate about the potential of AI and excited to lead the charge in generative technology that will shape the future of housing and construction.Experienced with LLMs: You bring extensive experience working with large language models (e.g., GPT-4, BERT) and generative AI applications, with a track record of deploying and optimizing LLM-based systems.Problem-Solver: You thrive in a startup environment and are adept at tackling complex, ambiguous challenges with a solutions-first mindset.Data-Driven and Analytical: Skilled at working with large datasets, you experiment with model architectures and measure outcomes to drive continuous improvement.Collaborative Team Player: You communicate technical concepts effectively across disciplines and enjoy working in a dynamic, cross-functional environment.Preferred Qualifications4+ years of experience in machine learning or AI, with a focus on generative AI or natural language processing.Technical Proficiency: Advanced skills in Python and ML frameworks like PyTorch, TensorFlow, and Hugging Face Transformers.Production Experience: Hands-on experience deploying and optimizing ML models in production.ML Ops & Cloud Proficiency: Familiarity with ML Ops practices and cloud services (AWS, GCP, Azure).\nWhy Join Arbor?\nImpactful Work: Be part of a mission to revolutionize the construction and home improvement industries through pioneering AI solutions. Your work will shape the future of housing and make a tangible impact on people’s lives.Unmatched Opportunity: Join at a pivotal stage of growth in a trillion-dollar industry with vast, untapped potential. Here, you’ll have the freedom to innovate and contribute to industry-defining solutions.Growth and Development: We are committed to investing in our team’s professional development and growth. You’ll have ample opportunities to lead, innovate, and grow as we scale.Dynamic Culture: Work in a fast-paced, agile environment where creativity, efficiency, and collaboration are celebrated.Competitive Package: We offer a competitive salary, equity options, flexible working hours, and comprehensive benefits.", "url": "https://www.linkedin.com/jobs/view/4256734127/", "applyMethod": {"companyApplyUrl": "", "easyApplyUrl": "https://www.linkedin.com/job-apply/4256734127"}, "company": {"id": 18578562, "name": "Arbor ", "universalName": "arborsmarthome", "description": "Arbor is a Home Improvements Technology Startup.\n\nThe 21st Century Way To Shop For Your Home. \n\nWe're the future of home improvement shopping. \n\nArbor is the only company that combines software and artificial intelligence with human insight to power an industry that touches all of us. We challenge every part of home improvement shopping in the existing market because we believe a better online shopping experience will lead to a better life.", "logo": "https://media.licdn.com/dms/image/v2/C560BAQHd-Uar2Nb27Q/company-logo_200_200/company-logo_200_200/0/1631396201375?e=1756944000&v=beta&t=S7yuqfKrAik01LOzkdxL-SIC0KZy-CXRYCWbGI5n4PI", "url": "https://www.linkedin.com/company/arborsmarthome", "followerCount": 42186, "staffCount": 37, "staffCountRange": {"start": 11, "end": 50}, "industries": ["Software Development"], "headquarter": {"geographicArea": "California", "country": "US", "city": "Palo Alto", "line1": "Bryant St"}}, "contentLanguage": {"code": "EN", "name": "English"}, "location": "San Francisco Bay Area", "type": "Full-time", "applies": 434, "views": 779, "workPlace": "Hybrid", "expireAt": 1766560096000, "jobFunctions": ["ENG", "IT"], "industries": [4], "listedAt": 1751008096000, "listedAtDate": "2025-06-27 07:08:16 +0000 UTC", "originalListedAt": 1751008096000, "originalListedDate": "2025-06-27 07:08:16 +0000 UTC"}}