{"source": "Generic", "title": "Software Development Engineer, North America Stores Expansion - Job ID: 3022007 | Amazon.jobs", "paragraphs": ["Job ID: 3022007 | Amazon.com Services LLC", "As an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalent", "- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degree", "- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site."], "job_descriptions": ["Amazon Jobs home pageSearch for jobs by title or keywordsearch job by locationMy careerMy applicationsMy profileAccount securitySettingsSign outAmazon Jobs home pagecancelSearch for jobs by title or keywordsearch job by locationSearch jobsSoftware Development Engineer, North America Stores ExpansionJob ID: 3022007 | Amazon.com Services LLCApply nowDESCRIP<PERSON><PERSON><PERSON> an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.Job detailsUSA, WA, SeattleUS Home and Lifestyle, Canada RetailSoftware DevelopmentShare this jobJOIN US ONFind CareersJob CategoriesTeamsLocationsUS and EU Military recruitingWarehouse and Hourly JobsWorking At AmazonCultureBenefitsAmazon NewsletterInclusive experiencesOur leadership principlesHelpFAQInterview tipsReview application statusDisability accommodationsLegal disclosures and noticesEnglishČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文ČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文Amazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Privacy and DataImpressum© 1996-2025, Amazon.com, Inc. or its affiliates", "Amazon Jobs home pageSearch for jobs by title or keywordsearch job by locationMy careerMy applicationsMy profileAccount securitySettingsSign outAmazon Jobs home pagecancelSearch for jobs by title or keywordsearch job by locationSearch jobsSoftware Development Engineer, North America Stores ExpansionJob ID: 3022007 | Amazon.com Services LLCApply nowDESCRIP<PERSON><PERSON><PERSON> an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.Job detailsUSA, WA, SeattleUS Home and Lifestyle, Canada RetailSoftware DevelopmentShare this job", "Software Development Engineer, North America Stores ExpansionJob ID: 3022007 | Amazon.com Services LLCApply nowDESCRIPTIONAs an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.Job detailsUSA, WA, SeattleUS Home and Lifestyle, Canada RetailSoftware DevelopmentShare this job", "DESCRI<PERSON><PERSON><PERSON>As an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.Job detailsUSA, WA, SeattleUS Home and Lifestyle, Canada RetailSoftware DevelopmentShare this job", "DESCRI<PERSON><PERSON><PERSON>As an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.Job detailsUSA, WA, SeattleUS Home and Lifestyle, Canada RetailSoftware DevelopmentShare this job", "DESCRI<PERSON><PERSON><PERSON>As an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.", "DESCRI<PERSON><PERSON><PERSON>As an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentBASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degreePREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.", "DESCRI<PERSON><PERSON><PERSON>As an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.You will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.Basic qualifications- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming languagePreferred qualifications- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalent", "BASIC QUALIFICATIONS- 3+ years of non-internship professional software development experience- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience- Experience programming with at least one software programming language- Bachelor's degree", "PREFERRED QUALIFICATIONS- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience- Bachelor's degree in computer science or equivalentAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Our inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visithttps://amazon.jobs/content/en/how-we-hire/accommodationsfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.Our compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visithttps://www.aboutamazon.com/workplace/employee-benefits. This position will remain posted until filled. Applicants should apply via our internal or external career site.", "JOIN US ONFind CareersJob CategoriesTeamsLocationsUS and EU Military recruitingWarehouse and Hourly JobsWorking At AmazonCultureBenefitsAmazon NewsletterInclusive experiencesOur leadership principlesHelpFAQInterview tipsReview application statusDisability accommodationsLegal disclosures and noticesEnglishČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文ČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文Amazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.Privacy and DataImpressum© 1996-2025, Amazon.com, Inc. or its affiliates", "Find CareersJob CategoriesTeamsLocationsUS and EU Military recruitingWarehouse and Hourly JobsWorking At AmazonCultureBenefitsAmazon NewsletterInclusive experiencesOur leadership principlesHelpFAQInterview tipsReview application statusDisability accommodationsLegal disclosures and noticesEnglishČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文ČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文", "Find CareersJob CategoriesTeamsLocationsUS and EU Military recruitingWarehouse and Hourly JobsWorking At AmazonCultureBenefitsAmazon NewsletterInclusive experiencesOur leadership principlesHelpFAQInterview tipsReview application statusDisability accommodationsLegal disclosures and noticesEnglishČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文ČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文", "Find CareersJob CategoriesTeamsLocationsUS and EU Military recruitingWarehouse and Hourly JobsWorking At AmazonCultureBenefitsAmazon NewsletterInclusive experiencesOur leadership principlesHelpFAQInterview tipsReview application statusDisability accommodationsLegal disclosures and noticesEnglishČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文ČeštinaDeutschEnglishEnglish, BritishEspañolFrançaisItaliano日本語PolskiPortuguês, Brasil简体中文"], "job_requirements": ["HomeTeamsLocationsJob categoriesMy careerMy applicationsMy profileAccount securitySettingsSign outResourcesDisability accommodationsBenefitsInclusive experiencesInterview tipsLeadership principlesWorking at AmazonFAQ", "My applicationsMy profileAccount securitySettingsSign out", "Disability accommodationsBenefitsInclusive experiencesInterview tipsLeadership principlesWorking at AmazonFAQ", "USA, WA, SeattleUS Home and Lifestyle, Canada RetailSoftware Development", "USA, WA, Seattle", "US Home and Lifestyle, Canada Retail", "Software Development", "", "", "Job CategoriesTeamsLocationsUS and EU Military recruitingWarehouse and Hourly Jobs", "CultureBenefitsAmazon NewsletterInclusive experiencesOur leadership principles", "FAQInterview tipsReview application statusDisability accommodationsLegal disclosures and notices"], "full_text": "Software Development Engineer, North America Stores Expansion - Job ID: 3022007 | Amazon.jobs\nSkip to main content\nHome\nTeams\nLocations\nJob categories\nMy career\nMy applications\nMy profile\nAccount security\nSettings\nSign out\nResources\nDisability accommodations\nBenefits\nInclusive experiences\nInterview tips\nLeadership principles\nWorking at Amazon\nFAQ\n×\nAmazon Jobs home page\nSearch for jobs by title or keyword\nsearch job by location\nMy career\nMy applications\nMy profile\nAccount security\nSettings\nSign out\nAmazon Jobs home page\ncancel\nSearch for jobs by title or keyword\nsearch job by location\nSearch jobs\nSoftware Development Engineer, North America Stores Expansion\nJob ID: 3022007 | Amazon.com Services LLC\nApply now\nDESCRIPTION\nAs an Amazon Software Development Engineer, you will solve unique and complex problems at a rapid pace, utilizing the latest technologies to create solutions that are highly scalable. You will find that there is an unlimited amount of opportunities within Amazon, where developing your career across a wide range of teams is highly supported. We are committed to making your work experience as enjoyable as the experiences you’ll be creating for our customers.\nYou will own the front-end, back-end, or full stack design and development of product features, building scale, efficiency, and differentiated customer experiences. We’re looking for software engineers passionate about building software solutions end-to-end, have strong software development experience delivering at scale solutions, and systems design skills. You should have a demonstrated ability delivering within a DevOps delivery model from scoping requirements, requirement analysis, design, development, test, CI/CD, security implementation, and operational excellence with the ability to work cross-functionally with Product Managers, business stakeholders and other tech teams through the actual launch of the project. You should also have experience in communicating with users, other technical teams, and management to collect requirements, describe software product features, and technical designs.\nBasic qualifications\n- 3+ years of non-internship professional software development experience\n- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience\n- Experience programming with at least one software programming language\nPreferred qualifications\n- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience\n- Bachelor's degree in computer science or equivalent\nBASIC QUALIFICATIONS\n- 3+ years of non-internship professional software development experience\n- 2+ years of non-internship design or architecture (design patterns, reliability and scaling) of new and existing systems experience\n- Experience programming with at least one software programming language\n- Bachelor's degree\nPREFERRED QUALIFICATIONS\n- 3+ years of full software development life cycle, including coding standards, code reviews, source control management, build processes, testing, and operations experience\n- Bachelor's degree in computer science or equivalent\nAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.\nOur inclusive culture empowers Amazonians to deliver the best results for our customers. If you have a disability and need a workplace accommodation or adjustment during the application and hiring process, including support for the interview or onboarding process, please visit\nhttps://amazon.jobs/content/en/how-we-hire/accommodations\nfor more information. If the country/region you’re applying in isn’t listed, please contact your Recruiting Partner.\nOur compensation reflects the cost of labor across several US geographic markets. The base pay for this position ranges from $129,300/year in our lowest geographic market up to $223,600/year in our highest geographic market. Pay is based on a number of factors including market location and may vary depending on job-related knowledge, skills, and experience. Amazon is a total compensation company. Dependent on the position offered, equity, sign-on payments, and other forms of compensation may be provided as part of a total compensation package, in addition to a full range of medical, financial, and/or other benefits. For more information,  please visit\nhttps://www.aboutamazon.com/workplace/employee-benefits\n. This position will remain posted until filled. Applicants should apply via our internal or external career site.\nJob details\nUSA, WA, Seattle\nUS Home and Lifestyle, Canada Retail\nSoftware Development\nShare this job\nJOIN US ON\nFind Careers\nJob Categories\nTeams\nLocations\nUS and EU Military recruiting\nWarehouse and Hourly Jobs\nWorking At Amazon\nCulture\nBenefits\nAmazon Newsletter\nInclusive experiences\nOur leadership principles\nHelp\nFAQ\nInterview tips\nReview application status\nDisability accommodations\nLegal disclosures and notices\nEnglish\nČeština\nDeutsch\nEnglish\nEnglish, British\nEspañol\nFrançais\nItaliano\n日本語\nPolski\nPortuguês, Brasil\n简体中文\nČeština\nDeutsch\nEnglish\nEnglish, British\nEspañol\nFrançais\nItaliano\n日本語\nPolski\nPortuguês, Brasil\n简体中文\nAmazon is an equal opportunity employer and does not discriminate on the basis of protected veteran status, disability, or other legally protected status.\nPrivacy and Data\nImpressum\n© 1996-2025, Amazon.com, Inc. or its affiliates"}