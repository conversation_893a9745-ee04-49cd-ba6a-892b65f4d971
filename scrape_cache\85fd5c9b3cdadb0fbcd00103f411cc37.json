{"success": true, "message": "response retrieved successfully", "data": {"job_info": {"title": "Senior REACT Developer – IoT Platform (Freelancer, Offshore)", "description": "Senior REACT Developer – IoT Platform (Freelancer, Offshore-Team)\nAre you an experienced, hands-on developer ready to take on a critical leadership role in our offshore developer team? Are you highly skilled and passionate about crafting intuitive and highly interactive graphical low-code tools for cutting-edge B2B applications? We are seeking a Senior React Developer to lead the frontend development of our innovative IoT SaaS platform. This role is key to the success of our mission and requires a proactive, \"doer\" mindset. About Us:Peeriot GmbH is a German DeepTech software startup at the seed stage, dedicated to revolutionizing technology with a decentralized IoT platform. Our focus lies on creating scalable, peer-to-peer systems that bring IoT solutions to a new level of reliability and usability. Our team of top-tier talent shares a vision to innovate and push boundaries. Join us to play a pivotal role in shaping tomorrow's technology. We are planning to build up an offshore development team for our front-end topics. You will be the core of this new team. Grow with us! Role Overview:As a Senior React Developer, you will lead the development of the highly interactive low-code tools forming the core of our SaaS platform. The backend, developed in Rust, provides a powerful foundation, while your expertise in React will drive the creation of a powerful user-focused experience. In this hands-on role, you will provide technical leadership, ensuring the architecture, design, implementation, testing, and documentation of the frontend aligned with best practices in modern web development. You will also collaborate closely with backend engineers, UX designers, and other stakeholders to build a product. Key Responsibilities:Act as the Technical Lead for frontend development, defining, documenting, and implementing the UI/UX architecture and roadmap.Design, develop, test, and maintain the React-based interface for highly interactive low-code tools.Collaborate with backend engineers to integrate APIs and ensure a seamless experience between the Rust backend and React frontend.Develop complex graphical user interfaces, including drag-and-drop features, real-time updates, and intuitive workflows.Optimize performance for speed, responsiveness, and scalability across different devices and screen sizes.Implement robust CI/CD automation pipelines to streamline development, testing, and deployment.Stay up to date with the latest React and frontend development trends, ensuring best practices are followed. Key Requirements:Living in: Eastern Europe, South-East Asia8+ years of software development experience, with at least 6 years specializing in REACT for B2B applications.Strong expertise in JavaScript/TypeScript, React, and component-based architecture.Experience with highly interactive graphical interfaces, such as low-code platforms, dashboards, or data visualization tools.Proven experience working with RESTful and WebSocket APIs.Hands-on experience in CI/CD automation for frontend applications.Deep understanding of frontend performance optimization, accessibility, and best UI/UX practices.Excellent problem-solving skills, proactive mindset, and the ability to take initiative.Excellent communication skills in English to effectively lead and collaborate in a remote, cross-functional environment. Nice to Have:Experience with Domain-Driven Design principles.Knowledge of Semantic Technologies, such as SPARQL and OWL2. Why This Role Matters:This is a mission-critical role for Peeriot GmbH, requiring a hands-on leader who can define and deliver on a bold technical vision. We need a \"doer\" who thrives in a fast-paced startup environment, takes ownership, and is relentless in driving high-quality results.\nBenefits:Growth Opportunities: A supportive environment fostering professional growth.Flexible Work Environment: Remote work options and flexible hours.Competitive Compensation: Attractive salary and equity participation.Innovative Culture: A dynamic team dedicated to pushing technological boundaries.Cutting-Edge Technology: Access to state-of-the-art tools and resources. Interested?If you are passionate about good user interfaces, and IoT and want to lead in developing groundbreaking solutions, we’d love to hear from you. Apply now via Linkedin (important: please implement in the question session the solution of the following mathematical task: 5+4=? With that, we making sure, you have read the job description until here), or sending your application, including salary expectations and availability, to <EMAIL>.\nPeeriot GmbH is an equal opportunity employer and encourages applications from individuals of all backgrounds.", "location": "Vietnam", "location_id": "104195383", "employment_status": "Full-time", "is_remote_allowed": true, "is_third_party_sourced": false, "listed_at": "2025-07-18T19:23:01", "expire_at": "2026-01-14T18:23:01", "job_url": "https://www.linkedin.com/jobs/view/4267494827/?trk=jobs_biz_prem_srch", "experience_level": "", "is_new": false, "applicant_tracking_system": "LinkedIn", "original_listed_at": "2025-07-18T19:23:01", "job_state": "LISTED", "workplace_types": ["REMOTE"], "country_code": "vn", "job_posting_id": 4267494827, "is_reposted": false, "job_application_limit_reached": false, "eligible_for_referrals": false}, "company_info": {"name": "<PERSON><PERSON><PERSON>", "description": "Peeriot: Decentralized IoT for a Connected Future\n\nPeeriot bridges the physical and digital worlds – decentralized, secure, and intelligent.\n\nWe help companies connect machines, sensors, and systems directly at the edge – without relying on centralized cloud infrastructure. Our platform turns distributed data streams into real-time digital twins, making operations more transparent, efficient, and resilient.\n\nThanks to our decentralized architecture, sensitive data stays where it’s generated. This not only improves response times but also enhances data sovereignty and reduces exposure to security risks.\n\nWhether it’s industrial facilities, urban infrastructure, or connected logistics – <PERSON><PERSON>iot builds a digital nervous system that continuously learns, reacts, and evolves.\n\nWe enable secure, self-organized devices, networks & digital twins. Peeriot.\n\n#decentralized #EdgeComputing #IoT #DigitalTwin #DataSovereignty\n- - - - - \n\nSite Notice:\n \nPeeriot GmbH\nPeterssteinweg 14\n04107 Leipzig\n \nCommercial Register: HRB 42078\nRegistration court: Amtsgericht Leipzig\n \nRepresented by:\n<PERSON>, <PERSON><PERSON>\n \nContact:\nPhone: +49 (0) 178 962 54 72\nE-mail: <EMAIL>\n \nVAT ID:\nSales tax identification number according to Sect. 27 a of the Sales Tax Law:\nDE351485746\n \nPerson responsible for editorial:\n<PERSON><PERSON>, Peterssteinweg 14, 04107 Leipzig\n \nEU dispute resolution:\nThe European Commission provides a platform for online dispute resolution (ODR): https://ec.europa.eu/consumers/odr/. Our e-mail address can be found above in the site notice.\n \nDispute resolution proceedings in front of a consumer arbitration board:\nWe are not willing or obliged to participate in dispute resolution proceedings in front of a consumer arbitration board.", "staff_count": 13, "industries": ["IT Services and IT Consulting"], "universal_name": "peeriot", "headquarters": {"geographicArea": "Sachsen", "country": "DE", "city": "Leipzig", "postalCode": "04107", "$type": "com.linkedin.common.Address"}, "url": "https://www.linkedin.com/company/peeriot", "logo_url": "https://media.licdn.com/dms/image/v2/D4E0BAQHrDhOhbaT-Gg/company-logo_200_200/company-logo_200_200/0/1694378056023/peeriot_logo?e=1756339200&v=beta&t=jywf0CEZCTq94PZGRJqt5jM2Dzos1joq9BBgOxm84hQ", "background_cover_url": "https://media.licdn.com/dms/image/v2/D563DAQGrW4MUDBHqPg/image-scale_191_1128/image-scale_191_1128/0/1669992306325/peeriot_cover?e=1753801200&v=beta&t=0V5WSnbt7UUu6MhqVuGvO2Xgq6RkvzJ2F4CxlTQKPdo"}, "apply_details": {"total_applies": 29, "total_views": 385, "is_application_limit_reached": false}}}