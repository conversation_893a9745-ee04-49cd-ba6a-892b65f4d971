{"success": true, "message": "", "data": {"id": "4260820877", "state": "LISTED", "title": "Django Software Engineer with GIS - Mapping / Python / APIs", "description": "Django Software Engineer with GIS - Mapping / Python / APIs\nDjango focussed Full Stack Engineer with GIS skills such as ArcGIS, QGIS, ESRI, Google Earth Engine, Mapbox, Carto, Mapinfo etc, needed for an AI Spacetech firm asap.The role is hybrid (Houston office, 2-3 days/week).\nWhy join?Salary up to $150,000, dependent on experience and qualifications.Opportunity for equity in an innovative, growing company.Enrollment in a company-matched 401k plan.Flexible PTO and company-observed holidays.Comprehensive H/D/V coverage with employer contributions.Life and disability insurances (short & long-term).Engage with cutting-edge software development in a challenging and rewarding field.\nThe roleDesign and implement intuitive, high-performance interfaces (APIs and UIs) for complex GIS and software applications including mapping satellite paths.Build, test, and deploy robust web-based platforms (Python, Django, JavaScript) that support intelligent, data-intensive systems.Work closely with multidisciplinary teams to identify and implement key enhancements aligned with project objectives.Analyze, interpret, and manage large datasets to inform software development and improvements.\nThe essential requirementsAuthorized to work in the United States on ITAR projects (US Citizen or Permanent Resident).2+ years of professional software engineering experience, with strong proficiency in Python and its frameworks including Django.GIS skills in software such as ArcGIS, QGIS, ESRI, Mapbox, Carto, Google Earth Engine, Mapinfo etcVue.js or React or Angular experienceAPI knowledge (e.g., REST, GraphQL) and integration best practices.Familiarity with software testing principles and tools (backend testing; frontend testing knowledge is a plus).Ability to collaborate effectively within a team on technically intricate projects.Strong analytical abilities and a proactive problem-solving mindset.Experience deploying applications using cloud infrastructure (e.g., AWS, Azure, GCP).\nWhat will make you stand outExperience in Aerospace, space tech or satellite technology.Proven initiative in managing software features through the full development lifecycle.A genuine interest in technological innovation and its application to solve complex real-world problems.\nIf you are interested in this role, please apply with your resume through this site.\nSEO Keywords for SearchPython, Django, Software Engineer, ArcGIS, QGIS, ESRI, Mapbox, Carto, Google Earth Engine, Mapinfo, Full Stack Engineer, Python Developer, Django Developer, JavaScript Developer, API Developer, AI Engineer, Web Developer, Software Developer, Mid Level Software Engineer, Houston Software Engineer, Python, Django, JavaScript, Vue.js, GraphQL, REST, APIs, Pytest, Google Earth Engine, ArcGIS, QGIS, ESRI, Mapbox, Carto, Mapinfo, Vitest, AWS, Azure, GCP, Cloud Computing, Machine Learning, AI, Data Intensive Applications, Intelligent Systems, UI Developer, UX Developer, Fullstack Developer, Tech Jobs Houston, GIS, Geospatial.\nDisclaimerAttis Global Ltd is an equal opportunities employer. No terminology in this advert is intended to discriminate on any of the grounds protected by law, and all qualified applicants will receive consideration for employment without regard to age, sex, race, national origin, religion or belief, disability, pregnancy and maternity, marital status, political affiliation, socio-economic status, sexual orientation, gender, gender identity and expression, and/or gender reassignment. M/F/D/V. We operate as a staffing agency and employment business. More information can be found at attisglobal.com.", "url": "https://www.linkedin.com/jobs/view/4260820877/", "applyMethod": {"companyApplyUrl": "", "easyApplyUrl": "https://www.linkedin.com/job-apply/4260820877"}, "company": {"id": *********, "name": "<PERSON><PERSON>", "universalName": "attisglobal", "description": "Attis are an AI, Data & Software Executive Search & Recruitment company dedicated to connecting the Geospatial sector across the USA & UK with the talent they need to drive positive change.\n\nExpertise in:\n\n📡 EO & Remote Sensing\n🛰️ Space Operations\n🗺️ GIS and GEOINT\n📈 Climate & Weather \n\nWith a combined 25 years’ experience recruiting in the sector, we understand that technology can be a powerful force for good, and believe that businesses striving to make this change deserve a talent partner who share in their mission.\n\nThis is why we founded Attis, with the purpose of “Positively Impacting People and Planet”.\n\nIn order to reach the people and causes that need it most, we are partnered with B1G1 as a Business for Good, which enables our business to create positive worldwide impacts for a better future.\n\nIf you’re someone looking to join a business where your work makes a genuine impact, or a business looking to hire the talent to drive your business forward on its mission, then we can help. \n\nDo you want to make a bigger impact?\nDo you want to find out how we can help?\n\nGet in touch below:\n\nUSA\n<EMAIL>\n(+1) 917-672-3688\n\nUK\n<EMAIL>\n+44 (0)204 515 5959", "logo": "https://media.licdn.com/dms/image/v2/D4E0BAQGhuAo2Kkk9UA/company-logo_200_200/company-logo_200_200/0/1705871020545/attisglobal_logo?e=1757548800&v=beta&t=drwJwhpHPdNJE0-LJWCYmUJVXRrPPrU9QAOl537Qfoc", "url": "https://www.linkedin.com/company/attisglobal", "followerCount": 23034, "staffCount": 9, "staffCountRange": {"start": 2, "end": 10}, "industries": ["Staffing and Recruiting"], "headquarter": {"country": "GB", "city": "Manchester"}}, "contentLanguage": {"code": "EN", "name": "English"}, "location": "Houston, TX", "type": "Full-time", "applies": 94, "views": 265, "workPlace": "Hybrid", "expireAt": 1754041409000, "formattedJobFunctions": ["Information Technology", "Engineering", "Other"], "jobFunctions": ["IT", "ENG", "OTHR"], "industries": [1, 3089, 1594], "formattedIndustries": ["Defense and Space Manufacturing", "Space Research and Technology", "Technology, Information and Media"], "formattedExperienceLevel": "Associate", "listedAt": 1751449410000, "listedAtDate": "2025-07-02 09:43:30 +0000 UTC", "originalListedAt": 1751449410000, "originalListedDate": "2025-07-02 09:43:30 +0000 UTC"}}