{"source": "LinkedIn Scrape", "title": "Security Intelligence Developer", "location": "Ho Chi Minh City, Vietnam", "company": "N/A", "description": "Job Title:\nSecurity Intelligence Developer (Freshers & Early-Career)\nLocation:\nVietnam| Onsite\nExperience:\n04 years\nEmployment Type:\nFull-Time\nAbout the Role\nWe are looking for highly motivated individuals, freshers or professionals with up to 4 years of experience, who are passionate about cybersecurity, curious about threat intelligence, and eager to contribute to real-world impact. You will work on developing and enriching security content using OVAL, Python, and other data formats, while keeping pace with emerging threats and technologies.\nWhat You will Be Doing\n Develop and maintain security checks using OVAL (Open Vulnerability and Assessment Language)\n Write, structure, and refine intelligence feeds in JSON and other machine-readable formats\n Perform research on vulnerabilities, CVEs, misconfigurations, and emerging security topics\n Collaborate with researchers to convert vulnerability analysis into actionable detection logic\n Explore AI/ML use cases for automating security content creation\n Contribute to internal and external blogs and documentation\n Work on bulk data creation, testing, and validation in a fast-paced, delivery-oriented setup\nWhat You Should Bring\n Strong programming fundamentals, preferably in Java or Python\n Interest in cybersecurity, threat research, or security standards like OVAL/CVE/CWE\n Patience and discipline to handle repetitive, detail-oriented tasks with consistency\n Excellent written communication, ability to write clearly and concisely for blogs or technical documentation in English\n Willingness to learn and adapt quickly\n (For 24 years experience) Exposure to structured data formats (JSON/YAML), scripting for automation, or security research preferred\nNice to Have\n Familiarity with vulnerability databases (like NVD), STIX/TAXII, or MITRE ATT&CK\n Knowledge of AI or automation techniques to streamline data generation\n Experience with Git, SVN or other version control, and content pipelines\nWhy Join Us?\n Hands-on exposure to the inner workings of threat detection and vulnerability intelligence\n An opportunity to work at the intersection of cybersecurity and research\n Interact with teams globally that values precision, speed, and growth\n Flat hierarchy, strong mentorship, and cross-functional learning opportunities\nShow more\nShow less", "full_text": "SecPod hiring Security Intelligence Developer in Ho Chi Minh City, Vietnam | LinkedIn\nSkip to main content\nLinkedIn\nSecurity Intelligence Developer in Ho Chi Minh City\nExpand search\nJobs\nThis button displays the currently selected search type. When expanded it provides a list of search options that will switch the search inputs to match the current selection.\nJobs\nPeople\nLearning\nClear text\nClear text\nClear text\nClear text\nClear text\nJoin now\nSign in\nSecurity Intelligence Developer\nSecPod\nHo Chi Minh City, Vietnam\nApply\nSecurity Intelligence Developer\nSecPod\nHo Chi Minh City, Vietnam\n2 weeks ago\n53 applicants\nSee who <PERSON><PERSON><PERSON><PERSON> has hired for this role\nApply\nSave\nReport this job\nDirect message the job poster from SecPod\n<PERSON><PERSON>k\nAd<PERSON>hakti <PERSON>\nHR Manager | Employee Relations Expert | Driving Employees Growth\nJob Title:\nSecurity Intelligence Developer (Freshers & Early-Career)\nLocation:\nVietnam| Onsite\nExperience:\n04 years\nEmployment Type:\nFull-Time\nAbout the Role\nWe are looking for highly motivated individuals, freshers or professionals with up to 4 years of experience, who are passionate about cybersecurity, curious about threat intelligence, and eager to contribute to real-world impact. You will work on developing and enriching security content using OVAL, Python, and other data formats, while keeping pace with emerging threats and technologies.\nWhat You will Be Doing\n Develop and maintain security checks using OVAL (Open Vulnerability and Assessment Language)\n Write, structure, and refine intelligence feeds in JSON and other machine-readable formats\n Perform research on vulnerabilities, CVEs, misconfigurations, and emerging security topics\n Collaborate with researchers to convert vulnerability analysis into actionable detection logic\n Explore AI/ML use cases for automating security content creation\n Contribute to internal and external blogs and documentation\n Work on bulk data creation, testing, and validation in a fast-paced, delivery-oriented setup\nWhat You Should Bring\n Strong programming fundamentals, preferably in Java or Python\n Interest in cybersecurity, threat research, or security standards like OVAL/CVE/CWE\n Patience and discipline to handle repetitive, detail-oriented tasks with consistency\n Excellent written communication, ability to write clearly and concisely for blogs or technical documentation in English\n Willingness to learn and adapt quickly\n (For 24 years experience) Exposure to structured data formats (JSON/YAML), scripting for automation, or security research preferred\nNice to Have\n Familiarity with vulnerability databases (like NVD), STIX/TAXII, or MITRE ATT&CK\n Knowledge of AI or automation techniques to streamline data generation\n Experience with Git, SVN or other version control, and content pipelines\nWhy Join Us?\n Hands-on exposure to the inner workings of threat detection and vulnerability intelligence\n An opportunity to work at the intersection of cybersecurity and research\n Interact with teams globally that values precision, speed, and growth\n Flat hierarchy, strong mentorship, and cross-functional learning opportunities\nShow more\nShow less\nSeniority level\nEntry level\nEmployment type\nFull-time\nJob function\nEngineering and Information Technology\nIndustries\nComputer and Network Security\nReferrals increase your chances of interviewing at SecPod by 2x\nSee who you know\nGet notified about new\nDeveloper\njobs in\nHo Chi Minh City, Vietnam\n.\nSign in to create job alert\nSimilar jobs\nDeveloper cho website/ung dung\nDeveloper cho website/ung dung\nCT\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n12 months ago\nSoftware Engineer (Ho Chi Minh)\nSoftware Engineer (Ho Chi Minh)\nCaladan\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n12 hours ago\nSoftware Developer (Vietnam)\nSoftware Developer (Vietnam)\nAzeus Systems Limited\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 month ago\nPHP Developer\nPHP Developer\nKasatria Technologies\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n12 months ago\nFrontend Software Engineer\nFrontend Software Engineer\nUpskills\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 week ago\nFresher Java Software Engineer\nFresher Java Software Engineer\nKMS Technology, Inc.\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 day ago\nJunior/Middle PHP Developer\nJunior/Middle PHP Developer\nNFQ\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n2 weeks ago\nFrontend developer\nFrontend developer\nMealSuite\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 week ago\n(Fresher/ Intern to Junior level) Software Engineer _ Platform Team\n(Fresher/ Intern to Junior level) Software Engineer _ Platform Team\nZalopay\nHo Chi Minh City, Vietnam\n2 weeks ago\nJunior Software Engineer\nJunior Software Engineer\nPostCo\nHo Chi Minh City, Vietnam\n5 days ago\nSoftware Developer Intern (Telecommunications)\nSoftware Developer Intern (Telecommunications)\nEndava\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n12 hours ago\nSoftware Engineer (Front-End)\nSoftware Engineer (Front-End)\nHubble.Build\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 month ago\nDeveloper Apparel\nDeveloper Apparel\nPUMA Group\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 week ago\nJava Developer (Junior/Middle)\nJava Developer (Junior/Middle)\nSaigon Technology - Accelerate Software Development\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 week ago\nSoftware Engineer\nSoftware Engineer\nPMAX - Total Marketing Solution\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n2 weeks ago\nJavascript Developer (React)\nJavascript Developer (React)\nAccenture\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n2 weeks ago\nWeb Developers (PHP/.Net)\nWeb Developers (PHP/.Net)\nVidaltek\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 year ago\nJunior Backend/Full-stack Developer\nJunior Backend/Full-stack Developer\nFlowmingo AI\nHo Chi Minh City, Vietnam\n1 day ago\nSr Front-End React JS Software Developer / Engineer | 100% Remote | SaaS | Construction Tech\nSr Front-End React JS Software Developer / Engineer | 100% Remote | SaaS | Construction Tech\nClue Insights\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n6 months ago\nFrontend Developer\nFrontend Developer\nmgm technology partners Vietnam\nHo Chi Minh City, Vietnam\n1 week ago\nFullstack Developer (NodeJS,ReactJS - Middle/Senior Level)\nFullstack Developer (NodeJS,ReactJS - Middle/Senior Level)\nCodeLink\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n2 months ago\nSenior Software Engineer, Full-Stack (VN)\nSenior Software Engineer, Full-Stack (VN)\nQCP\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 month ago\nSoftware Engineer I\nSoftware Engineer I\nMiTek\nBinh Thuan, Ho Chi Minh City, Vietnam\n1 week ago\nSoftware Developer I\nSoftware Developer I\nMiTek\nBinh Thuan, Ho Chi Minh City, Vietnam\n6 days ago\nSenior Software Engineer (VN)\nSenior Software Engineer (VN)\nQCP\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 month ago\nSoftware Engineer (m/f/d)\nSoftware Engineer (m/f/d)\nZoi\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n2 hours ago\nFrontend developer\nFrontend developer\nMoatable\nHo Chi Minh City, Vietnam\n15 hours ago\nShow more jobs like this\nShow fewer jobs like this\nPeople also viewed\nFrontend Software Engineer (For AI-driven Platform)\nFrontend Software Engineer (For AI-driven Platform)\nUpskills\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n3 weeks ago\nJavascript Developer (Willing to do ServiceNow)\nJavascript Developer (Willing to do ServiceNow)\nDXC Technology Vietnam\nHo Chi Minh City, Vietnam\n3 weeks ago\nFrontend Developer\nFrontend Developer\nYum! Digital & Technology Vietnam\nHo Chi Minh City, Vietnam\n3 weeks ago\nFull-stack Engineer\nFull-stack Engineer\nHomebase (YC W21)\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n$1,500 - $3,000\n8 months ago\nJunior Developer (5G)\nJunior Developer (5G)\nEndava\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n5 days ago\nSoftware Engineer (Back-End)\nSoftware Engineer (Back-End)\nHubble.Build\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n4 months ago\nPython Developer (Middle)\nPython Developer (Middle)\nSaigon Technology - Accelerate Software Development\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n1 day ago\nSoftware Engineer\nSoftware Engineer\nWorldQuant\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n3 days ago\nFrontend Software Developer (all genders)\nFrontend Software Developer (all genders)\nHRS Group\nHo Chi Minh City, Ho Chi Minh City, Vietnam\n2 weeks ago\nFull Stack Developer\nFull Stack Developer\nmgm technology partners Vietnam\nHo Chi Minh City, Vietnam\n1 day ago\nExplore collaborative articles\nWere unlocking community knowledge in a new way. Experts add insights directly into each article, started with the help of AI.\nExplore More\nLinkedIn\n 2025\nAbout\nAccessibility\nUser Agreement\nPrivacy Policy\nCookie Policy\nCopyright Policy\nBrand Policy\nGuest Controls\nCommunity Guidelines\n (Arabic)\n (Bangla)\nCestina (Czech)\nDansk (Danish)\nDeutsch (German)\n (Greek)\nEnglish (English)\nEspanol (Spanish)\n (Persian)\nSuomi (Finnish)\nFrancais (French)\n (Hindi)\nMagyar (Hungarian)\nBahasa Indonesia (Indonesian)\nItaliano (Italian)\n (Hebrew)\n (Japanese)\n (Korean)\n (Marathi)\nBahasa Malaysia (Malay)\nNederlands (Dutch)\nNorsk (Norwegian)\n (Punjabi)\nPolski (Polish)\nPortugues (Portuguese)\nRomana (Romanian)\n (Russian)\nSvenska (Swedish)\n (Telugu)\n (Thai)\nTagalog (Tagalog)\nTurkce (Turkish)\n (Ukrainian)\nTieng Viet (Vietnamese)\n (Chinese (Simplified))\n (Chinese (Traditional))\nLanguage\nLinkedIn\nKnow when new jobs open up\nNever miss a job alert with the new LinkedIn app for Windows.\nGet the app\nAgree & Join LinkedIn\nBy clicking Continue to join or sign in, you agree to LinkedIns\nUser Agreement\n,\nPrivacy Policy\n, and\nCookie Policy\n.\nLinkedIn\nLinkedIn is better on the app\nDont have the app? Get it in the Microsoft Store.\nOpen the app\nSign in to see who you already know at SecPod\nSign in\nWelcome back\nEmail or phone\nPassword\nShow\nForgot password?\nSign in\nor\nBy clicking Continue to join or sign in, you agree to LinkedIns\nUser Agreement\n,\nPrivacy Policy\n, and\nCookie Policy\n.\nNew to LinkedIn?\nJoin now\nor\nNew to LinkedIn?\nJoin now\nBy clicking Continue to join or sign in, you agree to LinkedIns\nUser Agreement\n,\nPrivacy Policy\n, and\nCookie Policy\n.", "job_requirements": []}