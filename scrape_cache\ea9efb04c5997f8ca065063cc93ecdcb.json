{"source": "Generic", "title": "Staff Infrastructure Software Engineer at Vapi | Y Combinator", "paragraphs": ["Voice AI for developers.", "We're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.", "We're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try here", "The Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.", "JavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed Systems", "Voice AI for developers", "©2025Y Combinator"], "job_descriptions": ["AboutWhat Happens at YC?ApplyYC Interview GuideFAQPeopleYC BlogCompaniesStartup DirectoryFounder DirectoryLaunch YCStartup JobsAll Jobs◦ Engineering◦ Operations◦ Marketing◦ SalesStartup Job GuideYC Startup Jobs BlogFind a Co-FounderLibrarySAFEResourcesStartup SchoolNewsletterRequests for StartupsFor InvestorsHacker NewsBookfaceOpen main menuApply forF2025batch.ApplyVapiVoice AI for developers.Staff Infrastructure Software Engineer$250K - $400KLocationSan FranciscoJob TypeFull-timeExperience6+ yearsVisaUS citizen/visa onlyConnect directly with founders of the best YC-funded startups.Apply to role ›<PERSON><PERSON>ou<PERSON>About the roleVisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed SystemsAboutVapiVoice AI for developersVapiFounded:2020Batch:W21Team Size:10Status:ActiveLocation:San FranciscoFoundersJordan DearsleyFounderNikhil GuptaFounderSimilar JobsPylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future OpeningsFooterY CombinatorProgramsYC ProgramStartup SchoolWork at a StartupCo-Founder MatchingCompanyYC BlogContactPressPeopleCareersPrivacy PolicyNotice at CollectionSecurityTerms of UseResourcesStartup DirectoryStartup LibraryInvestorsSAFEHacker NewsLaunch YCYC DealsMake something people want.ApplyTwitterTwitterFacebookFacebookInstagramInstagramLinkedInLinkedInYoutubeYouTube©2025Y Combinator", "AboutWhat Happens at YC?ApplyYC Interview GuideFAQPeopleYC BlogCompaniesStartup DirectoryFounder DirectoryLaunch YCStartup JobsAll Jobs◦ Engineering◦ Operations◦ Marketing◦ SalesStartup Job GuideYC Startup Jobs BlogFind a Co-FounderLibrarySAFEResourcesStartup SchoolNewsletterRequests for StartupsFor InvestorsHacker NewsBookfaceOpen main menuApply forF2025batch.Apply", "AboutWhat Happens at YC?ApplyYC Interview GuideFAQPeopleYC BlogCompaniesStartup DirectoryFounder DirectoryLaunch YCStartup JobsAll Jobs◦ Engineering◦ Operations◦ Marketing◦ SalesStartup Job GuideYC Startup Jobs BlogFind a Co-FounderLibrarySAFEResourcesStartup SchoolNewsletterRequests for StartupsFor InvestorsHacker NewsBookfaceOpen main menuApply forF2025batch.Apply", "AboutWhat Happens at YC?ApplyYC Interview GuideFAQPeopleYC BlogCompaniesStartup DirectoryFounder DirectoryLaunch YCStartup JobsAll Jobs◦ Engineering◦ Operations◦ Marketing◦ SalesStartup Job GuideYC Startup Jobs BlogFind a Co-FounderLibrarySAFEResourcesStartup SchoolNewsletterRequests for StartupsFor InvestorsHacker NewsBookface", "AboutWhat Happens at YC?ApplyYC Interview GuideFAQPeopleYC BlogCompaniesStartup DirectoryFounder DirectoryLaunch YCStartup JobsAll Jobs◦ Engineering◦ Operations◦ Marketing◦ SalesStartup Job GuideYC Startup Jobs BlogFind a Co-FounderLibrarySAFEResourcesStartup SchoolNewsletterRequests for StartupsFor InvestorsHacker NewsBookface", "VapiVoice AI for developers.Staff Infrastructure Software Engineer$250K - $400KLocationSan FranciscoJob <PERSON>Full-timeExperience6+ yearsVisaUS citizen/visa onlyConnect directly with founders of the best YC-funded startups.Apply to role ›<PERSON><PERSON>ounderAbout the roleVisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed SystemsAboutVapiVoice AI for developersVapiFounded:2020Batch:W21Team Size:10Status:ActiveLocation:San FranciscoFoundersJordan DearsleyFounderNikhil GuptaFounderSimilar JobsPylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future Openings", "VapiVoice AI for developers.Staff Infrastructure Software Engineer$250K - $400KLocationSan FranciscoJob <PERSON>Full-timeExperience6+ yearsVisaUS citizen/visa onlyConnect directly with founders of the best YC-funded startups.Apply to role ›<PERSON><PERSON>ounderAbout the roleVisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed SystemsAboutVapiVoice AI for developersVapiFounded:2020Batch:W21Team Size:10Status:ActiveLocation:San FranciscoFoundersJordan DearsleyFounderNikhil GuptaFounderSimilar JobsPylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future Openings", "VapiVoice AI for developers.Staff Infrastructure Software Engineer$250K - $400KLocationSan FranciscoJob <PERSON>Full-timeExperience6+ yearsVisaUS citizen/visa onlyConnect directly with founders of the best YC-funded startups.Apply to role ›<PERSON><PERSON>ounderAbout the roleVisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed SystemsAboutVapiVoice AI for developersVapiFounded:2020Batch:W21Team Size:10Status:ActiveLocation:San FranciscoFoundersJordan DearsleyFounderNikhil GuptaFounderSimilar JobsPylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future Openings", "VapiVoice AI for developers.Staff Infrastructure Software Engineer$250K - $400KLocationSan <PERSON><PERSON>ob <PERSON>Full-timeExperience6+ yearsVisaUS citizen/visa onlyConnect directly with founders of the best YC-funded startups.Apply to role ›<PERSON><PERSON>nderAbout the roleVisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed SystemsAboutVapiVoice AI for developers", "VisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed Systems", "VisionWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.MissionWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.Give it a try hereThe RoleThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.OutcomesBuild reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.CompetenciesExperience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)QualificationsPrevious Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in EngineeringSkillsJavaScript, Kubernetes,Node.js, Rust, TypeScript, Distributed Systems", "VapiFounded:2020Batch:W21Team Size:10Status:ActiveLocation:San FranciscoFounders<PERSON><PERSON><PERSON>ou<PERSON><PERSON><PERSON><PERSON>ounderSimilar JobsPylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future Openings", "Similar JobsPylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future Openings", "PylonSenior Software EngineeredgetraceProduct / Full-Stack EngineerOverlapProduct EngineerDraftaidSenior+ Software EngineerStellar SleepFounding Product EngineerReadilyFounding Engineer (Fullstack)CargoSenior Product EngineerRemiStaff Software EngineerVarianceFull Stack Software EngineerDreamCraft Entertainment, Inc.Senior Software EngineerRedbirdSoftware EngineerCoteraSoftware EngineerStradaFounding Software Engineer (Full-Stack, Senior)Electric AirSenior Full stack EngineerPropolisFounding EngineerFoundrySoftware Engineer (New Grad to Mid-Level)ArketaSoftware Engineer - Fullstack, MXSynchFounding Software EngineerSynthFounding AI EngineerFlutterFlowSoftware Engineer - Future Openings", "Y CombinatorProgramsYC ProgramStartup SchoolWork at a StartupCo-Founder MatchingCompanyYC BlogContactPressPeopleCareersPrivacy PolicyNotice at CollectionSecurityTerms of UseResourcesStartup DirectoryStartup LibraryInvestorsSAFEHacker NewsLaunch YCYC DealsMake something people want.ApplyTwitterTwitterFacebookFacebookInstagramInstagramLinkedInLinkedInYoutubeYouTube©2025Y Combinator"], "job_requirements": ["Build reliable solutions.Build scalable solutions.Build well-instrumented and monitored solutions.", "Experience with Kubernetes, Pulumi, or scaling other distributed systemsCommunication: You can clearly articulate what you’re thinkingandfeelingMission-driven: You believe in voice being the default interface for the products of the futureFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)", "Previous Founding/Infrastructure/DevOps Engineer1+ year of experience with a seed or Series A company3+ years of experience in Infrastructure8+ years experience in Engineering", "YC ProgramStartup SchoolWork at a StartupCo-Founder Matching", "YC BlogContactPressPeopleCareersPrivacy PolicyNotice at CollectionSecurityTerms of Use", "Startup DirectoryStartup LibraryInvestorsSAFEHacker NewsLaunch YCYC Deals"], "full_text": "Staff Infrastructure Software Engineer at Vapi | Y Combinator\nAbout\nWhat Happens at YC?\nApply\nYC Interview Guide\nFAQ\nPeople\nYC Blog\nCompanies\nStartup Directory\nFounder Directory\nLaunch YC\nStartup Jobs\nAll Jobs\n◦ Engineering\n◦ Operations\n◦ Marketing\n◦ Sales\nStartup Job Guide\nYC Startup Jobs Blog\nFind a Co-Founder\nLibrary\nSAFE\nResources\nStartup School\nNewsletter\nRequests for Startups\nFor Investors\nHacker News\nBookface\nOpen main menu\nApply for\nF2025\nbatch.\nApply\nVapi\nVoice AI for developers.\nStaff Infrastructure Software Engineer\n$250K - $400K\nLocation\nSan Francisco\nJob Type\nFull-time\nExperience\n6+ years\nVisa\nUS citizen/visa only\nConnect directly with founders of the best YC-funded startups.\nApply to role ›\n<PERSON><PERSON>\nFounder\nAbout the role\nVision\nWe're creating the shift to voice as humanity's default interface. Why voice? Because voice captures the nuance, the emotion, and the humanity of interactions in ways text alone can't: voice makes technology human again.\nMission\nWe're building the platform for the future of voice technology. Our market edge is extensible, reliable infrastructure designed for the full complexity of voice interactions. 18 months, 150k developers, adding 1000 every day.\nGive it a try here\nThe Role\nThe Founding Senior Engineer (Infra) will be responsible for scaling Vapi’s real-time conversational infrastructure to millions of concurrent calls, while ensuring every call gets picked up within 1s with 99.9% reliability. You’ll also be a foundational part of how we build the culture at Vapi.\nOutcomes\nBuild reliable solutions.\nBuild scalable solutions.\nBuild well-instrumented and monitored solutions.\nCompetencies\nExperience with Kubernetes, Pulumi, or scaling other distributed systems\nCommunication: You can clearly articulate what you’re thinking\nand\nfeeling\nMission-driven: You believe in voice being the default interface for the products of the future\nFounder Juice: New ideas come from you, you build it yourself or annoy everyone until it’s done ;)\nQualifications\nPrevious Founding/Infrastructure/DevOps Engineer\n1+ year of experience with a seed or Series A company\n3+ years of experience in Infrastructure\n8+ years experience in Engineering\nSkills\nJavaScript\n, Kubernetes,\nNode.js, Rust, TypeScript, Distributed Systems\nAbout\nVapi\nVoice AI for developers\nVapi\nFounded:\n2020\nBatch:\nW21\nTeam Size:\n10\nStatus:\nActive\nLocation:\nSan Francisco\nFounders\nJordan Dearsley\nFounder\nNikhil Gupta\nFounder\nSimilar Jobs\nPylon\nSenior Software Engineer\nedgetrace\nProduct / Full-Stack Engineer\nOverlap\nProduct Engineer\nDraftaid\nSenior+ Software Engineer\nStellar Sleep\nFounding Product Engineer\nReadily\nFounding Engineer (Fullstack)\nCargo\nSenior Product Engineer\nRemi\nStaff Software Engineer\nVariance\nFull Stack Software Engineer\nDreamCraft Entertainment, Inc.\nSenior Software Engineer\nRedbird\nSoftware Engineer\nCotera\nSoftware Engineer\nStrada\nFounding Software Engineer (Full-Stack, Senior)\nElectric Air\nSenior Full stack Engineer\nPropolis\nFounding Engineer\nFoundry\nSoftware Engineer (New Grad to Mid-Level)\nArketa\nSoftware Engineer - Fullstack, MX\nSynch\nFounding Software Engineer\nSynth\nFounding AI Engineer\nFlutterFlow\nSoftware Engineer - Future Openings\nFooter\nY Combinator\nPrograms\nYC Program\nStartup School\nWork at a Startup\nCo-Founder Matching\nCompany\nYC Blog\nContact\nPress\nPeople\nCareers\nPrivacy Policy\nNotice at Collection\nSecurity\nTerms of Use\nResources\nStartup Directory\nStartup Library\nInvestors\nSAFE\nHacker News\nLaunch YC\nYC Deals\nMake something people want.\nApply\nTwitter\nTwitter\nFacebook\nFacebook\nInstagram\nInstagram\nLinkedIn\nLinkedIn\nYoutube\nYouTube\n©\n2025\nY Combinator"}