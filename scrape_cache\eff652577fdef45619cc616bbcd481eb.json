{"success": true, "message": "response retrieved successfully", "data": {"job_info": {"title": "Senior/Lead Frontend Engineer", "description": "About Sky Mavis\n\nSky Mavis is building the future of gaming. We’re the creators of Axie Infinity, the most successful Web3 game ever, and <PERSON>in, a purpose-built blockchain that ranked as the 4th most-used chain in 2024, behind Ethereum, Bitcoin, and Solana.\n\nWe’ve processed over $4.3 billion in on-chain volume and are backed by more than $170 million from top-tier investors, including a16z, Accel, Libertus Capital, and Paradigm.\n\nOur team moves fast, builds with intention, and believes in a world where players truly own what they earn. If you’re excited by open economies, massive scale, and shaping new digital frontiers, join us.\n\nAbout The Role\n\nRonin Wallet is the gateway to the Ronin blockchain, empowering millions of users to manage digital assets and interact with decentralized applications (DApps) securely and seamlessly. Designed for gamers and Web3 enthusiasts, Ronin Wallet offers fast, low-cost transactions and an intuitive experience across browser extensions, mobile devices, and web applications.\n\nAs a Senior/Lead Frontend Engineer at Sky Mavis, you will play a critical role in shaping the future of Ronin Wallet, the gateway to the Ronin blockchain. You’ll work on both mobile and browser extension platforms, creating secure, performant, and user-friendly experiences that empower millions of users to manage digital assets and interact with decentralized applications (DApps).This is not just a coding role, you will lead technical initiatives, tackle challenging architectural problems, and help define standards for how DApps and wallets work together on the Ronin blockchain.\n\nWhat You’ll Do\n\nArchitect, develop, and maintain Ronin Wallet Mobile (React Native) and Browser Extension (React + Web3). Integrate wallet functionalities with DApps through technologies like WalletConnect, session keys, and custom Web3 provider solutions. Build and enforce secure patterns for transaction signing, message verification, and wallet interactions. Collaborate closely with product, design, and blockchain teams to deliver seamless, polished user experiences. Establish best practices for code quality, testing, and performance across platforms. Mentor and guide junior and mid-level engineers; provide architectural guidance and code reviews. Stay ahead of Web3 and frontend trends, helping evolve our technology stack to meet future challenges. \n\nTechnical Challenges You’ll Tackle\n\nMobile (React Native)\n\nWork deeply on Ronin Wallet Mobile as a core product, integrating multiple DApps and services into a unified experience. Define and enforce standards for DApps built on top of the mobile wallet, including wallet connect flows, session key handling, and secure transaction signing. Solve complex challenges related to cross-DApp session management, deep linking, and WebView integration with Web3 providers. \n\nBrowser Extension (ReactJS)\n\nWork deeply with the browser stack, including service workers, messaging systems, and WebAssembly (WASM) components. Implement secure and efficient communication between the extension, DApps, and background services. Tackle performance and scalability challenges in managing multiple wallet accounts, chains, and session states in a lightweight extension environment. \n\nWhat We Look For\n\n5+ years of professional frontend experience; 2+ years in senior or lead roles. Expertise in JavaScript / TypeScript, ReactJS / Next.js, and React Native. Deep experience working with browser technology, including service workers, messaging APIs, and WebAssembly (WASM). Hands-on knowledge of blockchain fundamentals (transactions, signatures, gas fees, contract interactions). Experience integrating Web3 technologies: ethers.js, web3.js, Wagmi, WalletConnect, etc. Strong understanding of security best practices for wallets and DApps. Ability to lead projects, make technical decisions, and mentor team members effectively. Excellent problem-solving, communication, and collaboration skills. \n\nNice to Have\n\nFamiliarity with EIP standards (e.g., EIP-712, EIP-4337, EIP-7702) and account abstraction concepts. Experience with native modules in React Native (bridging custom native code). Experience contributing to open-source projects in Web3 or frontend ecosystems.", "location": "Ho Chi Minh City, Ho Chi Minh City, Vietnam", "location_id": "*********", "employment_status": "Full-time", "is_remote_allowed": false, "is_third_party_sourced": false, "listed_at": "2025-07-21T08:24:43", "expire_at": "2025-08-20T08:24:43", "job_url": "https://www.linkedin.com/jobs/view/**********/?trk=jobs_biz_prem_srch", "experience_level": "Mid-Senior level", "industries": ["Software Development"], "job_functions": ["Engineering", "Information Technology"], "is_new": true, "original_listed_at": "2025-07-21T08:24:43", "job_state": "LISTED", "workplace_types": ["ONSITE"], "country_code": "vn", "job_posting_id": **********, "is_reposted": false, "job_application_limit_reached": false, "eligible_for_referrals": false}, "company_info": {"name": "Sky Mavis", "description": "Unlock the future // Hatched Axieinfinity & Ronin// We're hiring: http://skymavis.com/careers", "staff_count": 178, "industries": ["Software Development"], "specialities": ["Games", "Blockchain", "Ethereum", "NFT"], "universal_name": "skymavis", "headquarters": {"geographicArea": "Singapore", "country": "SG", "city": "Central Business District", "postalCode": "079902", "$type": "com.linkedin.common.Address", "line2": "#22-15 International Plaza, ", "line1": "10 Anson Rd"}, "url": "https://www.linkedin.com/company/skymavis", "logo_url": "https://media.licdn.com/dms/image/v2/D560BAQHOx0d0BSII8w/company-logo_200_200/company-logo_200_200/0/1689767988366/skymavis_logo?e=1755734400&v=beta&t=SS9BiFcnqMUEi2-Nxc9U_bjYIcpPpXQTdqoxmHgksKg", "background_cover_url": "https://media.licdn.com/dms/image/v2/D563DAQHq-bzDvR8O1A/image-scale_191_1128/image-scale_191_1128/0/1689740629675/skymavis_cover?e=1753729200&v=beta&t=ebpnZrefuev0YElyWK5qRgCzydLnehO4Y7jn9641XE0"}, "apply_details": {"total_applies": 4, "total_views": 62, "is_application_limit_reached": false, "application_url": "https://jobs.ashbyhq.com/skymavis/895c260c-2f15-4e2f-acb9-63af186db7d7/application?utm_source=LinkedInPaid", "is_easy_apply": false}}}