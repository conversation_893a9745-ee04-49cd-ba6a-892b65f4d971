#!/usr/bin/env python3
"""
Test script for payment webhook API
"""
import requests
import json
import os

# Configuration
BASE_URL = "http://localhost:8000"
WEBHOOK_API_KEY = os.getenv("WEBHOOK_API_KEY", "your-secret-webhook-key")

# Test user ID (replace with actual user ID from your database)
TEST_USER_ID = "784af227-217b-4518-b340-08c87ee53a24"  # Replace with real user ID
TEST_USER_EMAIL = "<EMAIL>"  # Replace with real user email

def test_webhook_success():
    """Test successful webhook call"""
    print("🧪 Testing Successful Webhook Call")
    print("=" * 50)
    
    webhook_data = {
        "status": "active",
        "plan": "pro",
        "user_id": TEST_USER_ID,
        "user_email": TEST_USER_EMAIL,
        "customer_id": "cust_test_123",
        "bill_id": "bill_test_456"
    }
    
    headers = {
        "X-API-Key": WEBHOOK_API_KEY,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhook/payment",
            json=webhook_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Webhook call successful!")
        else:
            print("❌ Webhook call failed!")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_webhook_missing_api_key():
    """Test webhook call without API key"""
    print("\n🧪 Testing Missing API Key")
    print("=" * 50)
    
    webhook_data = {
        "status": "active",
        "plan": "starter",
        "user_id": TEST_USER_ID
    }
    
    headers = {
        "Content-Type": "application/json"
        # No X-API-Key header
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhook/payment",
            json=webhook_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 401:
            print("✅ Correctly rejected missing API key!")
        else:
            print("❌ Should have rejected missing API key!")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_webhook_invalid_api_key():
    """Test webhook call with invalid API key"""
    print("\n🧪 Testing Invalid API Key")
    print("=" * 50)
    
    webhook_data = {
        "status": "active",
        "plan": "starter",
        "user_id": TEST_USER_ID
    }
    
    headers = {
        "X-API-Key": "invalid-key-123",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhook/payment",
            json=webhook_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 401:
            print("✅ Correctly rejected invalid API key!")
        else:
            print("❌ Should have rejected invalid API key!")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_webhook_invalid_user():
    """Test webhook call with invalid user ID"""
    print("\n🧪 Testing Invalid User ID")
    print("=" * 50)
    
    webhook_data = {
        "status": "active",
        "plan": "starter",
        "user_id": "invalid-user-id-123"
    }
    
    headers = {
        "X-API-Key": WEBHOOK_API_KEY,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhook/payment",
            json=webhook_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 400:
            print("✅ Correctly handled invalid user ID!")
        else:
            print("❌ Should have handled invalid user ID!")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_webhook_invalid_data():
    """Test webhook call with invalid data"""
    print("\n🧪 Testing Invalid Data")
    print("=" * 50)
    
    webhook_data = {
        "status": "invalid_status",  # Invalid status
        "plan": "invalid_plan",      # Invalid plan
        "user_id": TEST_USER_ID
    }
    
    headers = {
        "X-API-Key": WEBHOOK_API_KEY,
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/webhook/payment",
            json=webhook_data,
            headers=headers
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 422:
            print("✅ Correctly validated invalid data!")
        else:
            print("❌ Should have validated invalid data!")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_webhook_health():
    """Test webhook health check"""
    print("\n🧪 Testing Webhook Health Check")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/webhook/health")
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            print("✅ Health check successful!")
        else:
            print("❌ Health check failed!")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_different_plans():
    """Test webhook with different plans"""
    print("\n🧪 Testing Different Plans")
    print("=" * 50)
    
    plans = ["trial", "starter", "pro"]
    
    for plan in plans:
        print(f"\n--- Testing plan: {plan} ---")
        
        webhook_data = {
            "status": "active",
            "plan": plan,
            "user_id": TEST_USER_ID,
            "customer_id": f"cust_{plan}_123"
        }
        
        headers = {
            "X-API-Key": WEBHOOK_API_KEY,
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(
                f"{BASE_URL}/webhook/payment",
                json=webhook_data,
                headers=headers
            )
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Plan {plan} updated successfully!")
                print(f"Updated fields: {data.get('updated_fields', {})}")
            else:
                print(f"❌ Plan {plan} update failed!")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Error testing plan {plan}: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Payment Webhook Test Suite")
    print("=" * 60)
    print(f"Base URL: {BASE_URL}")
    print(f"API Key: {WEBHOOK_API_KEY}")
    print(f"Test User ID: {TEST_USER_ID}")
    print(f"Test User Email: {TEST_USER_EMAIL}")
    print("=" * 60)
    
    # Run all tests
    test_webhook_health()
    test_webhook_success()
    test_webhook_missing_api_key()
    test_webhook_invalid_api_key()
    test_webhook_invalid_user()
    test_webhook_invalid_data()
    test_different_plans()
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\nWebhook API endpoints tested:")
    print("- POST /webhook/payment")
    print("- GET /webhook/health")
    print("\nMake sure to:")
    print("1. Set WEBHOOK_API_KEY environment variable")
    print("2. Update TEST_USER_ID with a real user ID")
    print("3. Update TEST_USER_EMAIL with a real user email")
    print("4. Start your FastAPI server before running tests")

if __name__ == "__main__":
    main()
