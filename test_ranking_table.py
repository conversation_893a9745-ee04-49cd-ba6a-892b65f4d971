#!/usr/bin/env python3
"""
Test script to verify ranking table structure and data mapping.
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).resolve().parent / "app"))

from app.rank.ranking import test_table_insertion
from app.db.database import <PERSON><PERSON><PERSON>al
from sqlalchemy import text

def test_table_structure():
    """Test if the ranking_results table exists and has the correct structure."""
    try:
        db = SessionLocal()
        
        # Check if table exists
        result = db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ranking_results'
            );
        """))
        table_exists = result.scalar()
        
        if not table_exists:
            print("❌ ranking_results table does not exist!")
            return False
        
        print("✅ ranking_results table exists")
        
        # Check table structure
        result = db.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'ranking_results'
            ORDER BY ordinal_position;
        """))
        
        columns = result.fetchall()
        print("\n📋 Table structure:")
        for col in columns:
            nullable = "NULL" if col[2] == "YES" else "NOT NULL"
            print(f"  {col[0]}: {col[1]} ({nullable})")
        
        # Check if indexes exist
        result = db.execute(text("""
            SELECT indexname, indexdef
            FROM pg_indexes 
            WHERE tablename = 'ranking_results';
        """))
        
        indexes = result.fetchall()
        print(f"\n🔍 Found {len(indexes)} indexes:")
        for idx in indexes:
            print(f"  {idx[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking table structure: {e}")
        return False
    finally:
        db.close()

def test_job_descriptions():
    """Test if there are job descriptions in the database."""
    try:
        db = SessionLocal()
        
        # Check if job_descriptions table exists
        result = db.execute(text("""
            SELECT COUNT(*) FROM job_descriptions;
        """))
        count = result.scalar()
        
        print(f"📊 Found {count} job descriptions in database")
        
        if count > 0:
            # Get a sample job description
            result = db.execute(text("""
                SELECT id, jd_text, information_extracted_json
                FROM job_descriptions 
                WHERE information_extracted_json IS NOT NULL
                LIMIT 1;
            """))
            
            job = result.fetchone()
            if job:
                print(f"✅ Sample job found:")
                print(f"  ID: {job[0]}")
                print(f"  JD Text: {job[1][:100]}...")
                print(f"  Candidates: {len(job[2]) if job[2] else 0}")
                return job[0]  # Return the job ID for testing
            else:
                print("⚠️  No job descriptions with extracted information found")
                return None
        else:
            print("❌ No job descriptions found in database")
            return None
            
    except Exception as e:
        print(f"❌ Error checking job descriptions: {e}")
        return None
    finally:
        db.close()

def main():
    """Main test function."""
    print("🧪 Testing Ranking Table Structure and Data Mapping")
    print("=" * 60)
    
    # Test table structure
    print("\n1. Testing table structure...")
    if not test_table_structure():
        print("❌ Table structure test failed!")
        return
    
    # Test job descriptions
    print("\n2. Testing job descriptions...")
    job_id = test_job_descriptions()
    
    if not job_id:
        print("❌ No suitable job description found for testing!")
        print("Please ensure you have job descriptions with extracted information in the database.")
        return
    
    # Test table insertion
    print(f"\n3. Testing table insertion with job_id: {job_id}")
    success = test_table_insertion(job_id)
    
    if success:
        print("\n✅ All tests passed! The ranking table structure and data mapping are working correctly.")
    else:
        print("\n❌ Table insertion test failed!")
    
    print("\n" + "=" * 60)
    print("Test completed!")

if __name__ == "__main__":
    main() 