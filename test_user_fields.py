#!/usr/bin/env python3
"""
Test script for new user fields (status and plan)
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword123"

def test_user_registration():
    """Test user registration with new fields"""
    print("🧪 Testing User Registration with New Fields")
    print("=" * 50)
    
    # Test data
    user_data = {
        "username": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "company_name": "Test Company",
        "full_name": "Test User",
        "status": "active",
        "plan": "starter"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print("✅ Registration successful!")
            print(f"User Data: {json.dumps(data, indent=2)}")
            
            # Check if new fields are present
            user_info = data.get('data', {})
            if 'status' in user_info and 'plan' in user_info:
                print(f"✅ Status: {user_info['status']}")
                print(f"✅ Plan: {user_info['plan']}")
            else:
                print("❌ New fields not found in response")
                
        else:
            print(f"❌ Registration failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_user_login():
    """Test user login and check if new fields are returned"""
    print("\n🧪 Testing User Login")
    print("=" * 50)
    
    login_data = {
        "username": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"Login Data: {json.dumps(data, indent=2)}")
            
            # Check if new fields are present
            user_info = data.get('data', {})
            if 'status' in user_info and 'plan' in user_info:
                print(f"✅ Status: {user_info['status']}")
                print(f"✅ Plan: {user_info['plan']}")
                return user_info.get('access_token')
            else:
                print("❌ New fields not found in login response")
                
        else:
            print(f"❌ Login failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def test_get_me(access_token):
    """Test /auth/me endpoint"""
    print("\n🧪 Testing /auth/me Endpoint")
    print("=" * 50)
    
    if not access_token:
        print("❌ No access token available")
        return
    
    try:
        response = requests.get(
            f"{BASE_URL}/auth/me",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Get me successful!")
            print(f"User Data: {json.dumps(data, indent=2)}")
            
            # Check if new fields are present
            user_info = data.get('data', {})
            if 'status' in user_info and 'plan' in user_info:
                print(f"✅ Status: {user_info['status']}")
                print(f"✅ Plan: {user_info['plan']}")
            else:
                print("❌ New fields not found in /me response")
                
        else:
            print(f"❌ Get me failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_database_directly():
    """Test database directly to verify fields exist"""
    print("\n🧪 Testing Database Directly")
    print("=" * 50)
    
    try:
        import sys
        import os
        from pathlib import Path
        
        # Add the app directory to Python path
        project_root = Path(__file__).parent
        sys.path.append(str(project_root))
        
        from app.db.database import get_database_url
        from sqlalchemy import create_engine, text
        from sqlalchemy.orm import sessionmaker
        
        # Get database connection
        database_url = get_database_url()
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as db:
            # Check table structure
            query = text("""
                SELECT column_name, data_type, column_default 
                FROM information_schema.columns 
                WHERE table_name = 'users' 
                AND column_name IN ('status', 'plan')
                ORDER BY column_name
            """)
            
            result = db.execute(query).fetchall()
            
            if result:
                print("✅ New columns found in database:")
                for row in result:
                    print(f"  - {row.column_name}: {row.data_type} (default: {row.column_default})")
            else:
                print("❌ New columns not found in database")
            
            # Check sample data
            sample_query = text("""
                SELECT email, status, plan, created_at 
                FROM users 
                ORDER BY created_at DESC 
                LIMIT 5
            """)
            
            sample_result = db.execute(sample_query).fetchall()
            
            if sample_result:
                print("\n📊 Sample user data:")
                for row in sample_result:
                    print(f"  - {row.email}: status={row.status}, plan={row.plan}")
            
    except Exception as e:
        print(f"❌ Database test error: {str(e)}")

def main():
    """Main test function"""
    print("🚀 User Fields Test Suite")
    print("=" * 60)
    
    # Test 1: Database structure
    test_database_directly()
    
    # Test 2: User registration
    test_user_registration()
    
    # Test 3: User login
    access_token = test_user_login()
    
    # Test 4: Get me endpoint
    test_get_me(access_token)
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\nIf all tests pass, your new user fields are working correctly!")

if __name__ == "__main__":
    main()
