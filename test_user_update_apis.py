#!/usr/bin/env python3
"""
Test script for user update APIs (status and plan)
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"

def get_access_token():
    """Get access token for testing"""
    # Use existing user credentials
    login_data = {
        "username": "<EMAIL>",  # Use an existing user
        "password": "your_password_here"  # Replace with actual password
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            return data.get('data', {}).get('access_token')
        else:
            print(f"❌ Login failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def test_get_user_info(access_token):
    """Test getting current user info"""
    print("🧪 Testing GET /auth/me")
    print("=" * 40)
    
    try:
        response = requests.get(
            f"{BASE_URL}/auth/me",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Get user info successful!")
            user_data = data.get('data', {})
            print(f"Current Status: {user_data.get('status')}")
            print(f"Current Plan: {user_data.get('plan')}")
            return user_data
        else:
            print(f"❌ Failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def test_update_user_status(access_token):
    """Test updating user status"""
    print("\n🧪 Testing PUT /auth/me/status")
    print("=" * 40)
    
    # Test updating to 'upgrading'
    status_data = {"status": "upgrading"}
    
    try:
        response = requests.put(
            f"{BASE_URL}/auth/me/status",
            json=status_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Status update successful!")
            user_data = data.get('data', {})
            print(f"New Status: {user_data.get('status')}")
            print(f"Plan: {user_data.get('plan')}")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_update_user_plan(access_token):
    """Test updating user plan"""
    print("\n🧪 Testing PUT /auth/me/plan")
    print("=" * 40)
    
    # Test updating to 'pro'
    plan_data = {"plan": "pro"}
    
    try:
        response = requests.put(
            f"{BASE_URL}/auth/me/plan",
            json=plan_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Plan update successful!")
            user_data = data.get('data', {})
            print(f"Status: {user_data.get('status')}")
            print(f"New Plan: {user_data.get('plan')}")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_update_user_profile(access_token):
    """Test updating multiple user fields"""
    print("\n🧪 Testing PUT /auth/me (Multiple Fields)")
    print("=" * 40)
    
    # Test updating multiple fields
    update_data = {
        "status": "active",
        "plan": "starter",
        "company_name": "Updated Company Name",
        "full_name": "Updated Full Name"
    }
    
    try:
        response = requests.put(
            f"{BASE_URL}/auth/me",
            json=update_data,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Profile update successful!")
            user_data = data.get('data', {})
            print(f"Status: {user_data.get('status')}")
            print(f"Plan: {user_data.get('plan')}")
            print(f"Company: {user_data.get('company_name')}")
            print(f"Full Name: {user_data.get('full_name')}")
        else:
            print(f"❌ Failed: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_invalid_values(access_token):
    """Test with invalid values"""
    print("\n🧪 Testing Invalid Values")
    print("=" * 40)
    
    # Test invalid status
    invalid_status = {"status": "invalid_status"}
    
    try:
        response = requests.put(
            f"{BASE_URL}/auth/me/status",
            json=invalid_status,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Invalid Status - Status Code: {response.status_code}")
        if response.status_code == 422:
            print("✅ Validation error as expected")
        else:
            print(f"❌ Unexpected response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Test invalid plan
    invalid_plan = {"plan": "invalid_plan"}
    
    try:
        response = requests.put(
            f"{BASE_URL}/auth/me/plan",
            json=invalid_plan,
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
        )
        
        print(f"Invalid Plan - Status Code: {response.status_code}")
        if response.status_code == 422:
            print("✅ Validation error as expected")
        else:
            print(f"❌ Unexpected response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    """Main test function"""
    print("🚀 User Update APIs Test Suite")
    print("=" * 60)
    
    # Get access token
    print("🔑 Getting access token...")
    access_token = get_access_token()
    
    if not access_token:
        print("❌ Cannot get access token. Please check login credentials.")
        print("Update the login_data in get_access_token() function with valid credentials.")
        return
    
    print("✅ Access token obtained")
    
    # Run tests
    test_get_user_info(access_token)
    test_update_user_status(access_token)
    test_update_user_plan(access_token)
    test_update_user_profile(access_token)
    test_invalid_values(access_token)
    
    # Final check
    print("\n🔍 Final User Info Check")
    print("=" * 40)
    test_get_user_info(access_token)
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\nAPI Endpoints tested:")
    print("- GET /auth/me")
    print("- PUT /auth/me/status")
    print("- PUT /auth/me/plan")
    print("- PUT /auth/me")

if __name__ == "__main__":
    main()
