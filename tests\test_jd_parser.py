import os
import sys
import json
from openai import OpenAI
from dotenv import load_dotenv

def test_jd_parsing():
    """Test the job description parsing functionality."""
    try:
        # Load environment variables
        load_dotenv()
        
        # Get API key
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            print("Error: OPENAI_API_KEY environment variable not set.")
            sys.exit(1)
        
        print("Initializing OpenAI client...")
        client = OpenAI(api_key=api_key)
        
        # Sample job description
        job_description = """
        Senior Software Engineer
        
        We are looking for a Senior Software Engineer to join our team. The ideal candidate should have:
        - 5+ years of experience in Python development
        - Strong knowledge of web frameworks (Django/Flask)
        - Experience with cloud platforms (AWS/GCP)
        - Bachelor's degree in Computer Science or related field
        
        Responsibilities:
        - Design and implement scalable software solutions
        - Collaborate with cross-functional teams
        - Mentor junior developers
        - Write clean, maintainable code
        
        Benefits:
        - Competitive salary
        - Health insurance
        - 401(k) matching
        - Flexible work hours
        """
        
        # Clean the text
        cleaned_text = job_description.encode("ascii", "ignore").decode("ascii")
        
        # Create the prompt
        prompt = f"""Parse the following job description and extract key information in JSON format:

{cleaned_text}

Return a JSON object with the following structure:
{{
    "title": "Job title",
    "company": "Company name",
    "location": "Job location",
    "type": "Job type (Full-time/Part-time/Contract)",
    "experience": "Required years of experience",
    "education": "Required education level",
    "skills": ["List", "of", "required", "skills"],
    "responsibilities": ["List", "of", "key", "responsibilities"],
    "benefits": ["List", "of", "benefits"],
    "salary_range": "Salary range if mentioned"
}}"""

        print("\nSending job description to OpenAI API...")
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that parses job descriptions."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7
        )
        
        # Parse the response
        try:
            parsed_data = json.loads(response.choices[0].message.content)
            print("\nParsed Job Description:")
            print(json.dumps(parsed_data, indent=2))
            print("\nJob description parsing test successful!")
        except json.JSONDecodeError as e:
            print(f"\nError parsing JSON response: {str(e)}")
            print("Raw response:", response.choices[0].message.content)
            sys.exit(1)
        
    except Exception as e:
        print(f"\nError testing job description parsing: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    print("Starting job description parsing test...")
    test_jd_parsing() 