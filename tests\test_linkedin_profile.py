#!/usr/bin/env python3
"""
Test script for LinkedIn Profile Location Verification API
"""

import requests
import json
import time
from typing import List, Dict

# API Configuration
BASE_URL = "http://localhost:8000"
API_ENDPOINTS = {
    "profile_details": f"{BASE_URL}/linkedin-profile/details",
    "multiple_profiles": f"{BASE_URL}/linkedin-profile/multiple",
    "verify_location": f"{BASE_URL}/linkedin-profile/verify-location",
    "cache_stats": f"{BASE_URL}/linkedin-profile/cache/stats",
    "health": f"{BASE_URL}/linkedin-profile/health"
}


def test_health_check():
    """Test the health check endpoint."""
    print("🔍 Testing Health Check...")
    try:
        response = requests.get(API_ENDPOINTS["health"])
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Status: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")

def test_profile_details(linkedin_url: str):
    """Test getting profile details from a LinkedIn URL."""
    print(f"\n🔍 Testing Profile Details for: {linkedin_url}")
    
    payload = {
        "linkedin_url": linkedin_url
    }
    
    try:
        response = requests.post(API_ENDPOINTS["profile_details"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                profile = data["data"]
                print("✅ Profile details extracted successfully")
                print(f"   Name: {profile.get('name', 'N/A')}")
                print(f"   Headline: {profile.get('headline', 'N/A')}")
                print(f"   Location: {profile.get('location', 'N/A')}")
                print(f"   Company: {profile.get('company', 'N/A')}")
                print(f"   Skills: {len(profile.get('skills', []))} skills found")
                print(f"   Experience: {len(profile.get('experience', []))} positions")
                return profile
            else:
                print(f"❌ Profile extraction failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def test_location_verification(linkedin_url: str, expected_location: str):
    """Test location verification for a LinkedIn profile."""
    print(f"\n🔍 Testing Location Verification...")
    print(f"   URL: {linkedin_url}")
    print(f"   Expected Location: {expected_location}")
    
    payload = {
        "linkedin_url": linkedin_url,
        "expected_location": expected_location
    }
    
    try:
        response = requests.post(API_ENDPOINTS["verify_location"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"] and data["verification_success"]:
                print("✅ Location verification completed")
                print(f"   Location Match: {data['location_match']}")
                print(f"   Expected: {data['expected_location']}")
                print(f"   Actual: {data['actual_location']}")
                
                if data.get("profile_data"):
                    profile = data["profile_data"]
                    print(f"   Profile Name: {profile.get('name', 'N/A')}")
                    print(f"   Profile Company: {profile.get('company', 'N/A')}")
            else:
                print(f"❌ Location verification failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_multiple_profiles(linkedin_urls: List[str]):
    """Test getting details for multiple LinkedIn profiles."""
    print(f"\n🔍 Testing Multiple Profiles ({len(linkedin_urls)} URLs)...")
    
    payload = {
        "linkedin_urls": linkedin_urls,
        "max_concurrent": 2
    }
    
    try:
        response = requests.post(API_ENDPOINTS["multiple_profiles"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ Multiple profiles processed successfully")
                print(f"   Total Processed: {data['total_processed']}")
                print(f"   Successful: {data['successful_count']}")
                print(f"   Failed: {data['failed_count']}")
                
                for i, profile_response in enumerate(data["profiles"]):
                    if profile_response["success"]:
                        profile = profile_response["data"]
                        print(f"   Profile {i+1}: {profile.get('name', 'N/A')} - {profile.get('location', 'N/A')}")
                    else:
                        print(f"   Profile {i+1}: Failed - {profile_response.get('error', 'Unknown error')}")
            else:
                print(f"❌ Multiple profiles processing failed")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def test_cache_stats():
    """Test getting cache statistics."""
    print(f"\n🔍 Testing Cache Statistics...")
    
    try:
        response = requests.get(API_ENDPOINTS["cache_stats"])
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ Cache statistics retrieved")
                print(f"   Total Cached Profiles: {data['total_cached_profiles']}")
                print(f"   Cache Directory: {data['cache_directory']}")
                print(f"   Total Size: {data['total_cache_size_mb']} MB")
            else:
                print(f"❌ Cache stats failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    """Main test function."""
    print("🚀 LinkedIn Profile Location Verification API Test")
    print("=" * 60)
    
    # Test health check first
    test_health_check()
    
    # Sample LinkedIn URLs for testing
    # Note: These are example URLs - replace with real LinkedIn profile URLs for testing
    sample_urls = [
        "https://www.linkedin.com/in/satyanadella/",  # Microsoft CEO
        "https://www.linkedin.com/in/timcook/",       # Apple CEO
        "https://www.linkedin.com/in/jeffweiner08/"   # LinkedIn CEO
    ]
    
    # Test individual profile details
    if sample_urls:
        profile = test_profile_details(sample_urls[0])
        
        # Test location verification
        if profile:
            test_location_verification(sample_urls[0], "Seattle")
            test_location_verification(sample_urls[0], "San Francisco")
    
    # Test multiple profiles (limit to 2 for demo)
    if len(sample_urls) >= 2:
        test_multiple_profiles(sample_urls[:2])
    
    # Test cache statistics
    test_cache_stats()
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")
    print("\n📝 Usage Examples:")
    print("1. Get profile details:")
    print(f"   curl -X POST {API_ENDPOINTS['profile_details']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"linkedin_url\": \"https://www.linkedin.com/in/example/\"}'")
    
    print("\n2. Verify location:")
    print(f"   curl -X POST {API_ENDPOINTS['verify_location']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"linkedin_url\": \"https://www.linkedin.com/in/example/\", \"expected_location\": \"San Francisco\"}'")
    
    print("\n3. Get cache stats:")
    print(f"   curl {API_ENDPOINTS['cache_stats']}")

if __name__ == "__main__":
    main() 