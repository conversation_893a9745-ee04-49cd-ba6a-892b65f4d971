#!/usr/bin/env python3
"""
Test script for Enhanced Location Verification using SerpAPI
"""

import requests
import json
import time

# API Configuration
BASE_URL = "http://localhost:8000"
API_ENDPOINTS = {
    "verify_location": f"{BASE_URL}/serpapi/verify-location",
    "search_profiles": f"{BASE_URL}/serpapi/search",
    "health": f"{BASE_URL}/serpapi/health"
}

def test_health_check():
    """Test the SerpAPI health check endpoint."""
    print("🔍 Testing SerpAPI Health Check...")
    try:
        response = requests.get(API_ENDPOINTS["health"])
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Status: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")

def test_location_verification(candidate_name: str, expected_location: str, company: str = None):
    """Test location verification for a candidate."""
    print(f"\n🔍 Testing Location Verification...")
    print(f"   Candidate: {candidate_name}")
    print(f"   Expected Location: {expected_location}")
    if company:
        print(f"   Company: {company}")
    
    payload = {
        "candidate_name": candidate_name,
        "expected_location": expected_location
    }
    
    if company:
        payload["company"] = company
    
    try:
        response = requests.post(API_ENDPOINTS["verify_location"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ Location verification completed")
                print(f"   Result: {data['verification_result']}")
                print(f"   Confidence: {data['location_confidence']:.2f}")
                print(f"   LinkedIn Matches: {len(data['linkedin_matches'])}")
                print(f"   Web Matches: {len(data['web_matches'])}")
                print(f"   Company Matches: {len(data['company_matches'])}")
                
                # Show top evidence sources
                if data['evidence_sources']:
                    print("   Evidence Sources:")
                    for i, source in enumerate(data['evidence_sources'][:3]):
                        print(f"     {i+1}. {source}")
                
                # Show sample matches
                if data['linkedin_matches']:
                    print("   Sample LinkedIn Match:")
                    match = data['linkedin_matches'][0]
                    print(f"     Title: {match['title']}")
                    print(f"     Confidence: {match['confidence']:.2f}")
                
                return data
            else:
                print(f"❌ Location verification failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def test_search_profiles(job_title: str, location: str):
    """Test profile search functionality."""
    print(f"\n🔍 Testing Profile Search...")
    print(f"   Job Title: {job_title}")
    print(f"   Location: {location}")
    
    payload = {
        "job_title": job_title,
        "location": location,
        "num_results": 5
    }
    
    try:
        response = requests.post(API_ENDPOINTS["search_profiles"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ Profile search completed")
                print(f"   Results: {len(data['organic_results'])} profiles found")
                
                # Show first result
                if data['organic_results']:
                    first_result = data['organic_results'][0]
                    print("   Sample Result:")
                    print(f"     Title: {first_result.get('title', 'N/A')}")
                    print(f"     Snippet: {first_result.get('snippet', 'N/A')[:100]}...")
                    print(f"     Link: {first_result.get('link', 'N/A')}")
                
                return data
            else:
                print(f"❌ Profile search failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def main():
    """Main test function."""
    print("🚀 Enhanced Location Verification API Test")
    print("=" * 60)
    
    # Test health check first
    test_health_check()
    
    # Test cases for location verification
    test_cases = [
        {
            "name": "Satya Nadella",
            "location": "Seattle, Washington",
            "company": "Microsoft"
        },
        {
            "name": "Tim Cook",
            "location": "Cupertino, California",
            "company": "Apple"
        },
        {
            "name": "Sundar Pichai",
            "location": "Mountain View, California",
            "company": "Google"
        }
    ]
    
    # Test location verification for each case
    for i, case in enumerate(test_cases, 1):
        print(f"\n{'='*40}")
        print(f"Test Case {i}: {case['name']}")
        print(f"{'='*40}")
        
        result = test_location_verification(
            candidate_name=case['name'],
            expected_location=case['location'],
            company=case['company']
        )
        
        # Add delay between requests
        if i < len(test_cases):
            time.sleep(2)
    
    # Test profile search
    print(f"\n{'='*40}")
    print("Testing Profile Search")
    print(f"{'='*40}")
    
    search_result = test_search_profiles(
        job_title="Software Engineer",
        location="San Francisco, CA"
    )
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")
    print("\n📝 Usage Examples:")
    print("1. Verify candidate location:")
    print(f"   curl -X POST {API_ENDPOINTS['verify_location']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"candidate_name\": \"John Doe\", \"expected_location\": \"San Francisco, CA\", \"company\": \"Tech Corp\"}'")
    
    print("\n2. Search for profiles:")
    print(f"   curl -X POST {API_ENDPOINTS['search_profiles']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"job_title\": \"Software Engineer\", \"location\": \"San Francisco, CA\", \"num_results\": 10}'")
    
    print("\n3. Health check:")
    print(f"   curl {API_ENDPOINTS['health']}")

if __name__ == "__main__":
    main() 