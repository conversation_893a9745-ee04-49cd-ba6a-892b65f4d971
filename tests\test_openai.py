import os
import sys
from openai import OpenAI

def ask_chatgpt(question):
    try:
        # api_key = os.environ["OPENAI_API_KEY"]
        api_key="********************************************************************************************************************************************************************"
    except KeyError:
        print("Error: OPENAI_API_KEY environment variable not set.")
        sys.exit(1)

    client = OpenAI(api_key=api_key)

    response = client.chat.completions.create(
        model="gpt-4",  # or "gpt-3.5-turbo"
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": question}
        ],
        temperature=0.7
    )
    return response.choices[0].message.content

if __name__ == "__main__":
    print("Hello, ChatGPT!")
    question = "What is the capital of France?"
    answer = ask_chatgpt(question)
    print(f"ChatGPT says: {answer}")

