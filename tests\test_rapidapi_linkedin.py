#!/usr/bin/env python3
"""
Test script for RapidAPI LinkedIn Profile Extraction API
"""

import requests
import json
import time
import os
from typing import List, Dict

# API Configuration
BASE_URL = "http://localhost:8000"
API_ENDPOINTS = {
    "single_profile": f"{BASE_URL}/rapidapi-linkedin/profile",
    "batch_profiles": f"{BASE_URL}/rapidapi-linkedin/batch",
    "process_csv": f"{BASE_URL}/rapidapi-linkedin/csv",
    "upload_csv": f"{BASE_URL}/rapidapi-linkedin/upload-csv",
    "cache_stats": f"{BASE_URL}/rapidapi-linkedin/cache/stats",
    "health": f"{BASE_URL}/rapidapi-linkedin/health"
}

def test_health_check():
    """Test the RapidAPI LinkedIn health check endpoint."""
    print("🔍 Testing RapidAPI LinkedIn Health Check...")
    try:
        response = requests.get(API_ENDPOINTS["health"])
        if response.status_code == 200:
            print("✅ Health check passed")
            print(f"   Status: {response.json()}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Health check error: {str(e)}")

def test_single_profile(linkedin_url: str):
    """Test fetching a single LinkedIn profile."""
    print(f"\n🔍 Testing Single Profile: {linkedin_url}")
    
    payload = {
        "linkedin_url": linkedin_url
    }
    
    try:
        response = requests.post(API_ENDPOINTS["single_profile"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                profile = data["data"]
                print("✅ Profile extracted successfully")
                print(f"   Name: {profile.get('name', 'N/A')}")
                print(f"   Location: {profile.get('contact', {}).get('location', 'N/A')}")
                print(f"   LinkedIn: {profile.get('contact', {}).get('linkedin', 'N/A')}")
                print(f"   Universities: {len(profile.get('universities', []))}")
                print(f"   Experiences: {len(profile.get('experiences', []))}")
                print(f"   Skills: {len(profile.get('skills', []))}")
                
                # Show first experience if available
                if profile.get('experiences'):
                    exp = profile['experiences'][0]
                    print(f"   Current Position: {exp.get('position', 'N/A')} at {exp.get('company', 'N/A')}")
                
                return profile
            else:
                print(f"❌ Profile extraction failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def test_batch_profiles(linkedin_urls: List[str]):
    """Test batch processing of LinkedIn profiles."""
    print(f"\n🔍 Testing Batch Profiles ({len(linkedin_urls)} URLs)...")
    
    payload = {
        "linkedin_urls": linkedin_urls,
        "max_workers": 3
    }
    
    try:
        response = requests.post(API_ENDPOINTS["batch_profiles"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ Batch processing completed")
                print(f"   Total Processed: {data['total_processed']}")
                print(f"   Successful: {data['successful_count']}")
                print(f"   Failed: {data['failed_count']}")
                
                # Show sample results
                for i, profile in enumerate(data["profiles"][:3]):  # Show first 3
                    if profile.get('name'):
                        print(f"   Profile {i+1}: {profile['name']} - {profile.get('contact', {}).get('location', 'N/A')}")
                    else:
                        print(f"   Profile {i+1}: Failed - {profile.get('error', 'Unknown error')}")
                
                return data
            else:
                print(f"❌ Batch processing failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def create_test_csv():
    """Create a test CSV file with LinkedIn URLs."""
    csv_content = """LinkedIn_URL
https://www.linkedin.com/in/satyanadella/
https://www.linkedin.com/in/timcook/
https://www.linkedin.com/in/sundarpichai/
"""
    
    csv_path = "test_linkedin_urls.csv"
    with open(csv_path, 'w') as f:
        f.write(csv_content)
    
    print(f"📄 Created test CSV file: {csv_path}")
    return csv_path

def test_csv_processing(csv_file_path: str):
    """Test CSV file processing."""
    print(f"\n🔍 Testing CSV Processing: {csv_file_path}")
    
    payload = {
        "csv_file_path": csv_file_path,
        "output_dir": "test_output"
    }
    
    try:
        response = requests.post(API_ENDPOINTS["process_csv"], json=payload)
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ CSV processing completed")
                print(f"   Total Profiles: {data['total_profiles']}")
                print(f"   Successful Profiles: {data['successful_profiles']}")
                print(f"   JSON Output: {data['json_path']}")
                print(f"   CSV Output: {data['csv_path']}")
                
                # Check if output files exist
                if data['json_path'] and os.path.exists(data['json_path']):
                    print("   ✅ JSON output file created")
                if data['csv_path'] and os.path.exists(data['csv_path']):
                    print("   ✅ CSV output file created")
                
                return data
            else:
                print(f"❌ CSV processing failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def test_upload_csv(csv_file_path: str):
    """Test CSV file upload and processing."""
    print(f"\n🔍 Testing CSV Upload: {csv_file_path}")
    
    try:
        with open(csv_file_path, 'rb') as f:
            files = {'file': (os.path.basename(csv_file_path), f, 'text/csv')}
            response = requests.post(API_ENDPOINTS["upload_csv"], files=files, params={"output_dir": "test_upload_output"})
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ CSV upload and processing completed")
                print(f"   Total Profiles: {data['total_profiles']}")
                print(f"   Successful Profiles: {data['successful_profiles']}")
                print(f"   JSON Output: {data['json_path']}")
                print(f"   CSV Output: {data['csv_path']}")
                
                return data
            else:
                print(f"❌ CSV upload failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    return None

def test_cache_stats():
    """Test getting cache statistics."""
    print(f"\n🔍 Testing Cache Statistics...")
    
    try:
        response = requests.get(API_ENDPOINTS["cache_stats"])
        
        if response.status_code == 200:
            data = response.json()
            if data["success"]:
                print("✅ Cache statistics retrieved")
                print(f"   Total Cached Profiles: {data['total_cached_profiles']}")
                print(f"   Cache Directory: {data['cache_directory']}")
                print(f"   Total Size: {data['total_cache_size_mb']} MB")
            else:
                print(f"❌ Cache stats failed: {data.get('error', 'Unknown error')}")
        else:
            print(f"❌ API request failed: {response.status_code}")
            print(f"   Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    """Main test function."""
    print("🚀 RapidAPI LinkedIn Profile Extraction API Test")
    print("=" * 60)
    
    # Check if RapidAPI key is set
    if not os.getenv("RAPIDAPI_KEY"):
        print("⚠️  RAPIDAPI_KEY environment variable not set!")
        print("   Please set your RapidAPI key:")
        print("   export RAPIDAPI_KEY='your_rapidapi_key_here'")
        print("   Or add it to your .env file")
        return
    
    # Test health check first
    test_health_check()
    
    # Sample LinkedIn URLs for testing
    sample_urls = [
        "https://www.linkedin.com/in/satyanadella/",  # Microsoft CEO
        "https://www.linkedin.com/in/timcook/",       # Apple CEO
        "https://www.linkedin.com/in/sundarpichai/"   # Google CEO
    ]
    
    # Test single profile
    if sample_urls:
        profile = test_single_profile(sample_urls[0])
        
        # Add delay between requests
        time.sleep(2)
    
    # Test batch profiles (limit to 2 for demo)
    if len(sample_urls) >= 2:
        test_batch_profiles(sample_urls[:2])
        time.sleep(2)
    
    # Test CSV processing
    csv_file = create_test_csv()
    test_csv_processing(csv_file)
    time.sleep(2)
    
    # Test CSV upload
    test_upload_csv(csv_file)
    time.sleep(2)
    
    # Test cache statistics
    test_cache_stats()
    
    # Clean up test files
    try:
        if os.path.exists(csv_file):
            os.remove(csv_file)
            print(f"\n🗑️  Cleaned up test CSV file: {csv_file}")
    except Exception as e:
        print(f"⚠️  Could not clean up test file: {str(e)}")
    
    print("\n" + "=" * 60)
    print("✅ Test completed!")
    print("\n📝 Usage Examples:")
    print("1. Get single profile:")
    print(f"   curl -X POST {API_ENDPOINTS['single_profile']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"linkedin_url\": \"https://www.linkedin.com/in/example/\"}'")
    
    print("\n2. Process batch profiles:")
    print(f"   curl -X POST {API_ENDPOINTS['batch_profiles']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"linkedin_urls\": [\"https://www.linkedin.com/in/example1/\", \"https://www.linkedin.com/in/example2/\"], \"max_workers\": 3}'")
    
    print("\n3. Process CSV file:")
    print(f"   curl -X POST {API_ENDPOINTS['process_csv']} \\")
    print("        -H 'Content-Type: application/json' \\")
    print("        -d '{\"csv_file_path\": \"linkedin_urls.csv\", \"output_dir\": \"output\"}'")
    
    print("\n4. Upload CSV file:")
    print(f"   curl -X POST {API_ENDPOINTS['upload_csv']} \\")
    print("        -F 'file=@linkedin_urls.csv' \\")
    print("        -F 'output_dir=output'")
    
    print("\n5. Get cache stats:")
    print(f"   curl {API_ENDPOINTS['cache_stats']}")

if __name__ == "__main__":
    main() 