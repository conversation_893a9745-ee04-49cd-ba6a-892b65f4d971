#!/usr/bin/env python3
"""
Test script for SerpAPI endpoints
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1/serpapi"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_basic_search():
    """Test basic LinkedIn profile search"""
    print("🔍 Testing basic search...")
    
    payload = {
        "query": "software engineer python",
        "max_pages": 1,
        "location": "San Francisco"
    }
    
    response = requests.post(f"{BASE_URL}/search", json=payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Query: {data['query']}")
        print(f"Total results: {data['total_results']}")
        print(f"First 3 URLs: {data['linkedin_urls'][:3]}")
    else:
        print(f"Error: {response.text}")
    print()

def test_boolean_search():
    """Test boolean search for LinkedIn profiles"""
    print("🔍 Testing boolean search...")
    
    payload = {
        "job_title": "Senior Software Engineer",
        "skills": ["Python", "JavaScript", "React"],
        "location": "San Francisco, CA",
        "experience_level": "senior",
        "company": "Google",
        "max_pages": 1
    }
    
    response = requests.post(f"{BASE_URL}/boolean-search", json=payload)
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Query: {data['query']}")
        print(f"Total results: {data['total_results']}")
        print(f"First 3 URLs: {data['linkedin_urls'][:3]}")
    else:
        print(f"Error: {response.text}")
    print()

def test_search_history():
    """Test getting search history"""
    print("🔍 Testing search history...")
    response = requests.get(f"{BASE_URL}/search-history")
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Total cached searches: {data['total_cached_searches']}")
        if data['history']:
            print(f"First cache item: {data['history'][0]['cache_key']}")
    else:
        print(f"Error: {response.text}")
    print()

def test_clear_cache():
    """Test clearing the cache"""
    print("🔍 Testing cache clear...")
    response = requests.delete(f"{BASE_URL}/cache")
    print(f"Status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Message: {data['message']}")
        print(f"Cleared count: {data['cleared_count']}")
    else:
        print(f"Error: {response.text}")
    print()

if __name__ == "__main__":
    print("🚀 Testing SerpAPI Endpoints\n")
    
    # Test all endpoints
    test_health_check()
    test_basic_search()
    test_boolean_search()
    test_search_history()
    test_clear_cache()
    
    print("✅ All tests completed!") 